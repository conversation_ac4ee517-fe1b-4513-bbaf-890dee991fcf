#!/usr/bin/env node

/**
 * Test script to verify WebSocket is sending complete fixture data
 * Run this script to see what data is being broadcast via WebSocket
 */

const { io } = require('socket.io-client');

const SOCKET_URL = process.env.SOCKET_URL || 'http://localhost:3000';

console.log('🔌 Connecting to WebSocket at:', SOCKET_URL);

const socket = io(`${SOCKET_URL}/fixtures`, {
  transports: ['websocket', 'polling'],
  timeout: 20000,
});

socket.on('connect', () => {
  console.log('✅ Connected to /fixtures namespace');
  
  // Subscribe to live fixtures
  socket.emit('subscribe-live');
  console.log('📡 Subscribed to live fixtures');
});

socket.on('disconnect', (reason) => {
  console.log('❌ Disconnected:', reason);
});

socket.on('connect_error', (err) => {
  console.error('🚨 Connection error:', err.message);
});

socket.on('subscription-success', (data) => {
  console.log('✅ Subscription successful:', data);
});

socket.on('live-fixtures-update', (fixtures) => {
  console.log('\n🔄 Live fixtures update received!');
  console.log(`📊 Number of fixtures: ${fixtures.length}`);
  
  fixtures.forEach((fixture, index) => {
    console.log(`\n--- Fixture ${index + 1}: ${fixture.teams.home.name} vs ${fixture.teams.away.name} ---`);
    console.log(`ID: ${fixture._id}`);
    console.log(`Status: ${fixture.fixture.status.short} (${fixture.fixture.status.long})`);
    console.log(`Score: ${fixture.goals.home} - ${fixture.goals.away}`);
    console.log(`Elapsed: ${fixture.fixture.status.elapsed || 'N/A'} minutes`);
    
    // Check for detailed data
    console.log('\n📋 Detailed Data Available:');
    console.log(`  Events: ${fixture.events ? fixture.events.length : 0} items`);
    console.log(`  Statistics: ${fixture.statistics ? fixture.statistics.length : 0} teams`);
    console.log(`  Lineups: ${fixture.lineups ? fixture.lineups.length : 0} teams`);
    console.log(`  Players: ${fixture.players ? fixture.players.length : 0} teams`);
    
    // Show sample events if available
    if (fixture.events && fixture.events.length > 0) {
      console.log('\n⚽ Sample Events:');
      fixture.events.slice(0, 3).forEach(event => {
        console.log(`  ${event.time.elapsed}' - ${event.type}: ${event.detail} (${event.team.name})`);
      });
    }
    
    // Show sample statistics if available
    if (fixture.statistics && fixture.statistics.length > 0) {
      console.log('\n📊 Sample Statistics:');
      fixture.statistics.forEach(teamStats => {
        console.log(`  ${teamStats.team.name}:`);
        teamStats.statistics.slice(0, 2).forEach(stat => {
          console.log(`    ${stat.type}: ${stat.value}`);
        });
      });
    }
  });
});

socket.on('fixture-update', (fixture) => {
  console.log('\n🎯 Individual fixture update received!');
  console.log(`Fixture: ${fixture.teams.home.name} vs ${fixture.teams.away.name}`);
  console.log(`Has Events: ${fixture.events ? fixture.events.length : 0}`);
  console.log(`Has Statistics: ${fixture.statistics ? fixture.statistics.length : 0}`);
  console.log(`Has Lineups: ${fixture.lineups ? fixture.lineups.length : 0}`);
  console.log(`Has Players: ${fixture.players ? fixture.players.length : 0}`);
});

// Keep the script running
console.log('\n👂 Listening for WebSocket updates... (Press Ctrl+C to exit)');

process.on('SIGINT', () => {
  console.log('\n👋 Disconnecting...');
  socket.disconnect();
  process.exit(0);
});
