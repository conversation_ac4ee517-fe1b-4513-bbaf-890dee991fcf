<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Standings WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .controls {
            margin-bottom: 20px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input {
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Live Standings WebSocket Test</h1>
        
        <div id="status" class="status disconnected">
            Disconnected
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <input type="number" id="leagueInput" placeholder="League ID (e.g., 39)" value="39">
            <button id="subscribeBtn" onclick="subscribeToLeague()" disabled>Subscribe to League</button>
            <button id="clearBtn" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="log" class="log">Waiting for connection...</div>
    </div>

    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script>
        let socket = null;
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const leagueInput = document.getElementById('leagueInput');

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logEl.textContent += `[${timestamp}] ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
        }

        function updateStatus(connected) {
            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                subscribeBtn.disabled = false;
            } else {
                statusEl.textContent = 'Disconnected';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                subscribeBtn.disabled = true;
            }
        }

        function connect() {
            log('Attempting to connect to WebSocket...');
            
            socket = io('https://api.kickoffpredictions.com/fixtures', {
                transports: ['websocket', 'polling'],
                timeout: 10000,
                forceNew: true
            });

            socket.on('connect', () => {
                log('✅ Connected to WebSocket server');
                updateStatus(true);
            });

            socket.on('disconnect', (reason) => {
                log(`❌ Disconnected: ${reason}`);
                updateStatus(false);
            });

            socket.on('connect_error', (error) => {
                log(`❌ Connection error: ${error.message}`);
                updateStatus(false);
            });

            // Listen for live standings updates
            socket.on('live-standings-update', (data) => {
                log('📊 Live standings update received:');
                log(`   League: ${data.leagueId}, Season: ${data.season}`);
                log(`   Teams affected: ${Object.keys(data.adjustments).length}`);
                log(`   Timestamp: ${data.timestamp}`);
                
                // Log details of adjustments
                Object.entries(data.adjustments).forEach(([teamId, adjustment]) => {
                    if (adjustment.liveMatches && adjustment.liveMatches.length > 0) {
                        log(`   Team ${teamId}: ${adjustment.points > 0 ? '+' : ''}${adjustment.points} pts, ${adjustment.liveMatches.length} live matches`);
                    }
                });
            });

            // Listen for fixture updates
            socket.on('fixture-update', (data) => {
                log(`⚽ Fixture update: ${data.fixtureId} - ${data.goals?.home || 0}-${data.goals?.away || 0}`);
            });

            // Listen for goal updates
            socket.on('fixture-goal-update', (data) => {
                log(`🥅 Goal update: Fixture ${data.fixtureId} - ${data.goals?.home || 0}-${data.goals?.away || 0}`);
            });

            // Listen for subscription confirmations
            socket.on('subscription-success', (data) => {
                log(`✅ Subscription successful: ${data.type} ${data.id}`);
            });

            socket.on('subscription-error', (data) => {
                log(`❌ Subscription error: ${data.message}`);
            });

            // Listen for team form updates
            socket.on('team-form-update', (data) => {
                log(`📈 Team form update: Team ${data.teamId} - ${data.previousForm} → ${data.newForm}`);
            });
        }

        function disconnect() {
            if (socket) {
                log('Disconnecting...');
                socket.disconnect();
                socket = null;
                updateStatus(false);
            }
        }

        function subscribeToLeague() {
            const leagueId = parseInt(leagueInput.value);
            if (socket && leagueId) {
                log(`Subscribing to league ${leagueId}...`);
                socket.emit('subscribe-league', leagueId);
            } else {
                log('❌ Please enter a valid league ID and ensure connection is active');
            }
        }

        function clearLog() {
            logEl.textContent = '';
        }

        // Auto-connect on page load
        window.addEventListener('load', () => {
            log('Page loaded. Click Connect to start testing.');
        });
    </script>
</body>
</html>
