/**
 * Test script for team rating functionality
 * 
 * This script tests:
 * 1. Team rating calculation from player data
 * 2. API endpoint with team ratings
 * 3. WebSocket team rating updates
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3000';
const API_KEY = '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';

// Test fixture ID (use a fixture that has player data)
const TEST_FIXTURE_ID = 1324250; // Live fixture from server logs

async function testTeamRatingsAPI() {
  console.log('🧪 Testing Team Ratings API...\n');

  try {
    // Test 1: Fetch fixture without team ratings
    console.log('📋 Test 1: Fetching fixture without team ratings...');
    const responseWithoutRatings = await axios.get(`${API_BASE_URL}/api/fixtures`, {
      params: { id: TEST_FIXTURE_ID },
      headers: { 'X-API-Key': API_KEY }
    });

    console.log(`✅ Fixture fetched successfully`);
    console.log(`   - Has player data: ${responseWithoutRatings.data.players ? 'Yes' : 'No'}`);
    console.log(`   - Has team ratings: ${responseWithoutRatings.data.teamRatings ? 'Yes' : 'No'}`);
    console.log('');

    // Test 2: Fetch fixture with team ratings
    console.log('📋 Test 2: Fetching fixture with team ratings...');
    const responseWithRatings = await axios.get(`${API_BASE_URL}/api/fixtures`, {
      params: { 
        id: TEST_FIXTURE_ID,
        includeTeamRatings: 'true'
      },
      headers: { 'X-API-Key': API_KEY }
    });

    console.log(`✅ Fixture with team ratings fetched successfully`);
    console.log(`   - Has player data: ${responseWithRatings.data.players ? 'Yes' : 'No'}`);
    console.log(`   - Has team ratings: ${responseWithRatings.data.teamRatings ? 'Yes' : 'No'}`);

    if (responseWithRatings.data.teamRatings) {
      const { home, away } = responseWithRatings.data.teamRatings;
      console.log(`   - Home team rating: ${home.rating} (${home.ratedPlayersCount}/${home.playersCount} players)`);
      console.log(`   - Away team rating: ${away.rating} (${away.ratedPlayersCount}/${away.playersCount} players)`);
      
      // Show top 3 rated players for each team
      if (home.playerRatings.length > 0) {
        console.log(`   - Top home players:`);
        home.playerRatings.slice(0, 3).forEach((player, index) => {
          console.log(`     ${index + 1}. ${player.name}: ${player.rating} (${player.position})`);
        });
      }
      
      if (away.playerRatings.length > 0) {
        console.log(`   - Top away players:`);
        away.playerRatings.slice(0, 3).forEach((player, index) => {
          console.log(`     ${index + 1}. ${player.name}: ${player.rating} (${player.position})`);
        });
      }
    }
    console.log('');

    // Test 3: Performance comparison
    console.log('📋 Test 3: Performance comparison...');
    const startTime1 = Date.now();
    await axios.get(`${API_BASE_URL}/api/fixtures`, {
      params: { id: TEST_FIXTURE_ID },
      headers: { 'X-API-Key': API_KEY }
    });
    const time1 = Date.now() - startTime1;

    const startTime2 = Date.now();
    await axios.get(`${API_BASE_URL}/api/fixtures`, {
      params: { 
        id: TEST_FIXTURE_ID,
        includeTeamRatings: 'true'
      },
      headers: { 'X-API-Key': API_KEY }
    });
    const time2 = Date.now() - startTime2;

    console.log(`✅ Performance comparison:`);
    console.log(`   - Without team ratings: ${time1}ms`);
    console.log(`   - With team ratings: ${time2}ms`);
    console.log(`   - Overhead: ${time2 - time1}ms`);
    console.log('');

    console.log('🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response status:', error.response.status);
      console.error('   Response data:', error.response.data);
    }
  }
}

// Run the test
if (require.main === module) {
  testTeamRatingsAPI();
}

module.exports = { testTeamRatingsAPI };
