# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build output
dist/
build/
*.tsbuildinfo

# Logs
logs
*.log

# OS specific files
.DS_Store
Thumbs.db

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo

# APNs Certificates
certs/*.p8
certs/*.pem
certs/*.key
certs/*.crt

# Keep the README in certs directory
!certs/README.md
