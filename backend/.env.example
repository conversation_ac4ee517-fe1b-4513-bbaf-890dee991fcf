# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/kickoffscore
MONGODB_DB_NAME=kickoffscore

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
# Generate a strong secret with: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
JWT_SECRET=your_strong_jwt_secret_key_here_at_least_64_characters_long
JWT_EXPIRATION=7d

# API-Football Configuration
API_FOOTBALL_KEY=your_api_football_key_here
API_FOOTBALL_HOST=api-football-v1.p.rapidapi.com
API_FOOTBALL_BASE_URL=https://api-football-v1.p.rapidapi.com/v3

# APNs Configuration (Apple Push Notification service)
APNS_KEY_PATH=./certs/AuthKey_XXXXXXXXXX.p8
APNS_KEY_ID=XXXXXXXXXX
APNS_TEAM_ID=XXXXXXXXXX
APNS_BUNDLE_ID=com.yourapp.bundle

# Set to 'true' to mock notifications instead of sending real ones
MOCK_NOTIFICATIONS=true
