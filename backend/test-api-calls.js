const axios = require('axios');

async function testAPICalls() {
  const baseURL = 'https://api.kickoffpredictions.com';
  const headers = {
    'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
  };

  console.log('=== TESTING API CALLS ===\n');

  try {
    // Test 1: Without team ratings
    console.log('1. Testing without team ratings:');
    const response1 = await axios.get(`${baseURL}/api/fixtures?id=1437226`, { headers });
    console.log(`   Status: ${response1.data.fixture.status.short} (${response1.data.fixture.status.long})`);
    console.log(`   Response size: ${JSON.stringify(response1.data).length} characters`);
    console.log(`   Has teamRatings: ${response1.data.teamRatings ? 'YES' : 'NO'}`);

    // Test 2: With team ratings
    console.log('\n2. Testing with team ratings:');
    const response2 = await axios.get(`${baseURL}/api/fixtures?id=1437226&includeTeamRatings=true`, { headers });
    console.log(`   Status: ${response2.data.fixture.status.short} (${response2.data.fixture.status.long})`);
    console.log(`   Response size: ${JSON.stringify(response2.data).length} characters`);
    console.log(`   Has teamRatings: ${response2.data.teamRatings ? 'YES' : 'NO'}`);

    // Test 3: Compare the fixture objects
    console.log('\n3. Comparing fixture objects:');
    const fixture1 = response1.data.fixture;
    const fixture2 = response2.data.fixture;
    
    console.log(`   Timestamp match: ${fixture1.timestamp === fixture2.timestamp}`);
    console.log(`   Date match: ${fixture1.date === fixture2.date}`);
    console.log(`   Venue match: ${JSON.stringify(fixture1.venue) === JSON.stringify(fixture2.venue)}`);
    console.log(`   Referee match: ${JSON.stringify(fixture1.referee) === JSON.stringify(fixture2.referee)}`);

    // Test 4: Compare teams and goals
    console.log('\n4. Comparing teams and goals:');
    console.log(`   Home team match: ${JSON.stringify(response1.data.teams.home) === JSON.stringify(response2.data.teams.home)}`);
    console.log(`   Away team match: ${JSON.stringify(response1.data.teams.away) === JSON.stringify(response2.data.teams.away)}`);
    console.log(`   Goals match: ${JSON.stringify(response1.data.goals) === JSON.stringify(response2.data.goals)}`);

    // Test 5: Check specific differences
    console.log('\n5. Detailed comparison:');
    console.log(`   Response 1 _id: ${response1.data._id}`);
    console.log(`   Response 2 _id: ${response2.data._id}`);
    console.log(`   Response 1 home team: ${response1.data.teams.home.name}`);
    console.log(`   Response 2 home team: ${response2.data.teams.home.name}`);
    console.log(`   Response 1 away team: ${response1.data.teams.away.name}`);
    console.log(`   Response 2 away team: ${response2.data.teams.away.name}`);
    console.log(`   Response 1 goals: ${response1.data.goals.home}-${response1.data.goals.away}`);
    console.log(`   Response 2 goals: ${response2.data.goals.home}-${response2.data.goals.away}`);
    console.log(`   Response 1 league: ${response1.data.league.name}`);
    console.log(`   Response 2 league: ${response2.data.league.name}`);

  } catch (error) {
    console.error('Error:', error.response?.data || error.message);
  }
}

testAPICalls();
