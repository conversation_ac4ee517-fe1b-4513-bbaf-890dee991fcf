const Redis = require('ioredis');

async function testCacheFix() {
  const redisClient = new Redis('redis://localhost:6379');
  
  try {
    console.log('Testing cache fix for fixture 1437226...');
    
    // Check what cache keys exist for this fixture
    const keys = await redisClient.keys('fixtures:id:1437226*');
    console.log('Existing cache keys:', keys);
    
    // Clear both cache variants manually to test
    const keysToDelete = [
      'fixtures:id:1437226',
      'fixtures:id:1437226:with-ratings'
    ];
    
    const result = await redisClient.del(...keysToDelete);
    console.log(`Deleted ${result} cache keys`);
    
    // Check again
    const keysAfter = await redisClient.keys('fixtures:id:1437226*');
    console.log('Cache keys after deletion:', keysAfter);
    
    console.log('Cache cleared successfully! The next API request should fetch fresh data.');
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await redisClient.quit();
  }
}

testCacheFix();
