#!/bin/bash

# KickoffScore Backend Deployment Script
# This script builds and deploys the backend to the remote server

set -e  # Exit on any error

# Configuration
REMOTE_SERVER="**************"
REMOTE_USER="root"
REMOTE_PATH="/var/www/api.kickoffpredictions.com"
LOCAL_BUILD_DIR="./dist"

echo "🚀 Starting KickoffScore Backend Deployment..."
echo "================================================"

# Step 1: Build the project locally
echo "📦 Building the project locally..."
npm run build

if [ ! -d "$LOCAL_BUILD_DIR" ]; then
    echo "❌ Build failed - dist directory not found"
    exit 1
fi

echo "✅ Build completed successfully"

# Step 2: Create deployment package
echo "📋 Creating deployment package..."
tar -czf backend-deploy.tar.gz \
    dist/ \
    package.json \
    package-lock.json \
    .env \
    src/

echo "✅ Deployment package created"

# Step 3: Upload to remote server
echo "🌐 Uploading to remote server..."
scp backend-deploy.tar.gz $REMOTE_USER@$REMOTE_SERVER:/tmp/

# Step 4: Deploy on remote server
echo "🔧 Deploying on remote server..."
ssh $REMOTE_USER@$REMOTE_SERVER << 'EOF'
set -e

echo "🛑 Stopping existing services..."
# Stop only the kickoffscore-api PM2 process
pm2 stop kickoffscore-api || echo "No kickoffscore-api process to stop"

echo "🗂️ Preparing deployment directory..."
mkdir -p /var/www/api.kickoffpredictions.com
cd /var/www/api.kickoffpredictions.com

# Remove old files but keep .env if it exists
if [ -f ".env" ]; then
    cp .env /tmp/.env.backup
fi

# Clear old deployment
rm -rf dist/ src/ node_modules/ package*.json || true

echo "📦 Extracting new deployment..."
tar -xzf /tmp/backend-deploy.tar.gz -C /var/www/api.kickoffpredictions.com/

# Restore .env if it was backed up
if [ -f "/tmp/.env.backup" ]; then
    cp /tmp/.env.backup .env
    rm /tmp/.env.backup
fi

echo "📚 Installing dependencies..."
npm ci --production

echo "🧹 Clearing Redis cache..."
redis-cli FLUSHALL || echo "Redis cache clear failed - continuing anyway"

echo "🔄 Starting services with PM2..."
# Start the application with PM2
pm2 start dist/server.js --name "kickoffscore-api" --env production || pm2 restart kickoffscore-api

# Save PM2 configuration
pm2 save

echo "🧪 Testing API endpoint..."
sleep 5  # Wait for service to start

# Test if the API is responding
CURRENT_DATE=$(date +%Y-%m-%d)
if curl -f -H "X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756" "http://localhost:3000/api/fixtures?date=$CURRENT_DATE" > /dev/null 2>&1; then
    echo "✅ API is responding correctly"
else
    echo "⚠️ API test failed - check logs with: pm2 logs kickoffscore-api"
fi

echo "📊 PM2 Status:"
pm2 status

echo "🧹 Cleaning up..."
rm -f /tmp/backend-deploy.tar.gz

echo "✅ Deployment completed successfully!"
echo "🔗 API should be available at: https://api.kickoffpredictions.com"
echo "📝 Check logs with: pm2 logs kickoffscore-api"
echo "📈 Monitor with: pm2 monit"
EOF

# Step 5: Optional friendly fixtures fetch (only if --fetch-friendlies flag is provided)
if [[ "$1" == "--fetch-friendlies" ]]; then
    echo "🏆 Fetching friendly fixtures on remote server..."
    ssh $REMOTE_USER@$REMOTE_SERVER << 'EOF'
cd /var/www/api.kickoffpredictions.com
echo "🔄 Running friendly fixtures fetch script..."
npx ts-node src/scripts/fetchFriendlyFixtures.ts || echo "⚠️ Friendly fixtures script failed - check logs"
EOF
else
    echo "⏭️ Skipping friendly fixtures fetch (use --fetch-friendlies to include)"
fi

# Step 6: Final verification
echo "🔍 Final verification..."
echo "Testing API endpoints..."

# Test the API endpoints
CURRENT_DATE=$(date +%Y-%m-%d)
echo "Testing current date fixtures ($CURRENT_DATE):"
curl -H "X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756" \
     "https://api.kickoffpredictions.com/api/fixtures?date=$CURRENT_DATE" \
     | jq 'length' || echo "API test failed"

echo "Testing for sample fixtures:"
curl -H "X-API-Key: 1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756" \
     "https://api.kickoffpredictions.com/api/fixtures?date=$CURRENT_DATE" \
     | jq '.[:3] | .[] | {home: .teams.home.name, away: .teams.away.name}' 2>/dev/null || echo "Sample fixtures test completed"

# Cleanup local files
echo "🧹 Cleaning up local files..."
rm -f backend-deploy.tar.gz

echo ""
echo "🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!"
echo "================================================"
echo "✅ Backend deployed to: $REMOTE_SERVER:$REMOTE_PATH"
echo "✅ Cache cleared"
echo "✅ Services restarted"
if [[ "$1" == "--fetch-friendlies" ]]; then
    echo "✅ Friendly fixtures fetched"
fi
echo ""
echo "🔗 Test the API:"
echo "   https://api.kickoffpredictions.com/api/fixtures?date=$(date +%Y-%m-%d)"
echo ""
echo "📝 Useful commands:"
echo "   ssh $REMOTE_USER@$REMOTE_SERVER 'pm2 logs kickoffscore-api'"
echo "   ssh $REMOTE_USER@$REMOTE_SERVER 'pm2 status'"
echo "   ssh $REMOTE_USER@$REMOTE_SERVER 'pm2 monit'"
echo ""
echo "🏆 For new leagues:"
echo "   ./fetch-new-leagues.sh <league_id_1> [league_id_2] ..."
echo "   Example: ./fetch-new-leagues.sh 39 140 78"
