import dotenv from 'dotenv';

dotenv.config(); // Ensure environment variables are loaded

// Validate essential environment variables
const requiredEnvVars = [
  'API_FOOTBALL_KEY',
  'MONGO_URI',
  'REDIS_URI',
  'JWT_SECRET',
];

for (const varName of requiredEnvVars) {
  if (!process.env[varName]) {
    // In a real app, you might throw an error or exit
    console.error(`Error: Environment variable ${varName} is not defined.`);
    // For now, we'll just log and continue, but this indicates a setup issue.
  }
}

const config = {
  server: {
    port: process.env.PORT || '3000', // Default if not set
  },
  apiFootball: {
    apiKey: process.env.API_FOOTBALL_KEY || '', // Provide default or handle missing key
    baseUrl: 'https://v3.football.api-sports.io',
    hostHeader: 'v3.football.api-sports.io',
    mediaBaseUrl: 'https://media.api-sports.io', // Official media CDN
  },
  database: {
    mongoUri: process.env.MONGO_URI || '', // Provide default or handle missing URI
  },
  cache: {
    redisUri: process.env.REDIS_URI || '', // Provide default or handle missing URI
  },
  JWT_SECRET: process.env.JWT_SECRET || 'default_jwt_secret_for_development',
  // Add other configurations as needed (e.g., logging level)
};

export default config;
