import { getDb } from './database';
import { createLeagueCoverageIndexes } from '../models/LeagueCoverage';
import { createStandingUpdatesIndexes } from '../models/StandingUpdate';
import { getFixtureVotesCollection } from '../models/FixtureVote';
import { getMessagesCollection } from '../models/Message';

// Initialize all database indexes
export async function createIndexes(): Promise<void> {
  console.log('Creating database indexes...');

  try {
    // Create indexes for all collections
    await createLeagueCoverageIndexes();
    await createStandingUpdatesIndexes();

    // Create indexes for fixture votes
    console.log('Creating fixture vote indexes...');
    const fixtureVotesCollection = getFixtureVotesCollection();

    // Index for authenticated user votes (unique constraint per category)
    await fixtureVotesCollection.createIndex(
      { userId: 1, fixtureId: 1, category: 1, isGuestVote: 1 },
      {
        unique: true,
        partialFilterExpression: { userId: { $exists: true }, isGuestVote: false }
      }
    );

    // Index for guest votes (unique constraint per category)
    await fixtureVotesCollection.createIndex(
      { guestIdentifier: 1, fixtureId: 1, category: 1, isGuestVote: 1 },
      {
        unique: true,
        partialFilterExpression: { guestIdentifier: { $exists: true }, isGuestVote: true }
      }
    );

    // General index for fixture-based queries
    await fixtureVotesCollection.createIndex({ fixtureId: 1 });

    // Index for fixture and category queries
    await fixtureVotesCollection.createIndex({ fixtureId: 1, category: 1 });

    // Index for user-specific queries
    await fixtureVotesCollection.createIndex({ userId: 1 });

    // Index for guest vote queries
    await fixtureVotesCollection.createIndex({ guestIdentifier: 1 });

    // Create indexes for chat messages
    console.log('Creating message indexes...');
    const messagesCollection = getMessagesCollection();
    await messagesCollection.createIndex({ fixtureId: 1, createdAt: -1 }); // For retrieving messages by fixture
    await messagesCollection.createIndex({ userId: 1 }); // For retrieving messages by user

    // Add more index creation functions here as needed

    console.log('All database indexes created successfully');
  } catch (error) {
    console.error('Error creating database indexes:', error);
  }
}
