/**
 * Targeted bet types for the application
 *
 * This file defines the bet types that we want to fetch and display in the application.
 * Limiting to only the bet types we need improves performance and reduces data usage.
 *
 * Bet types are organized into categories for better organization in the UI.
 */

// Mapping of bet type IDs to their names based on API-Football's actual IDs
export const targetedBetTypeMap: Record<number, string> = {
    // Match Outcome
    1: "Match Winner", // Home, Draw, Away (1X2)
    2: "Home/Away", // Draw No Bet
    12: "Double Chance", // Double Chance
    4: "Asian Handicap", // Asian Handicap
    // Removed: 9: "Handicap Result", // European Handicap

    // Goals
    5: "Goals Over/Under", // Over/Under goals
    8: "Both Teams Score", // Yes/No (BTTS)
    10: "Exact Score", // Exact score predictions
    14: "Team To Score First", // Team to score first
    16: "Total - Home", // Home team total goals
    17: "Total - Away", // Away team total goals

    // Half-Time
    13: "First Half Winner", // Home, Draw, Away in 1st half
    6: "Goals Over/Under First Half", // Goals Over/Under in 1st half
    // Removed: 7: "HT/FT Double", // Half Time/Full Time

    // Events
    45: "Corners Over Under", // Corners over/under
    80: "Cards Over/Under", // Cards over/under

    // Tournament
    61: "To Qualify", // Team to qualify (tournaments)

    // Live Bet Types (Time-based)
    19: "1x2 (1st Half)", // 1X2 for first half
    22: "1x2 - 15 minutes", // 1X2 at 15 minutes
    34: "1x2 - 30 minutes", // 1X2 at 30 minutes
    41: "1x2 - 50 minutes", // 1X2 at 50 minutes
    50: "1x2 - 60 minutes", // 1X2 at 60 minutes
    56: "1x2 - 70 minutes", // 1X2 at 70 minutes
    52: "1x2 - 80 minutes", // 1X2 at 80 minutes
};

// Array of targeted bet type IDs for easier filtering
export const targetedBetTypeIds = Object.keys(targetedBetTypeMap).map(Number);

// Bet type categories for UI organization
export const betTypeCategories = {
    matchOutcome: [1, 2, 12, 4],
    goals: [5, 8, 10, 14, 16, 17],
    halfTime: [13, 6, 19],
    events: [45, 80],
    tournament: [61],
    liveTimeBased: [22, 34, 41, 50, 56, 52]
};
