import express, { Router, RequestHandler } from 'express';
import { query, validationResult } from 'express-validator';
import {
  getTipsterRankings,
  getUserTipsterStats,
  getUserRecentTipResults,
  TipsterRanking
} from '../models/TipResult';
import { auth, optionalAuth, AuthRequest } from '../middleware/auth';
import { findUserById, findUserByUsername, sanitizeUser } from '../models/User';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();

// Cache keys and TTL
const TIPSTER_RANKINGS_CACHE_KEY = 'tipster_rankings:';
const USER_STATS_CACHE_KEY = 'user_stats:';
const CACHE_TTL_MINUTES = 10 * 60; // Cache for 10 minutes

// @route   GET /api/tipsters/ranking
// @desc    Get tipster rankings/leaderboard
// @access  Public
const getTipsterRankingsHandler: RequestHandler = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const sortBy = (req.query.sortBy as 'profit' | 'hitRate' | 'yield') || 'profit';

    if (limit > 100) {
      res.status(400).json({ message: 'Limit cannot exceed 100' });
      return;
    }

    // Check cache first
    const redisClient = getRedisClient();
    const cacheKey = `${TIPSTER_RANKINGS_CACHE_KEY}${sortBy}:${limit}`;
    const cachedRankings = await redisClient.get(cacheKey);

    if (cachedRankings) {
      res.status(200).json(JSON.parse(cachedRankings));
      return;
    }

    // Get rankings from database
    const rankings = await getTipsterRankings(limit, sortBy);

    // Cache the result
    await redisClient.setex(cacheKey, CACHE_TTL_MINUTES, JSON.stringify(rankings));

    res.status(200).json(rankings);
  } catch (err) {
    console.error('Error getting tipster rankings:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/:userId/stats
// @desc    Get specific user's tipster statistics
// @access  Public
const getUserTipsterStatsHandler: RequestHandler = async (req, res) => {
  try {
    const userId = req.params.userId;
    if (!userId) {
      res.status(400).json({ message: 'User ID is required' });
      return;
    }

    // Check cache first
    const redisClient = getRedisClient();
    const cacheKey = `${USER_STATS_CACHE_KEY}${userId}`;
    const cachedStats = await redisClient.get(cacheKey);

    if (cachedStats) {
      res.status(200).json(JSON.parse(cachedStats));
      return;
    }

    // Verify user exists
    const user = await findUserById(userId);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Get user's tipster stats
    const stats = await getUserTipsterStats(userId);
    if (!stats) {
      res.status(404).json({ message: 'No tipster statistics found for this user' });
      return;
    }

    // Prepare response with user info and stats
    const response = {
      user: {
        _id: user._id,
        name: user.name,
        profileImage: user.profileImage
      },
      stats
    };

    // Cache the result
    await redisClient.setex(cacheKey, CACHE_TTL_MINUTES, JSON.stringify(response));

    res.status(200).json(response);
  } catch (err) {
    console.error('Error getting user tipster stats:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/profile/:username
// @desc    Get tipster profile by username
// @access  Public
const getTipsterProfileHandler: RequestHandler = async (req, res) => {
  try {
    const username = req.params.username;
    if (!username) {
      res.status(400).json({ message: 'Username is required' });
      return;
    }

    // Find user by username
    const user = await findUserByUsername(username);
    if (!user) {
      res.status(404).json({ message: 'Tipster not found' });
      return;
    }

    // Get user's tipster stats
    const stats = await getUserTipsterStats(user._id!.toString());
    if (!stats) {
      res.status(404).json({ message: 'No tipster statistics found' });
      return;
    }

    // Get user's recent tip results
    const recentResults = await getUserRecentTipResults(user._id!.toString(), 20);

    // Sanitize user data and combine with stats
    const sanitizedUser = sanitizeUser(user);
    const profile = {
      user: sanitizedUser,
      stats,
      recentResults
    };

    res.status(200).json(profile);
  } catch (err) {
    console.error('Error getting tipster profile:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/:userId/results
// @desc    Get specific user's recent tip results
// @access  Public
const getUserTipResultsHandler: RequestHandler = async (req, res) => {
  try {
    const userId = req.params.userId;
    if (!userId) {
      res.status(400).json({ message: 'User ID is required' });
      return;
    }

    const limit = parseInt(req.query.limit as string) || 20;
    if (limit > 50) {
      res.status(400).json({ message: 'Limit cannot exceed 50' });
      return;
    }

    // Verify user exists
    const user = await findUserById(userId);
    if (!user) {
      res.status(404).json({ message: 'User not found' });
      return;
    }

    // Get user's recent tip results
    const results = await getUserRecentTipResults(userId, limit);

    res.status(200).json({
      user: {
        _id: user._id,
        name: user.name,
        profileImage: user.profileImage
      },
      results
    });
  } catch (err) {
    console.error('Error getting user tip results:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/me/ranking
// @desc    Get current user's ranking position
// @access  Private
const getMyRankingHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Get user's stats
    const userStats = await getUserTipsterStats(authReq.user.id);
    if (!userStats) {
      res.status(404).json({ message: 'No tipster statistics found' });
      return;
    }

    // Get all rankings to find user's position
    const allRankings = await getTipsterRankings(1000, 'profit'); // Get more results to find user
    const userRanking = allRankings.find(ranking => 
      ranking.userId.toString() === authReq.user!.id
    );

    if (!userRanking) {
      res.status(404).json({ message: 'User not found in rankings' });
      return;
    }

    res.status(200).json({
      ranking: userRanking,
      stats: userStats
    });
  } catch (err) {
    console.error('Error getting user ranking:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/leaderboard/summary
// @desc    Get summary of top tipsters for different categories
// @access  Public
const getLeaderboardSummaryHandler: RequestHandler = async (req, res) => {
  try {
    // Check cache first
    const redisClient = getRedisClient();
    const cacheKey = 'tipster_leaderboard_summary';
    const cachedSummary = await redisClient.get(cacheKey);

    if (cachedSummary) {
      res.status(200).json(JSON.parse(cachedSummary));
      return;
    }

    // Get top 5 for each category
    const [topProfit, topHitRate, topYield] = await Promise.all([
      getTipsterRankings(5, 'profit'),
      getTipsterRankings(5, 'hitRate'),
      getTipsterRankings(5, 'yield')
    ]);

    const summary = {
      topProfit,
      topHitRate,
      topYield,
      lastUpdated: new Date()
    };

    // Cache for 15 minutes
    await redisClient.setex(cacheKey, 15 * 60, JSON.stringify(summary));

    res.status(200).json(summary);
  } catch (err) {
    console.error('Error getting leaderboard summary:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tipsters/stats/overview
// @desc    Get overall tipster statistics (total users, tips, etc.)
// @access  Public
const getTipsterOverviewHandler: RequestHandler = async (req, res) => {
  try {
    // Check cache first
    const redisClient = getRedisClient();
    const cacheKey = 'tipster_overview_stats';
    const cachedOverview = await redisClient.get(cacheKey);

    if (cachedOverview) {
      res.status(200).json(JSON.parse(cachedOverview));
      return;
    }

    // Get overview statistics from database
    // This would require aggregation queries on the tipster stats collection
    // For now, we'll return a basic structure
    const overview = {
      totalTipsters: 0,
      totalTips: 0,
      totalProfit: 0,
      averageHitRate: 0,
      activeTipsters: 0, // Tipsters with tips in last 30 days
      lastUpdated: new Date()
    };

    // TODO: Implement actual aggregation queries
    // This is a placeholder for the overview statistics

    // Cache for 30 minutes
    await redisClient.setex(cacheKey, 30 * 60, JSON.stringify(overview));

    res.status(200).json(overview);
  } catch (err) {
    console.error('Error getting tipster overview:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Register routes with validation
router.get(
  '/ranking',
  [
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('sortBy').optional().isIn(['profit', 'hitRate', 'yield']).withMessage('Sort by must be profit, hitRate, or yield')
  ],
  getTipsterRankingsHandler
);

router.get(
  '/:userId/results',
  [
    query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
  ],
  getUserTipResultsHandler
);

// Order matters - more specific routes first
router.get('/me/ranking', auth, getMyRankingHandler);
router.get('/leaderboard/summary', getLeaderboardSummaryHandler);
router.get('/stats/overview', getTipsterOverviewHandler);
router.get('/profile/:username', getTipsterProfileHandler);
router.get('/:userId/stats', getUserTipsterStatsHandler);

export default router;
