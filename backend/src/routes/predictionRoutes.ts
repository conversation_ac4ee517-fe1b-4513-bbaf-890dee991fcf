import express, { Router, RequestHandler } from 'express';
import { getPredictionsCollection, Prediction } from '../models/Prediction';
import { fetchPredictions } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const PREDICTIONS_BASE_CACHE_KEY = 'predictions:';
const CACHE_TTL_SECONDS = 60 * 60; // Cache predictions for 1 hour (API updates hourly)

// Define the handler function with RequestHandler type
const getPredictionHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { fixture } = req.query; // Use fixture to match API-Football parameter name

    // Validate required parameter
    if (!fixture) {
        res.status(400).json({ message: 'Missing required query parameter: fixture' });
        return;
    }

    const fixtureNum = parseInt(fixture as string);

    if (isNaN(fixtureNum)) {
        res.status(400).json({ message: 'Invalid numeric value for fixture' });
        return;
    }

    const cacheKey = `${PREDICTIONS_BASE_CACHE_KEY}${fixtureNum}`;
    const collection = getPredictionsCollection();
    const now = new Date();

    try {
        // 1. Check cache
        const cachedPrediction = await redisClient.get(cacheKey);
        if (cachedPrediction) {
            console.log(`Serving prediction from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedPrediction));
            return;
        }

        // 2. Check DB for prediction data
        const predictionFromDb = await collection.findOne({ _id: fixtureNum });
        if (predictionFromDb) {
            // Check if DB data is recent enough (less than 1 hour old), otherwise re-fetch
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            if (predictionFromDb.lastUpdated > oneHourAgo) {
                console.log(`Serving prediction from DB (ID: ${fixtureNum})`);
                await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(predictionFromDb));
                res.status(200).json(predictionFromDb);
                return;
            } else {
                console.log(`Prediction data for fixture ${fixtureNum} is stale, fetching from API...`);
            }
        }

        // 3. Fetch from API service if not cached (or not in DB/stale)
        console.log(`Fetching prediction from API for fixture ID: ${fixtureNum}`);
        const predictionsFromApi = await fetchPredictions({ fixture: fixtureNum });

        // API returns an array, usually with one prediction object
        if (!predictionsFromApi || predictionsFromApi.length === 0) {
            res.status(404).json({ message: `Prediction not found for fixture ID ${fixtureNum}.` });
            return;
        }

        const predictionData = predictionsFromApi[0];

        // 4. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(predictionData));
        console.log(`Prediction stored in cache (Key: ${cacheKey})`);

        // 5. Store in DB for future use
        const predictionToStore: Prediction = {
            ...predictionData,
            _id: fixtureNum, // Use fixture ID as _id
            lastUpdated: now,
        };
        await collection.updateOne({ _id: fixtureNum }, { $set: predictionToStore }, { upsert: true });
        console.log(`Prediction stored/updated in DB (ID: ${fixtureNum})`);


        res.status(200).json(predictionData); // Return the prediction data from API

    } catch (error: any) {
        console.error(`Error fetching prediction for fixture ID ${fixtureNum}:`, error);
        if (error.response?.status === 404 || error.message?.includes('not found')) {
             res.status(404).json({ message: `Prediction not found for fixture ID ${fixtureNum}.` });
        } else {
             res.status(500).json({ message: 'Failed to fetch prediction' });
        }
    }
};

// GET /api/predictions?fixtureId=X
router.get('/', getPredictionHandler);

export default router;
