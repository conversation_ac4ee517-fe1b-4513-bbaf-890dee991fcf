import express, { Router, RequestHandler } from 'express';
import { checkApiStatus } from '../services/apiFootball';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const STATUS_CACHE_KEY = 'api:status';
const CACHE_TTL_SECONDS = 60 * 5; // Cache for 5 minutes

// Define the handler function with RequestHandler type
const getApiStatusHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();

    try {
        // 1. Try to get from cache first
        const cachedStatus = await redisClient.get(STATUS_CACHE_KEY);
        if (cachedStatus) {
            console.log('Returning cached API status');
            res.status(200).json(JSON.parse(cachedStatus));
            return;
        }

        // 2. Fetch from API if not in cache
        console.log('Fetching API status...');
        const status = await checkApiStatus();

        // 3. Cache the result
        await redisClient.setex(STATUS_CACHE_KEY, CACHE_TTL_SECONDS, JSON.stringify(status));

        // 4. Return the status
        res.status(200).json(status);

    } catch (error) {
        console.error('Error fetching API status:', error);
        res.status(500).json({ message: 'Failed to fetch API status' });
    }
};

// GET /api/status - Fetches API-Football status and usage information
router.get('/', getApiStatusHandler);

export default router;
