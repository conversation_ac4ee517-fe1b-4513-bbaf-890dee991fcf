import express, { Router, RequestHandler } from 'express';
import { getTeamStatisticsCollection, TeamStatistic, createTeamStatisticId } from '../models/TeamStatistic';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();
const STATS_BASE_CACHE_KEY = 'stats:team:';
const CACHE_TTL_SECONDS = 60 * 30; // Cache for 30 minutes (adjust as needed)

// Define the handler function with RequestHandler type
const getTeamStatsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    // Support both parameter naming conventions
    const leagueId = req.query.leagueId || req.query.league;
    const season = req.query.season;
    const teamId = req.query.teamId || req.query.team;
    const date = req.query.date as string | undefined;

    // Validate required parameters
    if (!leagueId || !season || !teamId) {
        res.status(400).json({ message: 'Missing required query parameters: league/leagueId, season, team/teamId' }); // Removed return
        return; // Exit handler
    }

    const leagueNum = parseInt(leagueId as string);
    const seasonNum = parseInt(season as string);
    const teamNum = parseInt(teamId as string);

    if (isNaN(leagueNum) || isNaN(seasonNum) || isNaN(teamNum)) {
        res.status(400).json({ message: 'Invalid numeric value for leagueId, season, or teamId' }); // Removed return
        return; // Exit handler
    }

    const statId = createTeamStatisticId(teamNum, leagueNum, seasonNum);
    const cacheKey = date
        ? `${STATS_BASE_CACHE_KEY}${statId}:date:${date}`
        : `${STATS_BASE_CACHE_KEY}${statId}`;

    try {
        // 1. Check cache
        const cachedStats = await redisClient.get(cacheKey);
        if (cachedStats) {
            console.log(`Serving team stats from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedStats));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching team stats from DB (ID: ${statId})`);
        const collection = getTeamStatisticsCollection();
        const projection = { lastUpdated: 0 }; // Exclude lastUpdated field

        const stats = await collection.findOne({ _id: statId }, { projection });

        if (!stats) {
            // Optionally, trigger an on-demand fetch here if data is missing but expected?
            // Or just return 404 if the job should have populated it.
            res.status(404).json({ message: 'Team statistics not found for the specified league, season, and team.' }); // Removed return
            return; // Exit handler
        }

        // 3. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(stats));
        console.log(`Team stats stored in cache (Key: ${cacheKey})`);

        res.status(200).json(stats); // Removed implicit return

    } catch (error) {
        console.error(`Error fetching team statistics for ID ${statId}:`, error);
        res.status(500).json({ message: 'Failed to fetch team statistics' }); // Removed implicit return
    }
};

// GET /api/teams/statistics?leagueId=X&season=Y&teamId=Z
router.get('/', getTeamStatsHandler);

export default router;
