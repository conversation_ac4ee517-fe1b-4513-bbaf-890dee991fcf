import express, { Request, Response } from 'express';
import { body, param, validationResult } from 'express-validator';
import { auth, AuthRequest } from '../middleware/auth';
import {
  updateNotificationPreference,
  findUserById
} from '../models/User';
import {
  addAnonymousDeviceToken,
  removeAnonymousDeviceToken,
  updateAnonymousNotificationPreferences,
  subscribeAnonymousToFixture,
  unsubscribeAnonymousFromFixture,
  subscribeAnonymousToTeam,
  unsubscribeAnonymousFromTeam,
  checkAnonymousFixtureSubscription
} from '../models/AnonymousDeviceToken';

const router = express.Router();

// --- Authenticated User Routes ---

// @route   PUT /api/notifications/preferences
// @desc    Update notification preferences for authenticated user
// @access  Private
router.put(
  '/preferences',
  auth,
  [
    // Validate specific preferences being updated
    body('upcomingFixtures').optional().isBoolean().withMessage('upcomingFixtures must be a boolean'),
    body('allEvents').optional().isBoolean().withMessage('allEvents must be a boolean'),
    body('goals').optional().isBoolean().withMessage('goals must be a boolean'),
    body('matchStatus').optional().isBoolean().withMessage('matchStatus must be a boolean'),
    body('redCards').optional().isBoolean().withMessage('redCards must be a boolean')
  ],
  async (req: AuthRequest, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      if (!req.user || !req.user.id) {
        res.status(401).json({ message: 'Not authorized' });
        return;
      }

      const preferences = req.body;

      // Update the specific preferences
      await updateNotificationPreference(req.user.id, preferences);

      // Fetch and return the updated user object
      const updatedUser = await findUserById(req.user.id);
      if (!updatedUser) {
        res.status(404).json({ message: 'User not found after update' });
        return;
      }

      res.status(200).json({
        message: 'Notification preferences updated successfully',
        preferences: updatedUser.preferences.notifications
      });
      return;
    } catch (err) {
      console.error('Error updating notification preferences:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// --- Anonymous Device Token Routes ---

// @route   POST /api/notifications/anonymous/device-tokens
// @desc    Register an anonymous device token
// @access  Public
router.post(
  '/anonymous/device-tokens',
  [
    body('token').isString().notEmpty().withMessage('Device token is required and must be a string')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const { token } = req.body;
      const result = await addAnonymousDeviceToken(token);

      res.status(201).json({
        message: 'Anonymous device token registered successfully',
        preferences: result.notificationPreferences
      });
      return;
    } catch (err) {
      console.error('Error registering anonymous device token:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   DELETE /api/notifications/anonymous/device-tokens/:token
// @desc    Remove an anonymous device token
// @access  Public
router.delete(
  '/anonymous/device-tokens/:token',
  [
    param('token').isString().notEmpty().withMessage('Device token is required')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const tokenToRemove = decodeURIComponent(req.params.token);
      await removeAnonymousDeviceToken(tokenToRemove);

      res.status(200).json({
        message: 'Anonymous device token removed successfully'
      });
      return;
    } catch (err) {
      console.error('Error removing anonymous device token:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   PUT /api/notifications/anonymous/preferences/:token
// @desc    Update notification preferences for anonymous device
// @access  Public
router.put(
  '/anonymous/preferences/:token',
  [
    param('token').isString().notEmpty().withMessage('Device token is required'),
    body('allEvents').optional().isBoolean().withMessage('allEvents must be a boolean'),
    body('goals').optional().isBoolean().withMessage('goals must be a boolean'),
    body('matchStatus').optional().isBoolean().withMessage('matchStatus must be a boolean'),
    body('redCards').optional().isBoolean().withMessage('redCards must be a boolean')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const token = decodeURIComponent(req.params.token);
      const preferences = req.body;

      const result = await updateAnonymousNotificationPreferences(token, preferences);

      if (!result) {
        res.status(404).json({ message: 'Device token not found' });
        return;
      }

      res.status(200).json({
        message: 'Notification preferences updated successfully',
        preferences: result.notificationPreferences
      });
      return;
    } catch (err) {
      console.error('Error updating anonymous notification preferences:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   POST /api/notifications/anonymous/subscriptions/:token/fixtures/:fixtureId
// @desc    Subscribe anonymous device to a fixture
// @access  Public
router.post(
  '/anonymous/subscriptions/:token/fixtures/:fixtureId',
  [
    param('token').isString().notEmpty().withMessage('Device token is required'),
    param('fixtureId').isInt().withMessage('Fixture ID must be an integer')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const token = decodeURIComponent(req.params.token);
      const fixtureId = parseInt(req.params.fixtureId);

      await subscribeAnonymousToFixture(token, fixtureId);

      res.status(200).json({
        message: `Subscribed to fixture ${fixtureId} successfully`
      });
      return;
    } catch (err) {
      console.error('Error subscribing to fixture:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   GET /api/notifications/anonymous/subscriptions/:token/fixtures/:fixtureId/status
// @desc    Check if anonymous device is subscribed to a fixture
// @access  Public
router.get(
  '/anonymous/subscriptions/:token/fixtures/:fixtureId/status',
  [
    param('token').isString().notEmpty().withMessage('Device token is required'),
    param('fixtureId').isInt().withMessage('Fixture ID must be an integer')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const token = decodeURIComponent(req.params.token);
      const fixtureId = parseInt(req.params.fixtureId);

      const result = await checkAnonymousFixtureSubscription(token, fixtureId);

      res.status(200).json({
        isSubscribed: result.isSubscribed,
        preferences: result.preferences
      });
      return;
    } catch (err) {
      console.error('Error checking fixture subscription:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   DELETE /api/notifications/anonymous/subscriptions/:token/fixtures/:fixtureId
// @desc    Unsubscribe anonymous device from a fixture
// @access  Public
router.delete(
  '/anonymous/subscriptions/:token/fixtures/:fixtureId',
  [
    param('token').isString().notEmpty().withMessage('Device token is required'),
    param('fixtureId').isInt().withMessage('Fixture ID must be an integer')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const token = decodeURIComponent(req.params.token);
      const fixtureId = parseInt(req.params.fixtureId);

      await unsubscribeAnonymousFromFixture(token, fixtureId);

      res.status(200).json({
        message: `Unsubscribed from fixture ${fixtureId} successfully`
      });
      return;
    } catch (err) {
      console.error('Error unsubscribing from fixture:', err);
      res.status(500).json({ message: 'Server error' });
      return;
    }
  }
);

// @route   POST /api/notifications/test/:token
// @desc    Send a test notification to a device token
// @access  Public
router.post(
  '/test/:token',
  [
    param('token').isString().notEmpty().withMessage('Device token is required')
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const token = decodeURIComponent(req.params.token);

      // Create a test notification
      const title = '🔔 Test Notification';
      const body = 'This is a test notification from KickoffScore. If you can see this, push notifications are working correctly!';

      // Add timestamp to make each notification unique
      const data = {
        type: 'test',
        timestamp: new Date().toISOString()
      };

      // Import the sendPushNotification function
      const { sendPushNotification } = require('../services/notificationService');

      // Send the notification
      console.log(`Sending test notification to token: ${token}`);
      const success = await sendPushNotification(token, title, body, data);

      if (success) {
        console.log(`Test notification sent successfully to token: ${token}`);
        res.status(200).json({
          message: 'Test notification sent successfully',
          details: {
            token,
            title,
            body
          }
        });
      } else {
        console.error(`Failed to send test notification to token: ${token}`);
        res.status(500).json({
          message: 'Failed to send test notification',
          token
        });
      }
    } catch (err) {
      console.error('Error sending test notification:', err);
      res.status(500).json({ message: 'Server error' });
    }
  }
);

// @route   POST /api/notifications/test-direct
// @desc    Send a test notification with a token provided in the request body
// @access  Public
router.post(
  '/test-direct',
  [
    body('token').isString().notEmpty().withMessage('Device token is required'),
    body('title').optional().isString(),
    body('body').optional().isString()
  ],
  async (req: Request, res: Response) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      res.status(400).json({ errors: errors.array() });
      return;
    }

    try {
      const { token } = req.body;

      // Create a test notification
      const title = req.body.title || '🔔 Direct Test Notification';
      const body = req.body.body || 'This is a direct test notification from KickoffScore. If you can see this, push notifications are working correctly!';

      // Add timestamp to make each notification unique
      const data = {
        type: 'test-direct',
        timestamp: new Date().toISOString()
      };

      // Import the sendPushNotification function
      const { sendPushNotification } = require('../services/notificationService');

      // Send the notification
      console.log(`Sending direct test notification to token: ${token}`);
      const success = await sendPushNotification(token, title, body, data);

      if (success) {
        console.log(`Direct test notification sent successfully to token: ${token}`);
        res.status(200).json({
          message: 'Direct test notification sent successfully',
          details: {
            token,
            title,
            body
          }
        });
      } else {
        console.error(`Failed to send direct test notification to token: ${token}`);
        res.status(500).json({
          message: 'Failed to send direct test notification',
          token
        });
      }
    } catch (err) {
      console.error('Error sending direct test notification:', err);
      res.status(500).json({ message: 'Server error' });
    }
  }
);

export default router;
