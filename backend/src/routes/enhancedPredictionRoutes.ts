/**
 * Enhanced Prediction Routes
 * 
 * API endpoints for serving Dixon-Coles enhanced predictions
 * with correct scores and BTTS predictions
 */

import { Router } from 'express';
import { EnhancedPredictionService } from '../services/enhancedPredictionService';
import connectDB from '../config/database';
import { EnhancedPrediction } from '../models/EnhancedPrediction';
import { getRedisClient } from '../config/redis';

const router = Router();

/**
 * GET /api/enhanced-predictions/stats
 * Get statistics about enhanced predictions
 */
router.get('/stats', async (req: any, res: any) => {
  try {
    const db = await connectDB();

    const stats = await db.collection('enhanced_predictions_v2').aggregate([
      {
        $group: {
          _id: null,
          totalPredictions: { $sum: 1 },
          averageConfidence: { $avg: '$metadata.confidence' },
          algorithmsUsed: { $addToSet: '$metadata.algorithm' },
          leaguesCovered: { $addToSet: '$league.id' },
          oldestPrediction: { $min: '$createdAt' },
          newestPrediction: { $max: '$createdAt' }
        }
      }
    ]).toArray();

    const confidenceDistribution = await db.collection('enhanced_predictions_v2').aggregate([
      {
        $bucket: {
          groupBy: '$metadata.confidence',
          boundaries: [0, 20, 40, 60, 80, 100],
          default: 'other',
          output: {
            count: { $sum: 1 }
          }
        }
      }
    ]).toArray();

    res.json({
      success: true,
      data: {
        overview: stats[0] || {
          totalPredictions: 0,
          averageConfidence: 0,
          algorithmsUsed: [],
          leaguesCovered: [],
          oldestPrediction: null,
          newestPrediction: null
        },
        confidenceDistribution
      }
    });

  } catch (error) {
    console.error('Error in enhanced prediction stats endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while retrieving prediction statistics'
    });
  }
});

/**
 * GET /api/enhanced-predictions/batch
 * Get enhanced predictions for multiple fixtures
 */
router.get('/batch', async (req: any, res: any) => {
  try {
    const { fixtures } = req.query;

    if (!fixtures || typeof fixtures !== 'string') {
      return res.status(400).json({
        error: 'Missing fixtures parameter',
        message: 'Please provide a comma-separated list of fixture IDs'
      });
    }

    const fixtureIds = fixtures.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));

    if (fixtureIds.length === 0) {
      return res.status(400).json({
        error: 'Invalid fixture IDs',
        message: 'No valid fixture IDs provided'
      });
    }

    if (fixtureIds.length > 20) {
      return res.status(400).json({
        error: 'Too many fixtures',
        message: 'Maximum 20 fixtures allowed per batch request'
      });
    }

    const db = await connectDB();
    const predictions: any[] = [];
    const errors: any[] = [];

    // Process each fixture
    for (const fixtureId of fixtureIds) {
      try {
        // Check Redis cache first (including failures)
        const redisClient = getRedisClient();
        const cacheKey = `enhanced_prediction:${fixtureId}`;
        const failureCacheKey = `enhanced_prediction_failed:${fixtureId}`;

        const cachedPrediction = await redisClient.get(cacheKey);
        if (cachedPrediction) {
          predictions.push(JSON.parse(cachedPrediction));
          continue;
        }

        // Check if this prediction failed recently
        const cachedFailure = await redisClient.get(failureCacheKey);
        if (cachedFailure) {
          const failureData = JSON.parse(cachedFailure);
          errors.push({
            fixtureId,
            error: 'Prediction generation failed (cached)',
            reason: failureData.reason,
            cachedFailure: true
          });
          continue;
        }

        // Check for existing prediction in database
        let prediction = await db.collection<EnhancedPrediction>('enhanced_predictions_v2')
          .findOne({ _id: fixtureId as any });

        // Generate new prediction only if needed
        if (!prediction || !isRecentPrediction(prediction.lastUpdated)) {
          // Get fixture information
          const fixture = await db.collection('fixtures').findOne({ _id: fixtureId as any });

          if (!fixture) {
            errors.push({
              fixtureId,
              error: 'Fixture not found'
            });
            continue;
          }

          // Generate new prediction
          prediction = await EnhancedPredictionService.generatePrediction(
            fixtureId,
            fixture.teams.home.id,
            fixture.teams.away.id,
            fixture.league.id,
            new Date(fixture.date)
          );

          if (prediction) {
            // Store the prediction in database
            await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
              { _id: fixtureId as any },
              { $set: prediction },
              { upsert: true }
            );

            // Cache successful prediction
            await redisClient.setex(cacheKey, 60 * 60 * 6, JSON.stringify(prediction)); // 6 hours
          } else {
            // Create null prediction instead of caching failure
            const nullPrediction = createNullPrediction(
              fixtureId,
              fixture.teams.home,
              fixture.teams.away,
              fixture.league,
              new Date(fixture.fixture.date)
            );

            // Store null prediction in database
            await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
              { _id: fixtureId as any },
              { $set: nullPrediction },
              { upsert: true }
            );

            // Cache null prediction (shorter TTL)
            await redisClient.setex(cacheKey, 60 * 30, JSON.stringify(nullPrediction)); // 30 minutes
            prediction = nullPrediction;
          }
        }

        if (prediction) {
          predictions.push(prediction);
        }

      } catch (error) {
        errors.push({
          fixtureId,
          error: 'Processing error',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    res.json({
      success: true,
      data: predictions,
      errors: errors.length > 0 ? errors : undefined,
      total: predictions.length,
      requested: fixtureIds.length
    });

  } catch (error) {
    console.error('Error in batch enhanced prediction endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing batch predictions'
    });
  }
});

/**
 * GET /api/enhanced-predictions/:fixtureId
 * Get enhanced prediction for a specific fixture
 */
router.get('/:fixtureId', async (req: any, res: any) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);
    
    if (isNaN(fixtureId)) {
      return res.status(400).json({
        error: 'Invalid fixture ID',
        message: 'Fixture ID must be a valid number'
      });
    }

    // CACHING DISABLED FOR TESTING - Always generate fresh predictions
    const db = await connectDB();

    // Check Redis cache first (including failed predictions)
    const redisClient = getRedisClient();
    const cacheKey = `enhanced_prediction:${fixtureId}`;
    const failureCacheKey = `enhanced_prediction_failed:${fixtureId}`;

    const cachedPrediction = await redisClient.get(cacheKey);
    if (cachedPrediction) {
      return res.json({
        success: true,
        data: JSON.parse(cachedPrediction),
        cached: true,
        source: 'redis'
      });
    }

    // Check if this prediction failed recently (cache failures for 10 minutes)
    const cachedFailure = await redisClient.get(failureCacheKey);
    if (cachedFailure) {
      const failureData = JSON.parse(cachedFailure);
      return res.status(500).json({
        error: 'Prediction generation failed',
        message: 'Unable to generate enhanced prediction for this fixture',
        reason: failureData.reason,
        cached: true,
        failedAt: failureData.timestamp
      });
    }

    // Check database cache
    const existingPrediction = await db.collection<EnhancedPrediction>('enhanced_predictions_v2')
      .findOne({ _id: fixtureId });
    if (existingPrediction && isRecentPrediction(existingPrediction.lastUpdated)) {
      // Cache in Redis for faster future access
      await redisClient.setex(cacheKey, 60 * 60 * 6, JSON.stringify(existingPrediction)); // 6 hours
      return res.json({
        success: true,
        data: existingPrediction,
        cached: true,
        source: 'database'
      });
    }

    // Get fixture information to generate new prediction
    const fixture = await db.collection('fixtures').findOne({ _id: fixtureId as any });
    
    if (!fixture) {
      return res.status(404).json({
        error: 'Fixture not found',
        message: `No fixture found with ID ${fixtureId}`
      });
    }

    // Generate new enhanced prediction
    const enhancedPrediction = await EnhancedPredictionService.generatePrediction(
      fixtureId,
      fixture.teams.home.id,
      fixture.teams.away.id,
      fixture.league.id,
      new Date(fixture.date)
    );

    if (!enhancedPrediction) {
      // Create a null prediction structure instead of returning error
      const nullPrediction = createNullPrediction(
        fixtureId,
        fixture.teams.home,
        fixture.teams.away,
        fixture.league,
        new Date(fixture.date)
      );

      // Store the null prediction in database to avoid repeated calculations
      await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
        { _id: fixtureId },
        { $set: nullPrediction },
        { upsert: true }
      );

      // Cache the null prediction (shorter TTL than successful predictions)
      await redisClient.setex(cacheKey, 60 * 30, JSON.stringify(nullPrediction)); // 30 minutes

      return res.json({
        success: true,
        data: nullPrediction,
        cached: false,
        reason: 'Insufficient data for enhanced prediction'
      });
    }

    // Store the prediction in database
    await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
      { _id: fixtureId },
      { $set: enhancedPrediction },
      { upsert: true }
    );

    // Cache in Redis for faster future access
    await redisClient.setex(cacheKey, 60 * 60 * 6, JSON.stringify(enhancedPrediction)); // 6 hours

    res.json({
      success: true,
      data: enhancedPrediction,
      cached: false,
      generatedAt: enhancedPrediction.lastUpdated
    });

  } catch (error) {
    console.error('Error in enhanced prediction endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while generating the enhanced prediction'
    });
  }
});

/**
 * GET /api/enhanced-predictions/batch
 * Get enhanced predictions for multiple fixtures
 */
router.get('/batch', async (req: any, res: any) => {
  try {
    const { fixtures } = req.query;
    
    if (!fixtures || typeof fixtures !== 'string') {
      return res.status(400).json({
        error: 'Missing fixtures parameter',
        message: 'Please provide a comma-separated list of fixture IDs'
      });
    }

    const fixtureIds = fixtures.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    
    if (fixtureIds.length === 0) {
      return res.status(400).json({
        error: 'Invalid fixture IDs',
        message: 'No valid fixture IDs provided'
      });
    }

    if (fixtureIds.length > 20) {
      return res.status(400).json({
        error: 'Too many fixtures',
        message: 'Maximum 20 fixtures allowed per batch request'
      });
    }

    const db = await connectDB();
    const predictions: any[] = [];
    const errors: any[] = [];

    // Process each fixture
    for (const fixtureId of fixtureIds) {
      try {
        // Get fixture information first
        const fixture = await db.collection('fixtures').findOne({ _id: fixtureId as any });

        if (!fixture) {
          errors.push({
            fixtureId,
            error: 'Fixture not found'
          });
          continue;
        }

        // Check for existing prediction
        let prediction = await db.collection<EnhancedPrediction>('enhanced_predictions_v2')
          .findOne({ _id: fixtureId as any });

        if (!prediction || !isRecentPrediction(prediction.lastUpdated)) {
          // Generate new prediction
          prediction = await EnhancedPredictionService.generatePrediction(
            fixtureId,
            fixture.teams.home.id,
            fixture.teams.away.id,
            fixture.league.id,
            new Date(fixture.date)
          );

          if (prediction) {
            // Store the prediction
            await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
              { _id: fixtureId as any },
              { $set: prediction },
              { upsert: true }
            );
          } else {
            // Create null prediction for insufficient data
            prediction = createNullPrediction(
              fixtureId,
              fixture.teams.home,
              fixture.teams.away,
              fixture.league,
              new Date(fixture.date)
            );

            // Store null prediction
            await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
              { _id: fixtureId as any },
              { $set: prediction },
              { upsert: true }
            );
          }
        }

        predictions.push(prediction);

      } catch (error) {
        errors.push({
          fixtureId,
          error: 'Processing error',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    res.json({
      success: true,
      data: predictions,
      errors: errors.length > 0 ? errors : undefined,
      total: predictions.length,
      requested: fixtureIds.length
    });

  } catch (error) {
    console.error('Error in batch enhanced prediction endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while processing batch predictions'
    });
  }
});

/**
 * GET /api/enhanced-predictions/league/:leagueId
 * Get enhanced predictions for upcoming fixtures in a league
 */
router.get('/league/:leagueId', async (req: any, res: any) => {
  try {
    const leagueId = parseInt(req.params.leagueId);
    const limit = parseInt(req.query.limit as string) || 10;
    const days = parseInt(req.query.days as string) || 7;

    if (isNaN(leagueId)) {
      return res.status(400).json({
        error: 'Invalid league ID',
        message: 'League ID must be a valid number'
      });
    }

    if (limit > 50) {
      return res.status(400).json({
        error: 'Limit too high',
        message: 'Maximum limit is 50 fixtures'
      });
    }

    const db = await connectDB();
    
    // Get upcoming fixtures for the league
    const now = new Date();
    const futureDate = new Date(now.getTime() + (days * 24 * 60 * 60 * 1000));

    const fixtures = await db.collection('fixtures').find({
      'league.id': leagueId,
      'date': { $gte: now, $lte: futureDate },
      'fixture.status.short': { $in: ['NS', 'TBD'] }
    })
    .sort({ date: 1 })
    .limit(limit)
    .toArray();

    const predictions: any[] = [];
    const errors: any[] = [];

    // Generate predictions for each fixture
    for (const fixture of fixtures) {
      try {
        // Check for existing prediction first
        let prediction = await db.collection<EnhancedPrediction>('enhanced_predictions_v2')
          .findOne({ _id: fixture._id as any });

        // Generate new prediction only if needed
        if (!prediction || !isRecentPrediction(prediction.lastUpdated)) {
          // Generate new prediction
          prediction = await EnhancedPredictionService.generatePrediction(
            fixture._id as unknown as number,
            fixture.teams.home.id,
            fixture.teams.away.id,
            fixture.league.id,
            new Date(fixture.date)
          );

          if (prediction) {
            // Store the prediction
            await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
              { _id: fixture._id as any },
              { $set: prediction },
              { upsert: true }
            );
          }
        }

        if (prediction) {
          predictions.push(prediction);
        } else {
          // Create null prediction for insufficient data
          const nullPrediction = createNullPrediction(
            fixture._id as unknown as number,
            fixture.teams.home,
            fixture.teams.away,
            fixture.league,
            new Date(fixture.date)
          );

          // Store null prediction
          await db.collection<EnhancedPrediction>('enhanced_predictions_v2').updateOne(
            { _id: fixture._id as any },
            { $set: nullPrediction },
            { upsert: true }
          );

          predictions.push(nullPrediction);
        }

      } catch (error) {
        errors.push({
          fixtureId: fixture._id,
          error: 'Processing error',
          details: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    res.json({
      success: true,
      data: predictions,
      errors: errors.length > 0 ? errors : undefined,
      league: {
        id: leagueId,
        name: fixtures[0]?.league?.name || 'Unknown League'
      },
      total: predictions.length,
      available: fixtures.length
    });

  } catch (error) {
    console.error('Error in league enhanced prediction endpoint:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: 'An error occurred while generating league predictions'
    });
  }
});

/**
 * Helper function to create a null prediction structure when insufficient data
 */
function createNullPrediction(
  fixtureId: number,
  homeTeam: any,
  awayTeam: any,
  league: any,
  fixtureDate: Date
): EnhancedPrediction {
  return {
    _id: fixtureId,
    fixture: {
      id: fixtureId,
      date: fixtureDate,
      status: 'NS'
    },
    league: {
      id: league.id,
      name: league.name,
      country: league.country,
      logo: league.logo,
      flag: league.flag,
      season: league.season,
      round: league.round,
      standings: league.standings
    },
    teams: {
      home: {
        id: homeTeam.id,
        name: homeTeam.name,
        logo: homeTeam.logo
      },
      away: {
        id: awayTeam.id,
        name: awayTeam.name,
        logo: awayTeam.logo
      }
    },
    predictions: {
      correctScore: {
        mostLikely: { home: 0, away: 0, probability: 0 },
        top5Scores: [],
        matrix: {},
        confidence: 'low' as const,
        algorithm: 'dixon-coles' as const
      },
      bothTeamsToScore: {
        prediction: false,
        probability: 0,
        confidence: 'low' as const
      },
      matchOutcome: {
        homeWin: 0,
        draw: 0,
        awayWin: 0,
        confidence: 'low' as const
      },
      expectedGoals: {
        home: 0,
        away: 0,
        total: 0,
        confidence: 'low' as const
      },
      goalDistribution: {
        under05: 0, under15: 0, under25: 0, under35: 0,
        over05: 0, over15: 0, over25: 0, over35: 0
      }
    },
    dixonColesParams: {
      homeAttack: 0,
      homeDefense: 0,
      awayAttack: 0,
      awayDefense: 0,
      homeAdvantage: 0,
      rho: 0,
      leagueId: league.id,
      calculatedAt: new Date()
    },
    metadata: {
      algorithm: 'dixon-coles' as const,
      dataSource: 'historical' as const,
      confidence: 0,
      processingTime: 0,
      lastUpdated: new Date(),
      modelVersion: '2.0',
      matchesUsedForTraining: 0,
      eloData: null
    },
    createdAt: new Date(),
    lastUpdated: new Date()
  };
}

/**
 * Helper function to check if prediction is recent (within 6 hours)
 * NOTE: Currently disabled for testing - all predictions are regenerated
 */
function isRecentPrediction(lastUpdated: Date): boolean {
  // CACHING DISABLED FOR TESTING - Always return false to force regeneration
  return false; // was: lastUpdated > sixHoursAgo

  // const sixHoursAgo = new Date(Date.now() - (6 * 60 * 60 * 1000));
  // return lastUpdated > sixHoursAgo;
}

export default router;
