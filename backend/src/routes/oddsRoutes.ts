import express, { Router, RequestHandler } from 'express';
import { getOddsCollection, getLiveOddsCollection, getBookmakersCollection, getBetsCollection } from '../models/Odds';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';
import { targetedBetTypeIds } from '../config/targetedBetTypes';
import { getBestOdds, getOddsComparison } from '../services/bestOddsService';
import { TipType, TipDetails } from '../models/Tip';
import { betValueToTipDetails } from '../utils/betTypeMapping';

const router: Router = express.Router();
const ODDS_BASE_CACHE_KEY = 'odds:';
const LIVE_ODDS_BASE_CACHE_KEY = 'odds:live:';
const BOOKMAKERS_CACHE_KEY = 'odds:bookmakers';
const BETS_CACHE_KEY = 'odds:bets';
const LIVE_BETS_CACHE_KEY = 'odds:live:bets';
const CACHE_TTL_SECONDS = 60 * 60; // Cache for 1 hour (adjust as needed)
const LIVE_CACHE_TTL_SECONDS = 60; // Cache live odds for 1 minute

// GET /api/odds?fixture=X&league=Y&season=Z&date=YYYY-MM-DD&bookmaker=B&bet=T&targeted=true
const getOddsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { fixture, league, season, date, bookmaker, bet, targeted } = req.query;
    const useTargetedOnly = targeted === 'true';

    // Validate parameters
    if (!fixture && !league && !date) {
        res.status(400).json({ message: 'At least one parameter (fixture, league, or date) is required' });
        return;
    }

    if (league && !season) {
        res.status(400).json({ message: 'Parameter "season" is required when using "league"' });
        return;
    }

    // Parse numeric parameters
    const fixtureId = fixture ? parseInt(fixture as string) : undefined;
    const leagueId = league ? parseInt(league as string) : undefined;
    const seasonYear = season ? parseInt(season as string) : undefined;
    const bookmakerId = bookmaker ? parseInt(bookmaker as string) : undefined;
    const betId = bet ? parseInt(bet as string) : undefined;

    // Validate numeric parameters
    if ((fixture && isNaN(fixtureId as number)) ||
        (league && isNaN(leagueId as number)) ||
        (season && isNaN(seasonYear as number)) ||
        (bookmaker && isNaN(bookmakerId as number)) ||
        (bet && isNaN(betId as number))) {
        res.status(400).json({ message: 'Invalid numeric value for fixture, league, season, bookmaker, or bet' });
        return;
    }

    // Create cache key based on query parameters
    const cacheKey = `${ODDS_BASE_CACHE_KEY}${fixtureId || ''}_${leagueId || ''}_${seasonYear || ''}_${date || ''}_${bookmakerId || ''}_${betId || ''}_${useTargetedOnly ? 'targeted' : ''}`;

    try {
        // 1. Check cache
        const cachedOdds = await redisClient.get(cacheKey);
        if (cachedOdds) {
            console.log(`Serving odds from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedOdds));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching odds from DB for fixture ${fixtureId}`);
        const collection = getOddsCollection();

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (fixtureId) filter.fixtureId = fixtureId;
        if (leagueId) filter.leagueId = leagueId;
        if (date) filter['fixture.date'] = { $regex: `^${date}` }; // Match date prefix

        // Bookmaker and bet filters
        if (bookmakerId) filter['bookmakers.id'] = bookmakerId;
        if (betId) filter['bookmakers.bets.id'] = betId;
        
        let oddsData;
        
        // Use optimized aggregation pipeline for fixture-specific requests
        if (fixtureId) {
            // This optimized pipeline will group all data for a specific fixture
            const pipeline = [
                { $match: { fixtureId: fixtureId } },
                {
                    $group: {
                        _id: '$fixtureId',
                        leagueId: { $first: '$leagueId' },
                        update: { $first: '$update' },
                        lastUpdated: { $first: '$lastUpdated' },
                        bookmakers: { $push: '$bookmakers' }
                    }
                },
                {
                    $project: {
                        _id: 1,
                        fixtureId: '$_id',
                        leagueId: 1,
                        update: 1,
                        lastUpdated: 1,
                        // Flatten the array of bookmakers arrays
                        bookmakers: {
                            $reduce: {
                                input: '$bookmakers',
                                initialValue: [],
                                in: { $concatArrays: ['$$value', '$$this'] }
                            }
                        }
                    }
                }
            ];

            // Execute the pipeline
            oddsData = await collection.aggregate(pipeline).toArray();
            
            // Process the result to merge duplicate bookmakers
            if (oddsData.length > 0) {
                const mergedResult = oddsData[0];
                
                // Group bookmakers by ID to merge duplicates
                const bookmakerMap = new Map();
                for (const bookmaker of mergedResult.bookmakers) {
                    const key = bookmaker.id;
                    if (!bookmakerMap.has(key)) {
                        bookmakerMap.set(key, {...bookmaker}); 
                    } else {
                        // Merge bets from this bookmaker instance with existing
                        const existingBookmaker = bookmakerMap.get(key);
                        const existingBetIds = new Set(existingBookmaker.bets.map(bet => bet.id));
                        
                        for (const bet of bookmaker.bets) {
                            if (!existingBetIds.has(bet.id)) {
                                existingBookmaker.bets.push(bet);
                                existingBetIds.add(bet.id);
                            }
                        }
                    }
                }
                
                // Convert back to array
                mergedResult.bookmakers = Array.from(bookmakerMap.values());
                
                // Filter to targeted bet types if requested
                if (useTargetedOnly) {
                    mergedResult.bookmakers.forEach(bookmaker => {
                        bookmaker.bets = bookmaker.bets.filter(bet => 
                            targetedBetTypeIds.includes(bet.id)
                        );
                    });
                    
                    // Remove bookmakers with no targeted bets
                    mergedResult.bookmakers = mergedResult.bookmakers.filter(
                        bookmaker => bookmaker.bets.length > 0
                    );
                }
                
                oddsData = [mergedResult];
            }
        } else {
            // For non-fixture specific queries, use the traditional find method
            oddsData = await collection.find(filter).toArray();
        }

        if (!oddsData || oddsData.length === 0) {
            res.status(404).json({ message: 'No odds found for the specified parameters' });
            return;
        }

        // 3. Store in cache (using a longer TTL for fixture-specific data)
        const ttl = fixtureId ? CACHE_TTL_SECONDS * 4 : CACHE_TTL_SECONDS; // 4 hours for fixture-specific data
        await redisClient.setex(cacheKey, ttl, JSON.stringify(oddsData));
        console.log(`Odds stored in cache (Key: ${cacheKey}, TTL: ${ttl}s)`);

        res.status(200).json(oddsData);

    } catch (error) {
        console.error(`Error fetching odds:`, error);
        res.status(500).json({ message: 'Failed to fetch odds' });
    }
};

// GET /api/odds/live?fixture=X&league=Y&bet=Z
const getLiveOddsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { fixture, league, bet } = req.query;

    // Parse numeric parameters
    const fixtureId = fixture ? parseInt(fixture as string) : undefined;
    const leagueId = league ? parseInt(league as string) : undefined;
    const betId = bet ? parseInt(bet as string) : undefined;

    // Validate numeric parameters
    if ((fixture && isNaN(fixtureId as number)) ||
        (league && isNaN(leagueId as number)) ||
        (bet && isNaN(betId as number))) {
        res.status(400).json({ message: 'Invalid numeric value for fixture, league, or bet' });
        return;
    }

    // Create cache key based on query parameters
    const cacheKey = `${LIVE_ODDS_BASE_CACHE_KEY}${fixtureId || ''}_${leagueId || ''}_${betId || ''}`;

    try {
        // 1. Check cache
        const cachedOdds = await redisClient.get(cacheKey);
        if (cachedOdds) {
            console.log(`Serving live odds from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedOdds));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching live odds from DB`);
        const collection = getLiveOddsCollection();

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (fixtureId) filter.fixtureId = fixtureId;
        if (leagueId) filter.leagueId = leagueId;
        if (betId) filter['bookmakers.bets.id'] = betId;

        const odds = await collection.find(filter).toArray();

        if (!odds || odds.length === 0) {
            res.status(404).json({ message: 'No live odds found for the specified parameters' });
            return;
        }

        // 3. Store in cache (short TTL for live odds)
        await redisClient.setex(cacheKey, LIVE_CACHE_TTL_SECONDS, JSON.stringify(odds));
        console.log(`Live odds stored in cache (Key: ${cacheKey})`);

        res.status(200).json(odds);

    } catch (error) {
        console.error(`Error fetching live odds:`, error);
        res.status(500).json({ message: 'Failed to fetch live odds' });
    }
};

// GET /api/odds/bookmakers?id=X&search=Y
const getBookmakersHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { id, search } = req.query;

    // Parse numeric parameters
    const bookmakerId = id ? parseInt(id as string) : undefined;

    // Validate numeric parameters
    if (id && isNaN(bookmakerId as number)) {
        res.status(400).json({ message: 'Invalid numeric value for id' });
        return;
    }

    // Validate search parameter
    if (search && (search as string).length < 3) {
        res.status(400).json({ message: 'Search parameter must be at least 3 characters' });
        return;
    }

    // Create cache key based on query parameters
    const cacheKey = `${BOOKMAKERS_CACHE_KEY}_${bookmakerId || ''}_${search || ''}`;

    try {
        // 1. Check cache
        const cachedBookmakers = await redisClient.get(cacheKey);
        if (cachedBookmakers) {
            console.log(`Serving bookmakers from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedBookmakers));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching bookmakers from DB`);
        const collection = getBookmakersCollection();

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (bookmakerId) filter._id = bookmakerId;
        if (search) filter.name = { $regex: search, $options: 'i' };

        const bookmakers = await collection.find(filter).toArray();

        if (!bookmakers || bookmakers.length === 0) {
            res.status(404).json({ message: 'No bookmakers found for the specified parameters' });
            return;
        }

        // 3. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(bookmakers));
        console.log(`Bookmakers stored in cache (Key: ${cacheKey})`);

        res.status(200).json(bookmakers);

    } catch (error) {
        console.error(`Error fetching bookmakers:`, error);
        res.status(500).json({ message: 'Failed to fetch bookmakers' });
    }
};

// GET /api/odds/bets?id=X&search=Y
const getBetsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { id, search } = req.query;

    // Parse numeric parameters
    const betId = id ? parseInt(id as string) : undefined;

    // Validate numeric parameters
    if (id && isNaN(betId as number)) {
        res.status(400).json({ message: 'Invalid numeric value for id' });
        return;
    }

    // Validate search parameter
    if (search && (search as string).length < 3) {
        res.status(400).json({ message: 'Search parameter must be at least 3 characters' });
        return;
    }

    // Create cache key based on query parameters
    const cacheKey = `${BETS_CACHE_KEY}_${betId || ''}_${search || ''}`;

    try {
        // 1. Check cache
        const cachedBets = await redisClient.get(cacheKey);
        if (cachedBets) {
            console.log(`Serving bets from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedBets));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching bets from DB`);
        const collection = getBetsCollection();

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (betId) {
            filter._id = betId;
        } else {
            // If no specific bet ID is requested, only return targeted bet types
            filter._id = { $in: targetedBetTypeIds };
        }
        if (search) filter.name = { $regex: search, $options: 'i' };

        const bets = await collection.find(filter).toArray();

        if (!bets || bets.length === 0) {
            res.status(404).json({ message: 'No bets found for the specified parameters' });
            return;
        }

        // 3. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(bets));
        console.log(`Bets stored in cache (Key: ${cacheKey})`);

        res.status(200).json(bets);

    } catch (error) {
        console.error(`Error fetching bets:`, error);
        res.status(500).json({ message: 'Failed to fetch bets' });
    }
};

// GET /api/odds/live/bets?id=X&search=Y
const getLiveBetsHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { id, search } = req.query;

    // Parse numeric parameters
    const betId = id ? parseInt(id as string) : undefined;

    // Validate numeric parameters
    if (id && isNaN(betId as number)) {
        res.status(400).json({ message: 'Invalid numeric value for id' });
        return;
    }

    // Validate search parameter
    if (search && (search as string).length < 3) {
        res.status(400).json({ message: 'Search parameter must be at least 3 characters' });
        return;
    }

    // Create cache key based on query parameters
    const cacheKey = `${LIVE_BETS_CACHE_KEY}_${betId || ''}_${search || ''}`;

    try {
        // 1. Check cache
        const cachedBets = await redisClient.get(cacheKey);
        if (cachedBets) {
            console.log(`Serving live bets from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedBets));
            return;
        }

        // 2. Fetch from DB
        console.log(`Fetching live bets from DB`);
        const collection = getBetsCollection();

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (betId) {
            filter._id = betId;
        } else {
            // If no specific bet ID is requested, only return targeted bet types
            filter._id = { $in: targetedBetTypeIds };
        }
        if (search) filter.name = { $regex: search, $options: 'i' };
        filter.isLive = true; // Only get live bets

        const bets = await collection.find(filter).toArray();

        if (!bets || bets.length === 0) {
            res.status(404).json({ message: 'No live bets found for the specified parameters' });
            return;
        }

        // 3. Store in cache
        await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(bets));
        console.log(`Live bets stored in cache (Key: ${cacheKey})`);

        res.status(200).json(bets);

    } catch (error) {
        console.error(`Error fetching live bets:`, error);
        res.status(500).json({ message: 'Failed to fetch live bets' });
    }
};

// GET /api/odds/best?fixture=X&tipType=Y&selection=Z&live=true (Legacy)
// POST /api/odds/best with { fixture, tipType, details, live } (New)
const getBestOddsHandler: RequestHandler = async (req, res) => {
    let fixtureId: number;
    let tipType: TipType;
    let tipDetails: TipDetails;
    let useLiveOdds: boolean;

    if (req.method === 'GET') {
        // Legacy GET method
        const { fixture, tipType: tipTypeParam, selection, live } = req.query;

        // Validate required parameters
        if (!fixture || !tipTypeParam || !selection) {
            res.status(400).json({
                message: 'Parameters "fixture", "tipType", and "selection" are required'
            });
            return;
        }

        // Parse parameters
        fixtureId = parseInt(fixture as string);
        tipType = tipTypeParam as TipType;
        useLiveOdds = live === 'true';

        // Validate fixture ID
        if (isNaN(fixtureId)) {
            res.status(400).json({ message: 'Invalid fixture ID' });
            return;
        }

        // Validate tip type
        if (!Object.values(TipType).includes(tipType)) {
            res.status(400).json({ message: 'Invalid tip type' });
            return;
        }

        // Convert selection back to tip details
        tipDetails = betValueToTipDetails(tipType, selection as string);

        if (Object.keys(tipDetails).length === 0) {
            res.status(400).json({ message: 'Invalid selection for the specified tip type' });
            return;
        }
    } else if (req.method === 'POST') {
        // New POST method
        const { fixture, tipType: tipTypeParam, details, live } = req.body;

        // Validate required parameters
        if (!fixture || !tipTypeParam || !details) {
            res.status(400).json({
                message: 'Parameters "fixture", "tipType", and "details" are required'
            });
            return;
        }

        // Parse parameters
        fixtureId = parseInt(fixture);
        tipType = tipTypeParam as TipType;
        tipDetails = details as TipDetails;
        useLiveOdds = Boolean(live);

        // Validate fixture ID
        if (isNaN(fixtureId)) {
            res.status(400).json({ message: 'Invalid fixture ID' });
            return;
        }

        // Validate tip type
        if (!Object.values(TipType).includes(tipType)) {
            res.status(400).json({ message: 'Invalid tip type' });
            return;
        }
    } else {
        res.status(405).json({ message: 'Method not allowed' });
        return;
    }

    try {
        // Get best odds
        const result = await getBestOdds(fixtureId, tipType, tipDetails, useLiveOdds);

        res.status(200).json(result);

    } catch (error) {
        console.error('Error in getBestOddsHandler:', error);
        res.status(500).json({
            message: 'Failed to fetch best odds',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// GET /api/odds/comparison?fixture=X&tipType=Y&live=true
const getOddsComparisonHandler: RequestHandler = async (req, res) => {
    const { fixture, tipType, live } = req.query;

    // Validate required parameters
    if (!fixture || !tipType) {
        res.status(400).json({
            message: 'Parameters "fixture" and "tipType" are required'
        });
        return;
    }

    // Parse parameters
    const fixtureId = parseInt(fixture as string);
    const useLiveOdds = live === 'true';

    // Validate fixture ID
    if (isNaN(fixtureId)) {
        res.status(400).json({ message: 'Invalid fixture ID' });
        return;
    }

    // Validate tip type
    if (!Object.values(TipType).includes(tipType as TipType)) {
        res.status(400).json({ message: 'Invalid tip type' });
        return;
    }

    try {
        // Get odds comparison for all selections
        const result = await getOddsComparison(fixtureId, tipType as TipType, useLiveOdds);

        res.status(200).json(result);

    } catch (error) {
        console.error('Error in getOddsComparisonHandler:', error);
        res.status(500).json({
            message: 'Failed to fetch odds comparison',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
};

// Define routes
router.get('/', getOddsHandler);
router.get('/live', getLiveOddsHandler);
router.get('/best', getBestOddsHandler);
router.post('/best', getBestOddsHandler); // Support both GET and POST for best odds
router.get('/comparison', getOddsComparisonHandler);
router.get('/bookmakers', getBookmakersHandler);
router.get('/bets', getBetsHandler);
router.get('/live/bets', getLiveBetsHandler);

export default router;
