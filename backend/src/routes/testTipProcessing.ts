import { Router, RequestHandler } from 'express';
import { processFinishedFixtureTips, processAllPendingTips, getTipProcessingStats } from '../jobs/tipProcessingJobs';
import { TipResultService } from '../services/tipResultService';
import { getTipsterStatsCollection } from '../models/TipResult';

const router = Router();

/**
 * Test endpoint to manually trigger tip processing for a specific fixture
 * GET /api/test/process-tips/:fixtureId
 */
const processFixtureTipsHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);

    if (isNaN(fixtureId)) {
      res.status(400).json({ error: 'Invalid fixture ID' });
      return;
    }

    console.log(`🧪 Manual tip processing triggered for fixture ${fixtureId}`);

    // Process tips for this specific fixture
    await processFinishedFixtureTips([fixtureId]);

    res.json({
      success: true,
      message: `Tip processing completed for fixture ${fixtureId}`,
      fixtureId
    });
  } catch (error) {
    console.error('Error in manual tip processing:', error);
    res.status(500).json({
      error: 'Failed to process tips',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

router.get('/process-tips/:fixtureId', processFixtureTipsHandler);

/**
 * Test endpoint to process all pending tips
 * GET /api/test/process-all-pending-tips
 */
const processAllPendingTipsHandler: RequestHandler = async (req, res) => {
  try {
    console.log('🧪 Manual processing of all pending tips triggered');

    const statsBefore = await getTipProcessingStats();
    await processAllPendingTips();
    const statsAfter = await getTipProcessingStats();

    res.json({
      success: true,
      message: 'All pending tips processed',
      statsBefore,
      statsAfter
    });
  } catch (error) {
    console.error('Error processing all pending tips:', error);
    res.status(500).json({
      error: 'Failed to process all pending tips',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

router.get('/process-all-pending-tips', processAllPendingTipsHandler);

/**
 * Test endpoint to get tip processing statistics
 * GET /api/test/tip-stats
 */
const getTipStatsHandler: RequestHandler = async (req, res) => {
  try {
    const stats = await getTipProcessingStats();
    res.json(stats);
  } catch (error) {
    console.error('Error getting tip stats:', error);
    res.status(500).json({
      error: 'Failed to get tip stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

router.get('/tip-stats', getTipStatsHandler);

/**
 * Test endpoint to directly process tips using TipResultService
 * GET /api/test/direct-process/:fixtureId
 */
const directProcessTipsHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);

    if (isNaN(fixtureId)) {
      res.status(400).json({ error: 'Invalid fixture ID' });
      return;
    }

    console.log(`🧪 Direct tip processing triggered for fixture ${fixtureId}`);

    // Use TipResultService directly
    const result = await TipResultService.processFixtureTips(fixtureId);

    res.json({
      success: true,
      message: `Direct tip processing completed for fixture ${fixtureId}`,
      fixtureId,
      result
    });
  } catch (error) {
    console.error('Error in direct tip processing:', error);
    res.status(500).json({
      error: 'Failed to process tips directly',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

router.get('/direct-process/:fixtureId', directProcessTipsHandler);

/**
 * Test endpoint to get all tipster stats (ignoring minimum requirements)
 * GET /api/test/all-tipster-stats
 */
const getAllTipsterStatsHandler: RequestHandler = async (req, res) => {
  try {
    const collection = getTipsterStatsCollection();

    // Get all tipster stats without minimum requirements
    const pipeline = [
      {
        $lookup: {
          from: 'users',
          localField: 'userId',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          userId: 1,
          userName: '$user.name',
          profileImage: '$user.profileImage',
          totalTips: 1,
          wonTips: 1,
          lostTips: 1,
          voidTips: 1,
          pushTips: 1,
          netProfit: 1,
          hitRate: 1,
          yield: 1,
          averageOdds: 1,
          last30DaysProfit: 1,
          last30DaysHitRate: 1,
          currentStreak: 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      {
        $sort: { netProfit: -1 }
      }
    ];

    const results = await collection.aggregate(pipeline).toArray();

    res.json({
      success: true,
      message: `Found ${results.length} tipster stats`,
      tipsters: results
    });
  } catch (error) {
    console.error('Error getting all tipster stats:', error);
    res.status(500).json({
      error: 'Failed to get tipster stats',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

router.get('/all-tipster-stats', getAllTipsterStatsHandler);

export default router;
