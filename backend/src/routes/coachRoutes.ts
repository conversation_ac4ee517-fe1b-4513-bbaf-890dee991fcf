import express, { Router, RequestHandler } from 'express';
import { getCoachesCollection } from '../models/Coach';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb';

const router: Router = express.Router();
const COACHES_CACHE_KEY = 'coaches:';

// Cache durations
const CACHE_TTL_HOUR = 60 * 60;
const CACHE_TTL_DAY = 60 * 60 * 24;
const CACHE_TTL_WEEK = CACHE_TTL_DAY * 7;

// GET /api/coaches - Get coaches
const getCoachesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const { id, team, search } = req.query;

    // Parse and validate parameters
    const coachId = id ? parseInt(id as string) : undefined;
    const teamId = team ? parseInt(team as string) : undefined;
    const searchTerm = search as string | undefined;

    // Validate at least one parameter is provided
    if (!coachId && !teamId && !searchTerm) {
        res.status(400).json({ message: 'At least one parameter (id, team, or search) is required.' });
        return;
    }

    // Validate parameter types
    if (coachId && isNaN(coachId)) {
        res.status(400).json({ message: 'Invalid coach ID.' });
        return;
    }

    if (teamId && isNaN(teamId)) {
        res.status(400).json({ message: 'Invalid team ID.' });
        return;
    }

    if (searchTerm && searchTerm.length < 3) {
        res.status(400).json({ message: 'Search term must be at least 3 characters.' });
        return;
    }

    // Build cache key
    let cacheKey = `${COACHES_CACHE_KEY}`;
    if (coachId) cacheKey += `id:${coachId}`;
    else if (teamId) cacheKey += `team:${teamId}`;
    else if (searchTerm) cacheKey += `search:${searchTerm}`;

    try {
        // Check cache
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving coaches from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build MongoDB filter
        const filter: Filter<any> = {};
        if (coachId) filter._id = coachId;
        if (teamId) filter['career.team.id'] = teamId;
        if (searchTerm) filter['profile.name'] = { $regex: searchTerm, $options: 'i' };

        console.log('Coaches filter:', filter);

        // Fetch from database
        const collection = getCoachesCollection();
        const coaches = await collection.find(filter).toArray();

        console.log(`Found ${coaches.length} coaches matching filter`);

        // Transform to match API response format
        const response = coaches.map(coach => ({
            ...coach.profile,
            team: coach.career.length > 0 ? coach.career[0].team : null,
            career: coach.career
        }));

        // Cache the result
        const cacheTTL = coachId ? CACHE_TTL_WEEK : CACHE_TTL_DAY;
        await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(response));

        if (coachId && coaches.length === 0) {
            res.status(404).json({ message: 'Coach not found' });
            return;
        }

        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching coaches:', error);
        res.status(500).json({ message: 'Failed to fetch coaches' });
    }
};

// Register routes
router.get('/', getCoachesHandler);

export default router;
