import express, { Router, RequestHandler } from 'express';
import { body, validationResult } from 'express-validator';
import {
  createOrUpdateVote,
  createOrUpdateGuestVote,
  generateGuestIdentifier,
  getUserVote,
  getUserVotesForFixture,
  getFixtureVoteStats,
  getFixtureVoteStatsByCategory,
  getMultipleFixtureVoteStats,
  getUserVotes,
  getVoteCount,
  getVoteDates,
  VoteOption,
  VoteCategory,
  isValidVoteForCategory,
  getValidVotesForCategory
} from '../models/FixtureVote';
import { auth, optionalAuth, AuthRequest } from '../middleware/auth';
import { getFixturesCollection } from '../models/Fixture';
import { getRedisClient } from '../config/redis';

const router: Router = express.Router();

// Cache keys and TTL
const FIXTURE_VOTES_BASE_CACHE_KEY = 'fixture_votes:';
const FIXTURE_VOTE_STATS_CACHE_KEY = `${FIXTURE_VOTES_BASE_CACHE_KEY}stats:`;
const CACHE_TTL_MINUTES = 5 * 60; // Cache for 5 minutes

// @route   POST /api/fixtures/votes/:fixtureId
// @desc    Vote for a fixture outcome
// @access  Public (works for both authenticated users and guests)
const createVoteHandler: RequestHandler = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  try {
    const authReq = req as AuthRequest;

    const fixtureId = parseInt(req.params.fixtureId);
    if (isNaN(fixtureId)) {
      res.status(400).json({ message: 'Invalid fixture ID' });
      return;
    }

    // Check if fixture exists
    const fixtureCollection = getFixturesCollection();
    const fixture = await fixtureCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      res.status(404).json({ message: 'Fixture not found' });
      return;
    }

    // Check if fixture is still eligible for voting
    // Allow voting until 70 minutes into the match
    const fixtureDate = new Date(fixture.fixture.date);
    const currentDate = new Date();

    // Check if the match has started
    if (fixtureDate < currentDate) {
      // If match has started, check if it's still within the 70-minute window
      const status = fixture.fixture.status;

      // If the match is finished or beyond 70 minutes, don't allow voting
      if (!status ||
          status.short === 'FT' ||
          status.short === 'AET' ||
          status.short === 'PEN' ||
          (status.elapsed && status.elapsed > 70)) {
        res.status(400).json({ message: 'Voting is closed for this match' });
        return;
      }
    }

    const vote = req.body.vote as VoteOption;
    const category = req.body.category as VoteCategory || VoteCategory.MATCH_OUTCOME; // Default to match_outcome for backward compatibility

    // Validate vote option for category
    if (!isValidVoteForCategory(category, vote)) {
      res.status(400).json({
        message: `Invalid vote option '${vote}' for category '${category}'. Valid options: ${getValidVotesForCategory(category).join(', ')}`
      });
      return;
    }

    // Determine if this is an authenticated user or guest
    if (authReq.user && authReq.user.id) {
      // Authenticated user - save vote to their profile
      await createOrUpdateVote(authReq.user.id, fixtureId, category, vote);
    } else {
      // Guest user - save vote with guest identifier
      const guestIdentifier = generateGuestIdentifier(req);
      await createOrUpdateGuestVote(guestIdentifier, fixtureId, category, vote);
    }

    // Invalidate cache for this fixture's vote stats
    const redisClient = getRedisClient();
    await redisClient.del(`${FIXTURE_VOTE_STATS_CACHE_KEY}${fixtureId}`);

    // Get updated vote statistics
    const stats = await getFixtureVoteStats(fixtureId);

    res.status(200).json({
      message: 'Vote recorded successfully',
      category,
      vote,
      stats,
      userType: authReq.user ? 'authenticated' : 'guest'
    });
  } catch (err) {
    console.error('Error recording vote:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/:fixtureId/stats
// @desc    Get vote statistics for a fixture
// @access  Public
const getFixtureVoteStatsHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);
    if (isNaN(fixtureId)) {
      res.status(400).json({ message: 'Invalid fixture ID' });
      return;
    }

    const redisClient = getRedisClient();
    const cacheKey = `${FIXTURE_VOTE_STATS_CACHE_KEY}${fixtureId}`;

    // Try to get from cache first
    const cachedStats = await redisClient.get(cacheKey);
    if (cachedStats) {
      res.status(200).json(JSON.parse(cachedStats));
      return;
    }

    // Check if fixture exists
    const fixtureCollection = getFixturesCollection();
    const fixture = await fixtureCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      res.status(404).json({ message: 'Fixture not found' });
      return;
    }

    // Get vote statistics
    const stats = await getFixtureVoteStats(fixtureId);

    // Cache the result
    await redisClient.setex(cacheKey, CACHE_TTL_MINUTES, JSON.stringify(stats));

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error getting vote statistics:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/batch/stats
// @desc    Get vote statistics for multiple fixtures
// @access  Public
const getBatchVoteStatsHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureIdsParam = req.query.ids as string;

    if (!fixtureIdsParam) {
      res.status(400).json({ message: 'Missing required query parameter: ids' });
      return;
    }

    const fixtureIds = fixtureIdsParam.split(',').map(id => parseInt(id.trim()));

    if (fixtureIds.some(isNaN)) {
      res.status(400).json({ message: 'Invalid fixture IDs. All IDs must be numeric.' });
      return;
    }

    if (fixtureIds.length > 20) {
      res.status(400).json({ message: 'Too many fixture IDs. Maximum allowed is 20.' });
      return;
    }

    // Get vote statistics for multiple fixtures
    const stats = await getMultipleFixtureVoteStats(fixtureIds);

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error getting batch vote statistics:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/:fixtureId/user
// @desc    Get the current user's votes for a fixture (all categories)
// @access  Private
const getUserVoteHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const fixtureId = parseInt(req.params.fixtureId);
    if (isNaN(fixtureId)) {
      res.status(400).json({ message: 'Invalid fixture ID' });
      return;
    }

    // Check if specific category is requested
    const category = req.query.category as VoteCategory;

    if (category) {
      // Get user's vote for specific category
      const vote = await getUserVote(authReq.user.id, fixtureId, category);

      if (!vote) {
        res.status(404).json({ message: `Vote not found for category '${category}'` });
        return;
      }

      res.status(200).json({
        category: vote.category,
        vote: vote.vote,
        createdAt: vote.createdAt,
        updatedAt: vote.updatedAt
      });
    } else {
      // Get user's votes for all categories
      const votes = await getUserVotesForFixture(authReq.user.id, fixtureId);

      // Format response
      const response: any = {};
      Object.values(VoteCategory).forEach(cat => {
        if (votes[cat]) {
          response[cat] = {
            vote: votes[cat]!.vote,
            createdAt: votes[cat]!.createdAt,
            updatedAt: votes[cat]!.updatedAt
          };
        } else {
          response[cat] = null;
        }
      });

      res.status(200).json(response);
    }
  } catch (err) {
    console.error('Error getting user vote:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/count
// @desc    Get the count of votes for the current user
// @access  Private
const getUserVoteCountHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Get the count of user's votes
    const count = await getVoteCount(authReq.user.id);

    // Return just the count
    res.status(200).json({ count });
  } catch (err) {
    console.error('Error getting vote count:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/dates
// @desc    Get all dates for which the user has votes
// @access  Private
const getUserVoteDatesHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Get all dates with votes
    const dates = await getVoteDates(authReq.user.id);

    // Return the dates
    res.status(200).json({ response: dates });
  } catch (err) {
    console.error('Error getting vote dates:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/fixtures/votes/user
// @desc    Get all votes for the current user, optionally filtered by date
// @access  Private
const getUserVotesHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Check if date and timezone parameters are provided
    const dateParam = req.query.date as string | undefined;
    const timezoneParam = req.query.timezone as string | undefined;

    // Get all user's votes with fixture details, filtered by date if provided
    const votes = await getUserVotes(authReq.user.id, dateParam, timezoneParam);

    // Return the votes with count
    res.status(200).json({
      response: votes,
      results: votes.length
    });
  } catch (err) {
    console.error('Error getting user votes:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Register routes
router.post(
  '/:fixtureId',
  [
    optionalAuth, // Allow both authenticated and guest users
    body('vote').isIn(Object.values(VoteOption))
      .withMessage(`Vote must be one of: ${Object.values(VoteOption).join(', ')}`),
    body('category').optional().isIn(Object.values(VoteCategory))
      .withMessage(`Category must be one of: ${Object.values(VoteCategory).join(', ')}`)
  ],
  createVoteHandler
);

// Important: Order matters for routes. More specific routes should come first
router.get('/batch/stats', getBatchVoteStatsHandler);
router.get('/count', auth, getUserVoteCountHandler);
router.get('/dates', auth, getUserVoteDatesHandler);
router.get('/user', auth, getUserVotesHandler);
router.get('/:fixtureId/stats', getFixtureVoteStatsHandler);
router.get('/:fixtureId/user', auth, getUserVoteHandler);

export default router;
