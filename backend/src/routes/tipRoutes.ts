import express, { Router, RequestHandler } from 'express';
import { body, validationResult } from 'express-validator';
import {
  createTip,
  findTipsByFixture,
  findTipsByUser,
  deleteTip,
  updateTipLikes,
  getTipsWithDetails,
  validateTipDetails,
  getTipsCollection,
  TipType,
  TipStatus,
  Tip
} from '../models/Tip';
import { ObjectId } from 'mongodb';
import {
  createOrUpdateTipsterStats,
  getUserTipsterStats,
  getUserRecentTipResults
} from '../models/TipResult';
import { auth, optionalAuth, AuthRequest } from '../middleware/auth';
import { getFixturesCollection } from '../models/Fixture';
import { getRedisClient } from '../config/redis';
import { getSocketIO } from '../server';
import { broadcastNewTip, broadcastTipLikeUpdate, broadcastTipDeletion } from '../services/tipSocketService';

const router: Router = express.Router();

// Cache keys and TTL
const TIPS_CACHE_KEY = 'tips:fixture:';
const CACHE_TTL_MINUTES = 2 * 60; // Cache for 2 minutes

// @route   POST /api/tips
// @desc    Create a new tip
// @access  Private (authenticated users only)
const createTipHandler: RequestHandler = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
    return;
  }

  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Authentication required to create tips' });
      return;
    }

    const { fixtureId, tipType, details, odds, stake, description, isPublic } = req.body;

    // Validate fixture exists
    const fixturesCollection = getFixturesCollection();
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });
    if (!fixture) {
      res.status(404).json({ message: 'Fixture not found' });
      return;
    }

    // Check if fixture is still upcoming (can't tip on finished matches)
    const isUpcoming = fixture.fixture.status.short === 'NS' || fixture.fixture.status.short === 'TBD';
    if (!isUpcoming) {
      res.status(400).json({ message: 'Cannot create tips for matches that have already started or finished' });
      return;
    }

    // Validate tip type
    if (!Object.values(TipType).includes(tipType)) {
      res.status(400).json({ message: 'Invalid tip type' });
      return;
    }

    // Validate tip details based on type
    if (!validateTipDetails(tipType, details)) {
      res.status(400).json({ message: 'Invalid tip details for the specified tip type' });
      return;
    }

    // Validate odds (must be greater than 1.0)
    if (odds <= 1.0) {
      res.status(400).json({ message: 'Odds must be greater than 1.0' });
      return;
    }

    // Create the tip
    const tipData = {
      userId: new ObjectId(authReq.user.id),
      fixtureId,
      tipType,
      details,
      odds,
      stake: stake || 0,
      description,
      isPublic: isPublic !== false // Default to true if not specified
    };

    const newTip = await createTip(tipData);

    // Ensure user has tipster stats record
    await createOrUpdateTipsterStats(authReq.user.id);

    // Invalidate cache for this fixture
    const redisClient = getRedisClient();
    await redisClient.del(`${TIPS_CACHE_KEY}${fixtureId}`);

    // Broadcast new tip to connected clients
    const io = getSocketIO();
    if (io) {
      // Get tip with details for broadcasting
      const tipsWithDetails = await getTipsWithDetails(fixtureId, 1);
      if (tipsWithDetails.length > 0) {
        broadcastNewTip(io, tipsWithDetails[0]);
      }
    }

    res.status(201).json({
      message: 'Tip created successfully',
      tip: newTip
    });
  } catch (err) {
    console.error('Error creating tip:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tips/fixture/:fixtureId
// @desc    Get all tips for a specific fixture
// @access  Public
const getFixtureTipsHandler: RequestHandler = async (req, res) => {
  try {
    const fixtureId = parseInt(req.params.fixtureId);
    if (isNaN(fixtureId)) {
      res.status(400).json({ message: 'Invalid fixture ID' });
      return;
    }

    const limit = parseInt(req.query.limit as string) || 50;
    if (limit > 100) {
      res.status(400).json({ message: 'Limit cannot exceed 100' });
      return;
    }

    // Check cache first
    const redisClient = getRedisClient();
    const cacheKey = `${TIPS_CACHE_KEY}${fixtureId}:${limit}`;
    const cachedTips = await redisClient.get(cacheKey);

    if (cachedTips) {
      res.status(200).json(JSON.parse(cachedTips));
      return;
    }

    // Get tips with user and fixture details
    const tips = await getTipsWithDetails(fixtureId, limit);

    // Cache the result
    await redisClient.setex(cacheKey, CACHE_TTL_MINUTES, JSON.stringify(tips));

    res.status(200).json(tips);
  } catch (err) {
    console.error('Error getting fixture tips:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tips/user/me
// @desc    Get current user's tips
// @access  Private
const getUserTipsHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const limit = parseInt(req.query.limit as string) || 50;
    if (limit > 100) {
      res.status(400).json({ message: 'Limit cannot exceed 100' });
      return;
    }

    const tips = await findTipsByUser(authReq.user.id, limit);

    res.status(200).json(tips);
  } catch (err) {
    console.error('Error getting user tips:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   DELETE /api/tips/:tipId
// @desc    Delete a tip (only by owner)
// @access  Private
const deleteTipHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const tipId = req.params.tipId;
    if (!tipId) {
      res.status(400).json({ message: 'Tip ID is required' });
      return;
    }

    // Get tip details before deletion for broadcasting
    const tipsCollection = getTipsCollection();
    const tipToDelete = await tipsCollection.findOne({
      _id: new ObjectId(tipId),
      userId: new ObjectId(authReq.user.id)
    });

    const deleted = await deleteTip(tipId, authReq.user.id);
    if (!deleted) {
      res.status(404).json({ message: 'Tip not found or not authorized to delete' });
      return;
    }

    // Broadcast tip deletion to connected clients
    const io = getSocketIO();
    if (io && tipToDelete) {
      broadcastTipDeletion(io, tipId, tipToDelete.fixtureId);
    }

    res.status(200).json({ message: 'Tip deleted successfully' });
  } catch (err) {
    console.error('Error deleting tip:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   POST /api/tips/:tipId/like
// @desc    Like/unlike a tip
// @access  Private
const likeTipHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const tipId = req.params.tipId;
    const { action } = req.body; // 'like' or 'unlike'

    if (!tipId) {
      res.status(400).json({ message: 'Tip ID is required' });
      return;
    }

    if (!action || !['like', 'unlike'].includes(action)) {
      res.status(400).json({ message: 'Action must be "like" or "unlike"' });
      return;
    }

    // Get tip details before updating for broadcasting
    const tipsCollection = getTipsCollection();
    const tip = await tipsCollection.findOne({ _id: new ObjectId(tipId) });

    if (!tip) {
      res.status(404).json({ message: 'Tip not found' });
      return;
    }

    const increment = action === 'like' ? 1 : -1;
    await updateTipLikes(tipId, increment);

    // Get updated like count
    const updatedTip = await tipsCollection.findOne({ _id: new ObjectId(tipId) });
    const newLikeCount = updatedTip?.likes || 0;

    // Broadcast like update to connected clients
    const io = getSocketIO();
    if (io) {
      broadcastTipLikeUpdate(io, tipId, tip.fixtureId, newLikeCount);
    }

    res.status(200).json({
      message: `Tip ${action}d successfully`,
      action,
      likes: newLikeCount
    });
  } catch (err) {
    console.error('Error updating tip likes:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tips/user/me/stats
// @desc    Get current user's tipster statistics
// @access  Private
const getUserStatsHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const stats = await getUserTipsterStats(authReq.user.id);
    if (!stats) {
      // Create initial stats if they don't exist
      const newStats = await createOrUpdateTipsterStats(authReq.user.id);
      res.status(200).json(newStats);
      return;
    }

    res.status(200).json(stats);
  } catch (err) {
    console.error('Error getting user stats:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// @route   GET /api/tips/user/me/results
// @desc    Get current user's recent tip results
// @access  Private
const getUserResultsHandler: RequestHandler = async (req, res) => {
  try {
    const authReq = req as AuthRequest;
    if (!authReq.user || !authReq.user.id) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    const limit = parseInt(req.query.limit as string) || 20;
    if (limit > 50) {
      res.status(400).json({ message: 'Limit cannot exceed 50' });
      return;
    }

    const results = await getUserRecentTipResults(authReq.user.id, limit);

    res.status(200).json(results);
  } catch (err) {
    console.error('Error getting user results:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Register routes with validation
router.post(
  '/',
  [
    auth,
    body('fixtureId').isInt({ min: 1 }).withMessage('Valid fixture ID is required'),
    body('tipType').isIn(Object.values(TipType)).withMessage('Valid tip type is required'),
    body('details').isObject().withMessage('Tip details are required'),
    body('odds').isFloat({ min: 1.01 }).withMessage('Odds must be greater than 1.0'),
    body('stake').optional().isFloat({ min: 0 }).withMessage('Stake must be a positive number'),
    body('description').optional().isString().trim().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters'),
    body('isPublic').optional().isBoolean().withMessage('isPublic must be a boolean')
  ],
  createTipHandler
);

router.post(
  '/:tipId/like',
  [
    auth,
    body('action').isIn(['like', 'unlike']).withMessage('Action must be "like" or "unlike"')
  ],
  likeTipHandler
);

// Order matters - more specific routes first
router.get('/user/me/stats', auth, getUserStatsHandler);
router.get('/user/me/results', auth, getUserResultsHandler);
router.get('/user/me', auth, getUserTipsHandler);
router.get('/fixture/:fixtureId', getFixtureTipsHandler);
router.delete('/:tipId', auth, deleteTipHandler);

export default router;
