import express, { Router, RequestHandler } from 'express';
import { getLeaguesCollection, League } from '../models/League';
import { getTeamsCollection } from '../models/Team';
import { getRedisClient } from '../config/redis';
import { Filter } from 'mongodb'; // Import Filter type

const router: Router = express.Router();
const LEAGUES_BASE_CACHE_KEY = 'leagues:';
const CACHE_TTL_SECONDS = 60 * 15; // Cache for 15 minutes (adjust as needed, less than hourly job)

// Define the handler function with RequestHandler type
const getLeaguesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // Check if we need to handle special parameters first
    const teamId = queryParams.team ? parseInt(queryParams.team as string) : null;
    const lastCount = queryParams.last ? parseInt(queryParams.last as string) : null;

    try {
        // Handle team parameter separately as it requires a lookup in the teams collection
        if (teamId) {
            console.log(`Fetching leagues for team ID: ${teamId}`);
            const teamsCollection = getTeamsCollection();
            const team = await teamsCollection.findOne({ _id: teamId });

            if (!team) {
                res.status(404).json({ message: `Team with ID ${teamId} not found` });
                return;
            }

            // If team has leagues associated, use those league IDs to filter
            if (team.leagues && team.leagues.length > 0) {
                const leagueIds = team.leagues.map(league => league.apiId);
                const leaguesCollection = getLeaguesCollection();
                const leagues = await leaguesCollection.find(
                    { _id: { $in: leagueIds } },
                    { projection: { lastUpdated: 0 } }
                ).sort({ 'league.name': 1 }).toArray();

                res.status(200).json(leagues);
                return;
            } else {
                // If team has no leagues associated, return empty array
                res.status(200).json([]);
                return;
            }
        }

        // Build MongoDB filter dynamically based on query params
        const filter: Filter<League> = {};
        if (queryParams.id) filter._id = parseInt(queryParams.id as string); // Use _id for league ID
        if (queryParams.name) filter['league.name'] = new RegExp(queryParams.name as string, 'i'); // Case-insensitive search
        if (queryParams.country) filter['country.name'] = new RegExp(queryParams.country as string, 'i');
        if (queryParams.code) filter['country.code'] = (queryParams.code as string).toUpperCase();
        if (queryParams.season) filter['seasons.year'] = parseInt(queryParams.season as string);
        if (queryParams.type) filter['league.type'] = queryParams.type as string;
        if (queryParams.current === 'true') filter['seasons.current'] = true;
        if (queryParams.search) {
            const searchRegex = new RegExp(queryParams.search as string, 'i');
            filter.$or = [
                { 'league.name': searchRegex },
                { 'country.name': searchRegex }
            ];
        }

        // Generate cache key based on query params (simple version: only cache unfiltered)
        // A more robust solution would involve hashing the query params or creating a more structured key.
        const isFiltered = Object.keys(queryParams).length > 0;
        const cacheKey = isFiltered ? null : `${LEAGUES_BASE_CACHE_KEY}all`; // Only cache the full list for now

        // 1. Check cache (only if not filtered)
        if (cacheKey) {
            const cachedLeagues = await redisClient.get(cacheKey);
            if (cachedLeagues) {
                console.log('Serving leagues from cache');
                res.status(200).json(JSON.parse(cachedLeagues));
                return;
            }
        }

        // 2. Fetch from DB using the constructed filter
        console.log('Fetching leagues from DB with filter:', JSON.stringify(filter));
        const collection = getLeaguesCollection();
        // Exclude MongoDB _id if it wasn't the primary filter, exclude lastUpdated
        const projection = { lastUpdated: 0 };
        if (!filter._id) {
           // If filtering by something other than ID, we might still want the league ID (_id)
           // projection._id = 0; // Decide if you want _id in the response when not filtering by it
        }

        // Create a query that applies the filter and sorting
        let query = collection.find(filter, { projection });

        // Apply sorting based on parameters
        if (lastCount) {
            // For 'last' parameter, sort by lastUpdated in descending order
            query = query.sort({ lastUpdated: -1 }).limit(Math.min(lastCount, 99)); // Limit to max 99 as per API docs
        } else {
            // Default sorting by league name
            query = query.sort({ 'league.name': 1 });
        }

        const leagues = await query.toArray();

        // 3. Store in cache (only if not filtered)
        if (cacheKey && leagues.length > 0) {
            await redisClient.setex(cacheKey, CACHE_TTL_SECONDS, JSON.stringify(leagues));
            console.log('Unfiltered leagues stored in cache');
        }

        res.status(200).json(leagues);

    } catch (error) {
        console.error('Error fetching leagues:', error);
        res.status(500).json({ message: 'Failed to fetch leagues' });
    }
};

// GET /api/leagues - Fetches leagues with optional filtering
router.get('/', getLeaguesHandler);

export default router;
