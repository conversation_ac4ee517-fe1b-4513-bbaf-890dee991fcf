import express, { Router, RequestHandler } from 'express';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { getRedisClient } from '../config/redis';
import { Filter, Sort } from 'mongodb';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { getLeagueTier, getLeaguePriority } from '../config/leagueTiers';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

const router: Router = express.Router();

// Cache constants
const LEAGUES_FIXTURES_BASE_CACHE_KEY = 'leagues:fixtures:';
const CACHE_TTL_10_MINUTES = 600; // 10 minutes in seconds

/**
 * Get fixtures grouped by leagues with pagination by leagues
 * This endpoint returns complete fixtures for a set of leagues per page
 */
const getLeagueFixturesHandler: RequestHandler = async (req, res) => {
    const redisClient = getRedisClient();
    const queryParams = req.query;

    // --- Parameter Validation and Processing ---
    const {
        date, page, leaguesPerPage, status, timezone
    } = queryParams;

    try {
        // Validate date parameter
        if (!date || typeof date !== 'string' || !dayjs(date, 'YYYY-MM-DD', true).isValid()) {
            res.status(400).json({ message: 'Invalid or missing date parameter. Use YYYY-MM-DD format.' });
            return;
        }

        // Parse pagination parameters
        const currentPage = page ? parseInt(page as string) : 1;
        const itemsPerPage = leaguesPerPage ? parseInt(leaguesPerPage as string) : 6; // Default to 6 leagues per page

        if (currentPage < 1 || isNaN(currentPage)) {
            res.status(400).json({ message: 'Invalid page parameter. Must be a positive integer.' });
            return;
        }

        if (itemsPerPage < 1 || isNaN(itemsPerPage)) {
            res.status(400).json({ message: 'Invalid leaguesPerPage parameter. Must be a positive integer.' });
            return;
        }

        // Build cache key
        const statusParam = status ? `-${status}` : '';
        const timezoneParam = timezone ? `:tz:${timezone}` : '';
        const cacheKey = `${LEAGUES_FIXTURES_BASE_CACHE_KEY}${date}${statusParam}${timezoneParam}:page${currentPage}:lpp${itemsPerPage}`;

        // Try to get from cache first
        const cachedData = await redisClient.get(cacheKey);
        if (cachedData) {
            console.log(`Serving league fixtures from cache (Key: ${cacheKey})`);
            res.status(200).json(JSON.parse(cachedData));
            return;
        }

        // Build filter for the date with timezone support
        let startOfDay: number;
        let endOfDay: number;

        if (timezone && typeof timezone === 'string') {
            // Use the provided timezone to calculate day boundaries
            try {
                startOfDay = dayjs.tz(date, timezone as string).startOf('day').unix();
                endOfDay = dayjs.tz(date, timezone as string).endOf('day').unix();
            } catch (error) {
                console.warn(`Invalid timezone provided: ${timezone}, falling back to UTC`);
                startOfDay = dayjs(date).startOf('day').unix();
                endOfDay = dayjs(date).endOf('day').unix();
            }
        } else {
            // Default to UTC if no timezone provided
            startOfDay = dayjs(date).startOf('day').unix();
            endOfDay = dayjs(date).endOf('day').unix();
        }

        const filter: Filter<Fixture> = {
            'fixture.timestamp': { $gte: startOfDay, $lte: endOfDay }
        };

        // Add status filter if provided
        if (status) {
            filter['fixture.status.short'] = { $in: (status as string).split('-') };
        }

        // Get the collection
        const collection = getFixturesCollection();

        // Get unique league IDs for this date/status combination
        const leagueIds = await collection.distinct('league.id', filter);
        
        // Get basic league info for sorting
        const leagueInfoQuery = { ...filter, 'league.id': { $in: leagueIds } };
        const projection = { 'league.id': 1, 'league.name': 1, 'league.country': 1, 'league.logo': 1, 'league.flag': 1, 'league.season': 1 };
        const fixturesWithLeagueInfo = await collection.find(leagueInfoQuery, { projection }).toArray();
        
        // Create a map of unique leagues
        const leagueInfoMap = new Map();
        fixturesWithLeagueInfo.forEach(fixture => {
            if (!leagueInfoMap.has(fixture.league.id)) {
                leagueInfoMap.set(fixture.league.id, fixture.league);
            }
        });
        
        // Convert to array and sort by tier and priority
        const sortedLeagues = Array.from(leagueInfoMap.values()).sort((a, b) => {
            // First sort by tier (lower tier number = higher popularity)
            const tierA = getLeagueTier(a.id);
            const tierB = getLeagueTier(b.id);
            
            if (tierA !== tierB) {
                return tierA - tierB; // Lower tier number first (Tier 1 before Tier 2)
            }
            
            // If tiers are the same, sort by priority within tier
            const priorityA = getLeaguePriority(a.id);
            const priorityB = getLeaguePriority(b.id);
            
            if (priorityA !== priorityB) {
                return priorityA - priorityB; // Lower priority number first
            }
            
            // If priorities are the same, sort by country
            if (a.country !== b.country) {
                return a.country.localeCompare(b.country);
            }
            
            // If countries are the same, sort by name
            return a.name.localeCompare(b.name);
        });
        
        // Extract the sorted league IDs in the correct order
        const sortedLeagueIds = sortedLeagues.map(league => league.id);
        
        // Count total leagues and calculate total pages using the sorted league IDs
        const totalLeagues = sortedLeagueIds.length;
        const totalPages = Math.ceil(totalLeagues / itemsPerPage);

        // If requested page exceeds total pages, return error
        if (currentPage > totalPages && totalPages > 0) {
            res.status(400).json({
                error: `Requested page ${currentPage} exceeds available pages (${totalPages})`,
                pagination: {
                    page: currentPage,
                    leaguesPerPage: itemsPerPage,
                    totalPages,
                    totalLeagues,
                    hasNextPage: false,
                    hasPrevPage: totalPages > 0
                }
            });
            return;
        }

        // Get the league IDs for the current page - use the SORTED array!
        const paginatedLeagueIds = sortedLeagueIds.slice(
            (currentPage - 1) * itemsPerPage,
            currentPage * itemsPerPage
        );

        // If no leagues found for this page
        if (paginatedLeagueIds.length === 0) {
            res.status(200).json({
                data: [],
                pagination: {
                    page: currentPage,
                    leaguesPerPage: itemsPerPage,
                    totalPages,
                    totalLeagues,
                    hasNextPage: currentPage < totalPages,
                    hasPrevPage: currentPage > 1
                }
            });
            return;
        }

        // Get all fixtures for these leagues
        const leagueFilter: Filter<Fixture> = {
            ...filter,
            'league.id': { $in: paginatedLeagueIds }
        };

        // Sort fixtures by timestamp
        const sort: Sort = { 'fixture.timestamp': 1, '_id': 1 };

        // Get all fixtures for the selected leagues
        const fixtures = await collection.find(leagueFilter, { projection: { lastUpdated: 0 } })
            .sort(sort)
            .toArray();

        // Group fixtures by league
        const fixturesByLeague: { [key: number]: Fixture[] } = {};

        fixtures.forEach(fixture => {
            const leagueId = fixture.league.id;
            if (!fixturesByLeague[leagueId]) {
                fixturesByLeague[leagueId] = [];
            }
            fixturesByLeague[leagueId].push(fixture);
        });

        // Convert to array format WHILE PRESERVING THE SORTED ORDER
        // Use the paginatedLeagueIds (which are already sorted by tier) to ensure correct order
        const leaguesWithFixtures = paginatedLeagueIds
            .filter(leagueId => fixturesByLeague[leagueId]) // Only include leagues that have fixtures
            .map(leagueId => {
                const leagueFixtures = fixturesByLeague[leagueId];
                // Use the first fixture to get league info
                const { league } = leagueFixtures[0];

                const tier = getLeagueTier(league.id);
                return {
                    league: {
                        id: league.id,
                        name: league.name,
                        country: league.country,
                        logo: league.logo,
                        flag: league.flag,
                        season: league.season,
                        tier: tier // Add tier information to the response
                    },
                    fixtures: leagueFixtures
                };
            });

        // No need to sort leagues here as they're already pre-sorted by tier before pagination
        // We just need to maintain the order of the leagues as they were selected

        // Prepare response
        const response = {
            data: leaguesWithFixtures,
            pagination: {
                page: currentPage,
                leaguesPerPage: itemsPerPage,
                totalPages,
                totalLeagues,
                hasNextPage: currentPage < totalPages,
                hasPrevPage: currentPage > 1
            }
        };

        // Cache the response
        await redisClient.setex(cacheKey, CACHE_TTL_10_MINUTES, JSON.stringify(response));
        console.log(`League fixtures stored in cache (Key: ${cacheKey})`);

        // Return the response
        res.status(200).json(response);

    } catch (error) {
        console.error('Error fetching league fixtures:', error);
        res.status(500).json({ message: 'Failed to fetch league fixtures' });
    }
};

// GET /api/league-fixtures - Fetches fixtures grouped by leagues with pagination
router.get('/', getLeagueFixturesHandler);

export default router;
