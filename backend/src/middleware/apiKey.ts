import { Request, Response, NextFunction } from 'express';

/**
 * Middleware to validate API key for all non-public endpoints
 * The key should be included in the X-API-Key header
 */
export function apiKeyAuth(req: Request, res: Response, next: NextFunction): void {
  const apiKey = req.header('X-API-Key');
  
  // Check if API key exists and matches the expected value
  if (!apiKey || apiKey !== process.env.API_KEY) {
    res.status(401).json({ message: 'Invalid API key' });
    return;
  }
  
  next();
}
