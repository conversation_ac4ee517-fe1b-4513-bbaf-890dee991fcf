import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { findUserById } from '../models/User';
import dotenv from 'dotenv';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'default_secret_key';

export interface AuthRequest extends Request {
  user?: {
    id: string;
  };
  file?: Express.Multer.File;
}

export function generateToken(userId: string): string {
  return jwt.sign({ id: userId }, JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRATION || '7d'
  } as jwt.SignOptions);
}

export async function auth(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
  try {
    // Get token from header
    const token = req.header('x-auth-token');

    // Check if no token
    if (!token) {
      res.status(401).json({ message: 'No token, authorization denied' });
      return;
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as { id: string };

    // Check if user exists
    const user = await findUserById(decoded.id);
    if (!user) {
      res.status(401).json({ message: 'User not found' });
      return;
    }

    // Add user id to request
    req.user = { id: decoded.id };
    next();
  } catch (err) {
    res.status(401).json({ message: 'Token is not valid' });
  }
}

/**
 * Optional authentication middleware that allows requests to proceed with or without authentication.
 * If a valid token is provided, req.user will be set. If not, the request continues without user info.
 * This is useful for endpoints that work for both authenticated and guest users.
 */
export async function optionalAuth(req: AuthRequest, res: Response, next: NextFunction): Promise<void> {
  try {
    // Get token from header
    const token = req.header('x-auth-token');

    // If no token provided, continue without user
    if (!token) {
      next();
      return;
    }

    // Verify token
    const decoded = jwt.verify(token, JWT_SECRET) as { id: string };

    // Check if user exists
    const user = await findUserById(decoded.id);
    if (user) {
      // Add user id to request if user exists
      req.user = { id: decoded.id };
    }

    next();
  } catch (err) {
    // Invalid token, continue without user (don't fail the request)
    next();
  }
}
