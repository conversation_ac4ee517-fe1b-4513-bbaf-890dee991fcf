/**
 * Bet Type Mapping Utilities
 * 
 * This file provides utilities to map between:
 * - TipType enums and API bet IDs
 * - Tip details and bet value strings
 * - Bet selections and their corresponding API values
 */

import { TipType, TipDetails } from '../models/Tip';

// Mapping from TipType to API bet IDs with market support
export const TIP_TYPE_TO_BET_ID_MAP: Record<TipType, Record<string, number>> = {
  [TipType.MATCH_RESULT]: {
    fulltime: 1,    // Match Winner (1X2)
    firsthalf: 13,  // First Half Winner
  },
  [TipType.BTTS]: {
    fulltime: 8,    // Both Teams Score
    firsthalf: 34,  // Both Teams Score - First Half
  },
  [TipType.OVER_UNDER_GOALS]: {
    fulltime: 5,    // Goals Over/Under
    firsthalf: 6,   // Goals Over/Under First Half
  },
  [TipType.OVER_UNDER_CORNERS]: {
    fulltime: 45,   // Corners Over Under
  },
  [TipType.OVER_UNDER_CARDS]: {
    fulltime: 80,   // Cards Over/Under
  },
  [TipType.DOUBLE_CHANCE]: {
    fulltime: 12,   // Double Chance
  },
  [TipType.HANDICAP]: {
    fulltime: 4,    // Asian Handicap
    firsthalf: 19,  // Asian Handicap First Half
  },
  [TipType.CORRECT_SCORE]: {
    fulltime: 10,   // Exact Score
  },
  [TipType.HALF_TIME_RESULT]: {
    firsthalf: 13,  // First Half Winner (same as match result first half)
  },
  [TipType.FIRST_GOAL]: {
    fulltime: 14,   // Team To Score First
  },
};

// Legacy mapping for backward compatibility (defaults to fulltime)
export const TIP_TYPE_TO_BET_ID: Record<TipType, number> = {
  [TipType.MATCH_RESULT]: 1,           // Match Winner (1X2)
  [TipType.BTTS]: 8,                   // Both Teams Score
  [TipType.OVER_UNDER_GOALS]: 5,       // Goals Over/Under
  [TipType.OVER_UNDER_CORNERS]: 45,    // Corners Over Under
  [TipType.OVER_UNDER_CARDS]: 80,      // Cards Over/Under
  [TipType.DOUBLE_CHANCE]: 12,         // Double Chance
  [TipType.HANDICAP]: 4,               // Asian Handicap
  [TipType.CORRECT_SCORE]: 10,         // Exact Score
  [TipType.HALF_TIME_RESULT]: 13,      // First Half Winner
  [TipType.FIRST_GOAL]: 14,            // Team To Score First
};

// Reverse mapping from bet ID to TipType
export const BET_ID_TO_TIP_TYPE: Record<number, TipType> = Object.entries(TIP_TYPE_TO_BET_ID)
  .reduce((acc, [tipType, betId]) => {
    acc[betId] = tipType as TipType;
    return acc;
  }, {} as Record<number, TipType>);

/**
 * Convert tip details to bet value string that matches API response
 */
export function tipDetailsToBetValue(tipType: TipType, details: TipDetails): string | null {
  switch (tipType) {
    case TipType.MATCH_RESULT:
    case TipType.HALF_TIME_RESULT:
      switch (details.result) {
        case 'home': return 'Home';
        case 'draw': return 'Draw';
        case 'away': return 'Away';
        default: return null;
      }

    case TipType.BTTS:
      switch (details.bttsValue) {
        case 'yes': return 'Yes';
        case 'no': return 'No';
        default: return null;
      }

    case TipType.OVER_UNDER_GOALS:
    case TipType.OVER_UNDER_CORNERS:
    case TipType.OVER_UNDER_CARDS:
      if (details.line && details.overUnder) {
        const lineStr = details.line.toString();
        return details.overUnder === 'over' ? `Over ${lineStr}` : `Under ${lineStr}`;
      }
      return null;

    case TipType.DOUBLE_CHANCE:
      // Map frontend values to API values
      switch (details.doubleChance) {
        case '1X': return 'Home/Draw';
        case 'X2': return 'Draw/Away';
        case '12': return 'Home/Away';
        default: return null;
      }

    case TipType.HANDICAP:
      if (details.handicapValue !== undefined && details.handicapTeam) {
        const handicapStr = details.handicapValue >= 0 ? `+${details.handicapValue}` : details.handicapValue.toString();
        return details.handicapTeam === 'home' ? `Home ${handicapStr}` : `Away ${handicapStr}`;
      }
      return null;

    case TipType.CORRECT_SCORE:
      if (details.homeScore !== undefined && details.awayScore !== undefined) {
        return `${details.homeScore}:${details.awayScore}`;
      }
      return null;

    case TipType.FIRST_GOAL:
      switch (details.firstGoalTeam) {
        case 'home': return 'Home';
        case 'away': return 'Away';
        default: return null;
      }

    default:
      return null;
  }
}

/**
 * Convert bet value string back to tip details
 */
export function betValueToTipDetails(tipType: TipType, betValue: string): Partial<TipDetails> {
  switch (tipType) {
    case TipType.MATCH_RESULT:
    case TipType.HALF_TIME_RESULT:
      switch (betValue.toLowerCase()) {
        case 'home': return { result: 'home' };
        case 'draw': return { result: 'draw' };
        case 'away': return { result: 'away' };
        default: return {};
      }

    case TipType.BTTS:
      switch (betValue.toLowerCase()) {
        case 'yes': return { bttsValue: 'yes' };
        case 'no': return { bttsValue: 'no' };
        default: return {};
      }

    case TipType.OVER_UNDER_GOALS:
    case TipType.OVER_UNDER_CORNERS:
    case TipType.OVER_UNDER_CARDS:
      const overUnderMatch = betValue.match(/^(Over|Under)\s+(\d+\.?\d*)$/i);
      if (overUnderMatch) {
        return {
          overUnder: overUnderMatch[1].toLowerCase() as 'over' | 'under',
          line: parseFloat(overUnderMatch[2])
        };
      }
      return {};

    case TipType.DOUBLE_CHANCE:
      // Map API values to frontend values
      switch (betValue) {
        case 'Home/Draw': return { doubleChance: '1X' };
        case 'Draw/Away': return { doubleChance: 'X2' };
        case 'Home/Away': return { doubleChance: '12' };
        // Also handle legacy format
        case '1X': return { doubleChance: '1X' };
        case 'X2': return { doubleChance: 'X2' };
        case '12': return { doubleChance: '12' };
        default: return {};
      }

    case TipType.HANDICAP:
      const handicapMatch = betValue.match(/^(Home|Away)\s+([\+\-]?\d+\.?\d*)$/i);
      if (handicapMatch) {
        return {
          handicapTeam: handicapMatch[1].toLowerCase() as 'home' | 'away',
          handicapValue: parseFloat(handicapMatch[2])
        };
      }
      return {};

    case TipType.CORRECT_SCORE:
      const scoreMatch = betValue.match(/^(\d+):(\d+)$/);
      if (scoreMatch) {
        return {
          homeScore: parseInt(scoreMatch[1]),
          awayScore: parseInt(scoreMatch[2])
        };
      }
      return {};

    case TipType.FIRST_GOAL:
      switch (betValue.toLowerCase()) {
        case 'home': return { firstGoalTeam: 'home' };
        case 'away': return { firstGoalTeam: 'away' };
        default: return {};
      }

    default:
      return {};
  }
}

/**
 * Get the bet ID for a given tip type and market
 */
export function getBetIdForTipType(tipType: TipType, market: string = 'fulltime'): number | null {
  const marketMap = TIP_TYPE_TO_BET_ID_MAP[tipType];
  if (!marketMap) return null;

  // Try the specific market first, then fallback to fulltime
  return marketMap[market] || marketMap['fulltime'] || null;
}

/**
 * Legacy function for backward compatibility
 */
export function getBetIdForTipTypeLegacy(tipType: TipType): number | null {
  return TIP_TYPE_TO_BET_ID[tipType] || null;
}

/**
 * Get the tip type for a given bet ID
 */
export function getTipTypeForBetId(betId: number): TipType | null {
  return BET_ID_TO_TIP_TYPE[betId] || null;
}

/**
 * Check if a tip type is supported for odds fetching
 */
export function isTipTypeSupported(tipType: TipType): boolean {
  return tipType in TIP_TYPE_TO_BET_ID;
}

/**
 * Get all supported tip types for odds fetching
 */
export function getSupportedTipTypes(): TipType[] {
  return Object.keys(TIP_TYPE_TO_BET_ID) as TipType[];
}

/**
 * Validate if tip details can be converted to a bet value
 */
export function canConvertTipDetailsToBetValue(tipType: TipType, details: TipDetails): boolean {
  return tipDetailsToBetValue(tipType, details) !== null;
}
