/**
 * Utility functions for mapping fixture status codes
 */

/**
 * Maps API status codes to our application's display status codes
 * @param statusCode The original status code from the API
 * @returns The mapped status code for display in the application
 */
export function mapStatusCode(statusCode: string): string {
  // Map status codes for extended match scenarios
  switch (statusCode) {
    // Extra Time related status codes
    case 'BT':
      return 'Aw. ET'; // Break Time to Awaiting Extra Time
    case 'ET1':
      return 'ET'; // First Half Extra Time to Extra Time
    case 'ET2':
      return 'ET'; // Second Half Extra Time to Extra Time
    case 'HET':
      return 'BT'; // Half-time Extra Time to Break Time (for notification purposes)

    // Penalty related status codes
    case 'PSO':
      return 'P'; // Penalty Shoot-out to P (for notification purposes)

    // Handle cases where API already returns 'Aw. ET' - keep it as is
    case 'Aw. ET':
      return 'Aw. ET'; // Already mapped, keep as is

    // Return the original status code for all other statuses
    default:
      return statusCode;
  }
}

/**
 * Apply status mapping to a fixture object
 * @param fixture The fixture object to process
 * @returns The fixture with mapped status codes
 */
export function applyStatusMapping(fixture: any): any {
  if (!fixture || !fixture.fixture || !fixture.fixture.status || !fixture.fixture.status.short) {
    return fixture;
  }

  // Create a deep copy of the fixture to avoid modifying the original
  const mappedFixture = JSON.parse(JSON.stringify(fixture));

  // Map the status code
  mappedFixture.fixture.status.short = mapStatusCode(mappedFixture.fixture.status.short);

  return mappedFixture;
}

/**
 * Apply status mapping to an array of fixtures
 * @param fixtures Array of fixture objects to process
 * @returns Array of fixtures with mapped status codes
 */
export function applyStatusMappingToArray(fixtures: any[]): any[] {
  if (!fixtures || !Array.isArray(fixtures)) {
    return fixtures;
  }

  return fixtures.map(fixture => applyStatusMapping(fixture));
}
