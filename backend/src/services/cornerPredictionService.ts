/**
 * Corner Prediction Service
 * 
 * Generates corner predictions using Poisson distribution
 * Focuses on Total Corners Over/Under markets only
 */

import { 
  CornerPrediction, 
  CornerMarketPrediction, 
  TeamCornerStrength,
  LeagueCornerParameters,
  CornerPredictionUtils
} from '../models/CornerPrediction';
import { TeamCornerStrengthService } from './teamCornerStrengthService';
import { CornerDataService } from './cornerDataService';

export class CornerPredictionService {
  private static readonly MODEL_VERSION = '1.0.0';
  private static readonly MIN_CONFIDENCE_THRESHOLD = 0.3;
  
  /**
   * Generate corner prediction for a specific fixture
   */
  static async generateCornerPrediction(
    homeTeamId: number,
    awayTeamId: number,
    leagueId: number,
    fixtureDate: Date
  ): Promise<CornerPrediction | null> {
    try {
      // Check if league supports corner predictions
      const hasSupport = await CornerDataService.checkLeagueCornerSupport(leagueId);
      if (!hasSupport) {
        console.log(`League ${leagueId} does not support corner predictions`);
        return null;
      }
      
      // Get team corner strengths
      const teamStrengths = await TeamCornerStrengthService.getTeamCornerStrengths(
        leagueId,
        fixtureDate
      );
      
      const homeTeamStrength = teamStrengths.get(homeTeamId);
      const awayTeamStrength = teamStrengths.get(awayTeamId);
      
      if (!homeTeamStrength || !awayTeamStrength) {
        console.warn(`Insufficient corner data for teams ${homeTeamId} vs ${awayTeamId} in league ${leagueId}`);
        return this.generateLeagueAveragePrediction(leagueId);
      }
      
      // Check confidence threshold
      const minConfidence = Math.min(homeTeamStrength.confidence, awayTeamStrength.confidence);
      if (minConfidence < this.MIN_CONFIDENCE_THRESHOLD) {
        console.warn(`Low confidence for corner prediction: ${minConfidence.toFixed(2)}`);
        return this.generateLeagueAveragePrediction(leagueId);
      }
      
      // Calculate expected corner rates
      const { homeExpected, awayExpected, totalExpected } = this.calculateExpectedCorners(
        homeTeamStrength,
        awayTeamStrength,
        leagueId
      );
      
      // Generate market predictions
      const markets = this.generateMarketPredictions(totalExpected, minConfidence);
      
      // Generate corner distribution
      const distribution = this.generateCornerDistribution(totalExpected);
      
      // Calculate overall confidence
      const overallConfidence = this.calculateOverallConfidence(
        homeTeamStrength,
        awayTeamStrength,
        totalExpected
      );
      
      return {
        expectedTotal: totalExpected,
        homeExpected,
        awayExpected,
        confidence: overallConfidence,
        markets,
        distribution,
        algorithm: 'poisson',
        dataSource: 'historical',
        modelVersion: this.MODEL_VERSION,
        calculatedAt: new Date()
      };
      
    } catch (error) {
      console.error(`Error generating corner prediction for ${homeTeamId} vs ${awayTeamId}:`, error);
      return null;
    }
  }
  
  /**
   * Calculate expected corners for both teams
   */
  private static calculateExpectedCorners(
    homeTeamStrength: TeamCornerStrength,
    awayTeamStrength: TeamCornerStrength,
    leagueId: number
  ): { homeExpected: number; awayExpected: number; totalExpected: number } {
    
    // Get league average corners (fallback to global average if not available)
    const leagueAverage = 9.6; // TODO: Get from league parameters
    const baseCornerRate = leagueAverage / 2; // Average per team
    
    // Calculate home team expected corners
    // Home attack strength * Away defense weakness * Home advantage
    const homeExpected = baseCornerRate * 
      homeTeamStrength.cornerAttack * 
      awayTeamStrength.cornerDefense * 
      homeTeamStrength.homeCornerBonus;
    
    // Calculate away team expected corners
    // Away attack strength * Home defense weakness
    const awayExpected = baseCornerRate * 
      awayTeamStrength.cornerAttack * 
      homeTeamStrength.cornerDefense;
    
    const totalExpected = homeExpected + awayExpected;
    
    // Ensure reasonable bounds
    const boundedHomeExpected = Math.max(1, Math.min(15, homeExpected));
    const boundedAwayExpected = Math.max(1, Math.min(15, awayExpected));
    const boundedTotalExpected = boundedHomeExpected + boundedAwayExpected;
    
    return {
      homeExpected: boundedHomeExpected,
      awayExpected: boundedAwayExpected,
      totalExpected: boundedTotalExpected
    };
  }
  
  /**
   * Generate market predictions for Total Corners Over/Under
   */
  private static generateMarketPredictions(
    totalExpected: number,
    confidence: number
  ): CornerPrediction['markets'] {
    
    const thresholds = [8.5, 9.5, 10.5, 11.5, 12.5];
    const markets: any = {};
    
    for (const threshold of thresholds) {
      const overProbability = CornerPredictionUtils.calculateOverProbability(
        totalExpected,
        threshold
      );
      
      const marketPrediction: CornerMarketPrediction = {
        threshold,
        overProbability,
        underProbability: 1 - overProbability,
        value: CornerPredictionUtils.calculateValueRating(overProbability), // No market odds available yet
        confidence: this.getMarketConfidence(confidence, overProbability)
      };
      
      const marketKey = `over${threshold.toString().replace('.', '_')}`;
      markets[marketKey] = marketPrediction;
    }
    
    return markets;
  }
  
  /**
   * Generate corner distribution (probability for each corner count)
   */
  private static generateCornerDistribution(
    totalExpected: number,
    maxCorners: number = 20
  ): Array<{ corners: number; probability: number }> {
    
    const distribution = [];
    
    for (let corners = 0; corners <= maxCorners; corners++) {
      const probability = CornerPredictionUtils.poissonPMF(totalExpected, corners);
      
      if (probability > 0.001) { // Only include probabilities > 0.1%
        distribution.push({
          corners,
          probability
        });
      }
    }
    
    return distribution;
  }
  
  /**
   * Calculate overall prediction confidence
   */
  private static calculateOverallConfidence(
    homeTeamStrength: TeamCornerStrength,
    awayTeamStrength: TeamCornerStrength,
    totalExpected: number
  ): number {
    
    // Base confidence from team data quality
    const teamConfidence = (homeTeamStrength.confidence + awayTeamStrength.confidence) / 2;
    
    // Match count factor
    const minMatches = Math.min(homeTeamStrength.matchCount, awayTeamStrength.matchCount);
    const matchFactor = Math.min(minMatches / 20, 1); // Max confidence at 20+ matches
    
    // Expected value reasonableness factor
    const reasonablenessFactor = totalExpected >= 6 && totalExpected <= 16 ? 1 : 0.8;
    
    // Combine factors
    const overallConfidence = teamConfidence * matchFactor * reasonablenessFactor;
    
    return Math.round(overallConfidence * 100); // Convert to percentage
  }
  
  /**
   * Get market-specific confidence level
   */
  private static getMarketConfidence(
    overallConfidence: number,
    probability: number
  ): 'low' | 'medium' | 'high' {
    
    // Higher confidence for probabilities closer to 0.5 (more certain outcomes)
    const probabilityFactor = 1 - Math.abs(probability - 0.5) * 2;
    const adjustedConfidence = overallConfidence * probabilityFactor;
    
    if (adjustedConfidence > 0.7) return 'high';
    if (adjustedConfidence > 0.4) return 'medium';
    return 'low';
  }
  
  /**
   * Generate league average prediction when team data is insufficient
   */
  private static async generateLeagueAveragePrediction(
    leagueId: number
  ): Promise<CornerPrediction | null> {
    try {
      // Use global averages as fallback
      const totalExpected = 9.6; // Global average corners per match
      const homeExpected = 5.2;  // Slightly higher for home team
      const awayExpected = 4.4;  // Slightly lower for away team
      
      const markets = this.generateMarketPredictions(totalExpected, 0.3); // Low confidence
      const distribution = this.generateCornerDistribution(totalExpected);
      
      return {
        expectedTotal: totalExpected,
        homeExpected,
        awayExpected,
        confidence: 30, // Low confidence for league average
        markets,
        distribution,
        algorithm: 'poisson',
        dataSource: 'league_average',
        modelVersion: this.MODEL_VERSION,
        calculatedAt: new Date()
      };
      
    } catch (error) {
      console.error(`Error generating league average prediction for league ${leagueId}:`, error);
      return null;
    }
  }
  
  /**
   * Validate corner prediction
   */
  static validateCornerPrediction(prediction: CornerPrediction): boolean {
    try {
      // Check expected values are reasonable
      if (prediction.expectedTotal < 3 || prediction.expectedTotal > 20) {
        return false;
      }
      
      if (prediction.homeExpected < 0.5 || prediction.homeExpected > 15) {
        return false;
      }
      
      if (prediction.awayExpected < 0.5 || prediction.awayExpected > 15) {
        return false;
      }
      
      // Check confidence is reasonable
      if (prediction.confidence < 0 || prediction.confidence > 100) {
        return false;
      }
      
      // Check market probabilities sum correctly
      for (const market of Object.values(prediction.markets)) {
        const total = market.overProbability + market.underProbability;
        if (Math.abs(total - 1) > 0.01) { // Allow small rounding errors
          return false;
        }
      }
      
      // Check distribution probabilities
      const totalProbability = prediction.distribution.reduce(
        (sum, item) => sum + item.probability, 
        0
      );
      if (Math.abs(totalProbability - 1) > 0.1) { // Allow for truncation
        return false;
      }
      
      return true;
      
    } catch (error) {
      console.error('Error validating corner prediction:', error);
      return false;
    }
  }
}
