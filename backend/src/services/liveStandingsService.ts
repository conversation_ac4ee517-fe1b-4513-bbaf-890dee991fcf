import { Server as SocketIOServer } from 'socket.io';
import { getStandingsCollection, Standing, createStandingId } from '../models/Standing';
import { getRedisClient } from '../config/redis';

// Interface for live match information
export interface LiveMatchInfo {
  fixtureId: number;
  opponent: number;
  homeAway: 'home' | 'away';
  currentScore: { home: number; away: number };
  originalScore: { home: number; away: number }; // Score at match start
  status: string;
  minute: number;
  isFinished: boolean;
}

// Interface for live standing adjustments
export interface LiveStandingAdjustment {
  teamId: number;
  goalsFor: number;      // Additional goals from live matches
  goalsAgainst: number;  // Additional goals conceded from live matches
  points: number;        // Additional points (3 for win, 1 for draw, 0 for loss)
  wins: number;          // Additional wins from completed live matches
  draws: number;         // Additional draws from completed live matches
  losses: number;        // Additional losses from completed live matches
  matchesPlayed: number; // Additional matches played (when match finishes)
  liveMatches: LiveMatchInfo[]; // Current live matches for this team
  goalsDiff: number;     // Additional goal difference
}

// Interface for live standings data
export interface LiveStandingsData {
  leagueId: number;
  season: number;
  adjustments: Map<number, LiveStandingAdjustment>;
  lastUpdated: Date;
}

// Cache for live standings data
const liveStandingsCache = new Map<string, LiveStandingsData>();

/**
 * Live Standings Service
 * Manages real-time standings updates based on live match events
 */
export class LiveStandingsService {
  private io: SocketIOServer;
  private redisClient = getRedisClient();

  constructor(io: SocketIOServer) {
    this.io = io;
  }

  /**
   * Get the cache key for live standings
   */
  private getLiveStandingsCacheKey(leagueId: number, season: number): string {
    return `live_standings:${leagueId}:${season}`;
  }

  /**
   * Initialize or get live standings data for a league
   */
  private async initializeLiveStandings(leagueId: number, season: number): Promise<LiveStandingsData> {
    const cacheKey = this.getLiveStandingsCacheKey(leagueId, season);
    
    let liveData = liveStandingsCache.get(cacheKey);
    if (!liveData) {
      liveData = {
        leagueId,
        season,
        adjustments: new Map<number, LiveStandingAdjustment>(),
        lastUpdated: new Date()
      };
      liveStandingsCache.set(cacheKey, liveData);
    }
    
    return liveData;
  }

  /**
   * Update live match information
   */
  async updateLiveMatch(
    leagueId: number,
    season: number,
    fixtureId: number,
    homeTeamId: number,
    awayTeamId: number,
    currentScore: { home: number; away: number },
    originalScore: { home: number; away: number },
    status: string,
    minute: number
  ): Promise<void> {
    try {
      const liveData = await this.initializeLiveStandings(leagueId, season);
      const isFinished = ['FT', 'AET', 'PEN'].includes(status);

      // Update or create adjustments for both teams
      await this.updateTeamAdjustment(liveData, homeTeamId, fixtureId, awayTeamId, 'home', 
        currentScore, originalScore, status, minute, isFinished);
      
      await this.updateTeamAdjustment(liveData, awayTeamId, fixtureId, homeTeamId, 'away', 
        currentScore, originalScore, status, minute, isFinished);

      liveData.lastUpdated = new Date();

      // Broadcast live standings update
      await this.broadcastLiveStandingsUpdate(leagueId, season);

      console.log(`Live standings updated for league ${leagueId}, fixture ${fixtureId}`);
    } catch (error) {
      console.error('Error updating live match:', error);
    }
  }

  /**
   * Update team adjustment data
   */
  private async updateTeamAdjustment(
    liveData: LiveStandingsData,
    teamId: number,
    fixtureId: number,
    opponentId: number,
    homeAway: 'home' | 'away',
    currentScore: { home: number; away: number },
    originalScore: { home: number; away: number },
    status: string,
    minute: number,
    isFinished: boolean
  ): Promise<void> {
    let adjustment = liveData.adjustments.get(teamId);
    
    if (!adjustment) {
      adjustment = {
        teamId,
        goalsFor: 0,
        goalsAgainst: 0,
        points: 0,
        wins: 0,
        draws: 0,
        losses: 0,
        matchesPlayed: 0,
        liveMatches: [],
        goalsDiff: 0
      };
      liveData.adjustments.set(teamId, adjustment);
    }

    // Find existing live match or create new one
    let liveMatchIndex = adjustment.liveMatches.findIndex(m => m.fixtureId === fixtureId);
    let liveMatch: LiveMatchInfo;

    if (liveMatchIndex >= 0) {
      liveMatch = adjustment.liveMatches[liveMatchIndex];
      // Remove previous contribution from totals
      this.removeMatchContribution(adjustment, liveMatch);
    } else {
      liveMatch = {
        fixtureId,
        opponent: opponentId,
        homeAway,
        currentScore: { home: 0, away: 0 },
        originalScore: { home: 0, away: 0 },
        status: '',
        minute: 0,
        isFinished: false
      };
      adjustment.liveMatches.push(liveMatch);
      liveMatchIndex = adjustment.liveMatches.length - 1;
    }

    // Update live match data
    liveMatch.currentScore = { ...currentScore };
    liveMatch.originalScore = { ...originalScore };
    liveMatch.status = status;
    liveMatch.minute = minute;
    liveMatch.isFinished = isFinished;

    // Add new contribution to totals
    this.addMatchContribution(adjustment, liveMatch);

    // If match is finished, remove from live matches
    if (isFinished) {
      adjustment.liveMatches.splice(liveMatchIndex, 1);
    }
  }

  /**
   * Remove match contribution from adjustment totals
   */
  private removeMatchContribution(adjustment: LiveStandingAdjustment, liveMatch: LiveMatchInfo): void {
    const teamGoals = liveMatch.homeAway === 'home' ? liveMatch.currentScore.home : liveMatch.currentScore.away;
    const opponentGoals = liveMatch.homeAway === 'home' ? liveMatch.currentScore.away : liveMatch.currentScore.home;
    
    const originalTeamGoals = liveMatch.homeAway === 'home' ? liveMatch.originalScore.home : liveMatch.originalScore.away;
    const originalOpponentGoals = liveMatch.homeAway === 'home' ? liveMatch.originalScore.away : liveMatch.originalScore.home;

    // Remove goal contributions
    adjustment.goalsFor -= (teamGoals - originalTeamGoals);
    adjustment.goalsAgainst -= (opponentGoals - originalOpponentGoals);
    adjustment.goalsDiff -= ((teamGoals - originalTeamGoals) - (opponentGoals - originalOpponentGoals));

    // Remove match result contributions (for both live and finished matches)
    adjustment.matchesPlayed -= 1;

    if (teamGoals > opponentGoals) {
      adjustment.wins -= 1;
      adjustment.points -= 3;
    } else if (teamGoals === opponentGoals) {
      adjustment.draws -= 1;
      adjustment.points -= 1;
    } else {
      adjustment.losses -= 1;
    }
  }

  /**
   * Add match contribution to adjustment totals
   */
  private addMatchContribution(adjustment: LiveStandingAdjustment, liveMatch: LiveMatchInfo): void {
    const teamGoals = liveMatch.homeAway === 'home' ? liveMatch.currentScore.home : liveMatch.currentScore.away;
    const opponentGoals = liveMatch.homeAway === 'home' ? liveMatch.currentScore.away : liveMatch.currentScore.home;

    const originalTeamGoals = liveMatch.homeAway === 'home' ? liveMatch.originalScore.home : liveMatch.originalScore.away;
    const originalOpponentGoals = liveMatch.homeAway === 'home' ? liveMatch.originalScore.away : liveMatch.originalScore.home;

    // Add goal contributions
    adjustment.goalsFor += (teamGoals - originalTeamGoals);
    adjustment.goalsAgainst += (opponentGoals - originalOpponentGoals);
    adjustment.goalsDiff += ((teamGoals - originalTeamGoals) - (opponentGoals - originalOpponentGoals));

    // Add match result contributions
    if (liveMatch.isFinished) {
      // Final result for finished matches
      adjustment.matchesPlayed += 1;

      if (teamGoals > opponentGoals) {
        adjustment.wins += 1;
        adjustment.points += 3;
      } else if (teamGoals === opponentGoals) {
        adjustment.draws += 1;
        adjustment.points += 1;
      } else {
        adjustment.losses += 1;
      }
    } else {
      // Temporary result for live matches
      adjustment.matchesPlayed += 1;

      if (teamGoals > opponentGoals) {
        adjustment.wins += 1;
        adjustment.points += 3;
      } else if (teamGoals === opponentGoals) {
        adjustment.draws += 1;
        adjustment.points += 1;
      } else {
        adjustment.losses += 1;
      }
    }
  }

  /**
   * Get live standings adjustments for a league
   */
  async getLiveStandingsAdjustments(leagueId: number, season: number): Promise<Map<number, LiveStandingAdjustment>> {
    const liveData = await this.initializeLiveStandings(leagueId, season);
    return liveData.adjustments;
  }

  /**
   * Clear live standings data for a league (useful for testing or cleanup)
   */
  async clearLiveStandings(leagueId: number, season: number): Promise<void> {
    const cacheKey = this.getLiveStandingsCacheKey(leagueId, season);
    liveStandingsCache.delete(cacheKey);
    
    // Also clear from Redis if we decide to persist there
    await this.redisClient.del(`live_standings:${leagueId}:${season}`);
  }

  /**
   * Broadcast live standings update to connected clients
   */
  private async broadcastLiveStandingsUpdate(leagueId: number, season: number): Promise<void> {
    try {
      const adjustments = await this.getLiveStandingsAdjustments(leagueId, season);
      
      // Convert Map to object for JSON serialization
      const adjustmentsObj: { [key: number]: LiveStandingAdjustment } = {};
      adjustments.forEach((value, key) => {
        adjustmentsObj[key] = value;
      });

      // Broadcast to all clients subscribed to this league
      this.io.of('/fixtures').to(`league:${leagueId}`).emit('live-standings-update', {
        leagueId,
        season,
        adjustments: adjustmentsObj,
        timestamp: new Date().toISOString()
      });

      console.log(`Broadcasted live standings update for league ${leagueId}`);
    } catch (error) {
      console.error('Error broadcasting live standings update:', error);
    }
  }

  /**
   * Get teams currently playing live matches in a league
   */
  async getLiveMatchTeams(leagueId: number, season: number): Promise<number[]> {
    const liveData = await this.initializeLiveStandings(leagueId, season);
    const liveTeams = new Set<number>();
    
    liveData.adjustments.forEach((adjustment) => {
      if (adjustment.liveMatches.length > 0) {
        liveTeams.add(adjustment.teamId);
      }
    });
    
    return Array.from(liveTeams);
  }
}

// Export singleton instance
let liveStandingsServiceInstance: LiveStandingsService | null = null;

export function initializeLiveStandingsService(io: SocketIOServer): LiveStandingsService {
  if (!liveStandingsServiceInstance) {
    liveStandingsServiceInstance = new LiveStandingsService(io);
  }
  return liveStandingsServiceInstance;
}

export function getLiveStandingsService(): LiveStandingsService | null {
  return liveStandingsServiceInstance;
}
