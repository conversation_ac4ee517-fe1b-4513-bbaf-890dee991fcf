import { fetchData } from './apiFootball';
import { BookmakerOdds, Bookmaker, Bet } from '../models/Odds';

// Define the odds response structure from the API
export interface OddsResponse {
    league: {
        id: number;
        name?: string;
        country?: string;
        logo?: string;
        flag?: string;
        season?: number;
    };
    fixture: {
        id: number;
        timezone?: string;
        date?: string;
        timestamp?: number;
        status?: {
            long?: string;
            elapsed?: number;
            seconds?: string;
        };
    };
    teams?: {
        home?: {
            id: number;
            goals?: number;
        };
        away?: {
            id: number;
            goals?: number;
        };
    };
    status?: {
        stopped?: boolean;
        blocked?: boolean;
        finished?: boolean;
    };
    update: string; // ISO date string
    // For pre-match odds
    bookmakers?: BookmakerOdds[];
    // For live odds
    odds?: {
        id: number;
        name: string;
        values: {
            value: string;
            odd: string;
            handicap?: string;
            main?: boolean | null;
            suspended?: boolean;
        }[];
    }[];
}

// Define the bookmaker response structure from the API
export interface BookmakerResponse {
    id: number;
    name: string;
}

// Define the bet response structure from the API
export interface BetResponse {
    id: number;
    name: string;
}

// Define allowed query parameters for pre-match odds
interface FetchOddsParams {
    fixture?: number;
    league?: number;
    season?: number;
    date?: string;
    timezone?: string;
    page?: number;
    bookmaker?: number;
    bet?: number;
}

// Define allowed query parameters for live odds
interface FetchLiveOddsParams {
    fixture?: number;
    league?: number;
    bet?: number;
}

// Define allowed query parameters for bookmakers
interface FetchBookmakersParams {
    id?: number;
    search?: string;
}

// Define allowed query parameters for bets
interface FetchBetsParams {
    id?: number;
    search?: string;
}

// Define allowed query parameters for odds mapping
interface FetchOddsMappingParams {
    page?: number;
}

// Fetch pre-match odds
export async function fetchOdds(params: FetchOddsParams): Promise<OddsResponse[]> {
    // Validate parameters
    if (!params.fixture && !params.league && !params.date) {
        throw new Error("At least one parameter (fixture, league, or date) is required.");
    }

    // Season is required if league is provided
    if (params.league && !params.season) {
        throw new Error("Parameter 'season' is required when using 'league'.");
    }

    return fetchData<OddsResponse[]>('/odds', params);
}

// Fetch live odds
export async function fetchLiveOdds(params?: FetchLiveOddsParams): Promise<OddsResponse[]> {
    return fetchData<OddsResponse[]>('/odds/live', params);
}

// Fetch bookmakers
export async function fetchBookmakers(params?: FetchBookmakersParams): Promise<BookmakerResponse[]> {
    return fetchData<BookmakerResponse[]>('/odds/bookmakers', params);
}

// Fetch bet types
export async function fetchBets(params?: FetchBetsParams): Promise<BetResponse[]> {
    return fetchData<BetResponse[]>('/odds/bets', params);
}

// Fetch live bet types
export async function fetchLiveBets(params?: FetchBetsParams): Promise<BetResponse[]> {
    return fetchData<BetResponse[]>('/odds/live/bets', params);
}

// Fetch odds mapping
export async function fetchOddsMapping(params?: FetchOddsMappingParams): Promise<number[]> {
    return fetchData<number[]>('/odds/mapping', params);
}
