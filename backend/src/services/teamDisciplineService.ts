/**
 * Team Discipline Service
 * 
 * Calculates team card discipline using historical data
 * Similar to corner strength approach but adapted for card statistics
 */

import { 
  TeamCardDiscipline, 
  CardStatistics, 
  LeagueCardParameters,
  TeamCardDisciplineDocument 
} from '../models/CardPrediction';
import { CardDataService } from './cardDataService';
import connectDB from '../config/database';

export class TeamDisciplineService {
  private static readonly MIN_MATCHES_FOR_DISCIPLINE = 5; // Minimum matches for reliable card discipline calculation
  private static readonly CACHE_TTL_HOURS = 6;
  private static readonly HISTORICAL_MONTHS = 18;
  
  /**
   * Calculate team card discipline for a league
   */
  static async calculateTeamCardDiscipline(
    leagueId: number,
    cutoffDate: Date,
    currentDate: Date = new Date()
  ): Promise<Map<number, TeamCardDiscipline>> {
    try {
      // Check if league supports card statistics
      const hasSupport = await CardDataService.checkLeagueCardSupport(leagueId);
      if (!hasSupport) {
        console.log(`League ${leagueId} does not support card statistics`);
        return new Map();
      }
      
      // Get card data for the league
      const cardData = await CardDataService.getLeagueCardData(
        leagueId,
        cutoffDate,
        currentDate
      );
      
      if (cardData.length < this.MIN_MATCHES_FOR_DISCIPLINE) {
        console.warn(`Insufficient card data for league ${leagueId}: ${cardData.length} matches`);
        return new Map();
      }
      
      // Calculate league parameters
      const leagueParams = CardDataService.calculateLeagueCardParameters(cardData);
      
      // Get unique teams
      const teamIds = new Set<number>();
      cardData.forEach(match => {
        teamIds.add(match.homeTeamId);
        teamIds.add(match.awayTeamId);
      });
      
      const teamDiscipline = new Map<number, TeamCardDiscipline>();
      
      // Calculate discipline for each team
      for (const teamId of teamIds) {
        const discipline = this.calculateTeamDiscipline(
          teamId,
          cardData,
          leagueParams
        );
        
        if (discipline && discipline.matchCount >= this.MIN_MATCHES_FOR_DISCIPLINE) {
          teamDiscipline.set(teamId, discipline);
        }
      }
      
      console.log(`Calculated card discipline for ${teamDiscipline.size} teams in league ${leagueId}`);
      return teamDiscipline;
      
    } catch (error) {
      console.error(`Error calculating team card discipline for league ${leagueId}:`, error);
      return new Map();
    }
  }
  
  /**
   * Calculate card discipline for a specific team
   */
  private static calculateTeamDiscipline(
    teamId: number,
    cardData: CardStatistics[],
    leagueParams: LeagueCardParameters
  ): TeamCardDiscipline | null {
    try {
      const teamStats = CardDataService.getTeamCardStats(cardData, teamId);
      
      if (teamStats.matchesPlayed < this.MIN_MATCHES_FOR_DISCIPLINE) {
        return null;
      }
      
      // Calculate basic averages
      const avgCardsReceived = teamStats.cardsReceived.reduce((sum, cards) => sum + cards, 0) / teamStats.cardsReceived.length;
      const avgCardsCaused = teamStats.cardsCaused.reduce((sum, cards) => sum + cards, 0) / teamStats.cardsCaused.length;
      const avgYellowCards = teamStats.yellowCardsReceived.reduce((sum, cards) => sum + cards, 0) / teamStats.yellowCardsReceived.length;
      const avgRedCards = teamStats.redCardsReceived.reduce((sum, cards) => sum + cards, 0) / teamStats.redCardsReceived.length;
      
      // Calculate home/away card penalties
      let homeCardPenalty = 1.0; // Default
      let awayCardPenalty = 1.2; // Default
      
      if (teamStats.homeCardsReceived.length > 0 && teamStats.awayCardsReceived.length > 0) {
        const avgHomeCards = teamStats.homeCardsReceived.reduce((sum, cards) => sum + cards, 0) / teamStats.homeCardsReceived.length;
        const avgAwayCards = teamStats.awayCardsReceived.reduce((sum, cards) => sum + cards, 0) / teamStats.awayCardsReceived.length;
        
        if (avgCardsReceived > 0) {
          homeCardPenalty = avgHomeCards / avgCardsReceived;
          awayCardPenalty = avgAwayCards / avgCardsReceived;
          
          // Cap the penalties between reasonable bounds
          homeCardPenalty = Math.min(Math.max(homeCardPenalty, 0.7), 1.3);
          awayCardPenalty = Math.min(Math.max(awayCardPenalty, 0.7), 1.5);
        }
      }
      
      // Normalize discipline relative to league average
      const cardAttack = avgCardsReceived / (leagueParams.averageCards / 2);
      const cardDefense = avgCardsCaused / (leagueParams.averageCards / 2);
      
      // Calculate confidence based on data quality
      const confidence = this.calculateConfidence(
        teamStats.matchesPlayed,
        this.calculateDataQuality(teamStats.cardsReceived, teamStats.cardsCaused)
      );
      
      return {
        teamId,
        leagueId: leagueParams.leagueId,
        cardAttack,
        cardDefense,
        homeCardPenalty,
        awayCardPenalty,
        yellowCardRate: avgYellowCards,
        redCardRate: avgRedCards,
        matchCount: teamStats.matchesPlayed,
        confidence,
        lastUpdated: new Date()
      };
      
    } catch (error) {
      console.error(`Error calculating card discipline for team ${teamId}:`, error);
      return null;
    }
  }
  
  /**
   * Calculate data quality score based on consistency
   */
  private static calculateDataQuality(cardsReceived: number[], cardsCaused: number[]): number {
    if (cardsReceived.length === 0) return 0;
    
    // Calculate coefficient of variation for cards received and caused
    const avgReceived = cardsReceived.reduce((sum, cards) => sum + cards, 0) / cardsReceived.length;
    const avgCaused = cardsCaused.reduce((sum, cards) => sum + cards, 0) / cardsCaused.length;
    
    const varianceReceived = cardsReceived.reduce((sum, cards) => sum + Math.pow(cards - avgReceived, 2), 0) / cardsReceived.length;
    const varianceCaused = cardsCaused.reduce((sum, cards) => sum + Math.pow(cards - avgCaused, 2), 0) / cardsCaused.length;
    
    const cvReceived = avgReceived > 0 ? Math.sqrt(varianceReceived) / avgReceived : 1;
    const cvCaused = avgCaused > 0 ? Math.sqrt(varianceCaused) / avgCaused : 1;
    
    // Lower coefficient of variation = higher quality (more consistent)
    const avgCV = (cvReceived + cvCaused) / 2;
    return Math.max(0, Math.min(1, 1 - (avgCV - 0.4) / 0.8)); // Normalize to 0-1
  }
  
  /**
   * Calculate confidence score
   */
  private static calculateConfidence(matchCount: number, dataQuality: number): number {
    const matchFactor = Math.min(matchCount / (this.MIN_MATCHES_FOR_DISCIPLINE * 2), 1);
    return matchFactor * dataQuality;
  }
  
  /**
   * Get cached team card discipline
   */
  static async getCachedTeamCardDiscipline(leagueId: number): Promise<Map<number, TeamCardDiscipline> | null> {
    try {
      const db = await connectDB();
      
      const docs = await db.collection<TeamCardDisciplineDocument>('team_card_discipline')
        .find({ leagueId })
        .toArray();
      
      if (docs.length === 0) return null;
      
      // Check if data is stale
      const latestUpdate = Math.max(...docs.map(doc => doc.lastUpdated.getTime()));
      const isStale = this.isDataStale(new Date(latestUpdate), this.CACHE_TTL_HOURS);
      
      if (isStale) return null;
      
      const discipline = new Map<number, TeamCardDiscipline>();
      for (const doc of docs) {
        discipline.set(doc.teamId, {
          teamId: doc.teamId,
          leagueId: doc.leagueId,
          cardAttack: doc.discipline.cardAttack,
          cardDefense: doc.discipline.cardDefense,
          homeCardPenalty: doc.discipline.homeCardPenalty,
          awayCardPenalty: doc.discipline.awayCardPenalty,
          yellowCardRate: doc.discipline.yellowCardRate,
          redCardRate: doc.discipline.redCardRate,
          matchCount: doc.statistics.matchesPlayed,
          confidence: doc.statistics.matchesPlayed >= this.MIN_MATCHES_FOR_DISCIPLINE ? 0.8 : 0.5,
          lastUpdated: doc.lastUpdated
        });
      }
      
      return discipline;
      
    } catch (error) {
      console.error(`Error getting cached card discipline for league ${leagueId}:`, error);
      return null;
    }
  }
  
  /**
   * Cache team card discipline
   */
  static async cacheTeamCardDiscipline(
    teamDiscipline: Map<number, TeamCardDiscipline>,
    leagueId: number
  ): Promise<void> {
    try {
      const db = await connectDB();
      const collection = db.collection('team_card_discipline');
      
      const bulkOps = [];
      for (const [teamId, discipline] of teamDiscipline) {
        const doc: TeamCardDisciplineDocument = {
          _id: `${teamId}_${leagueId}`,
          teamId,
          leagueId,
          discipline: {
            cardAttack: discipline.cardAttack,
            cardDefense: discipline.cardDefense,
            homeCardPenalty: discipline.homeCardPenalty,
            awayCardPenalty: discipline.awayCardPenalty,
            yellowCardRate: discipline.yellowCardRate,
            redCardRate: discipline.redCardRate
          },
          statistics: {
            matchesPlayed: discipline.matchCount,
            cardsReceived: 0, // TODO: Calculate from historical data
            cardsCaused: 0, // TODO: Calculate from historical data
            homeMatchesPlayed: 0, // TODO: Calculate from historical data
            awayMatchesPlayed: 0, // TODO: Calculate from historical data
            averageCardsHome: 0, // TODO: Calculate from historical data
            averageCardsAway: 0, // TODO: Calculate from historical data
            yellowCardsReceived: Math.round(discipline.yellowCardRate * discipline.matchCount),
            redCardsReceived: Math.round(discipline.redCardRate * discipline.matchCount)
          },
          form: {
            last5Matches: [], // TODO: Calculate from historical data
            recentCardForm: 0 // TODO: Calculate from historical data
          },
          lastUpdated: new Date(),
          calculatedAt: new Date()
        };
        
        bulkOps.push({
          updateOne: {
            filter: { _id: doc._id },
            update: { $set: doc },
            upsert: true
          }
        });
      }
      
      if (bulkOps.length > 0) {
        await collection.bulkWrite(bulkOps);
        console.log(`Cached card discipline for ${bulkOps.length} teams in league ${leagueId}`);
      }
      
    } catch (error) {
      console.error(`Error caching team card discipline for league ${leagueId}:`, error);
    }
  }
  
  /**
   * Get or calculate team card discipline with caching
   */
  static async getTeamCardDiscipline(
    leagueId: number,
    currentDate: Date = new Date()
  ): Promise<Map<number, TeamCardDiscipline>> {
    try {
      // Try to get cached data first
      const cached = await this.getCachedTeamCardDiscipline(leagueId);
      if (cached && cached.size > 0) {
        console.log(`Using cached card discipline for league ${leagueId}: ${cached.size} teams`);
        return cached;
      }
      
      // Calculate fresh data
      const cutoffDate = new Date(currentDate);
      cutoffDate.setMonth(cutoffDate.getMonth() - this.HISTORICAL_MONTHS);
      
      const discipline = await this.calculateTeamCardDiscipline(
        leagueId,
        cutoffDate,
        currentDate
      );
      
      // Cache the results
      if (discipline.size > 0) {
        await this.cacheTeamCardDiscipline(discipline, leagueId);
      }
      
      return discipline;
      
    } catch (error) {
      console.error(`Error getting team card discipline for league ${leagueId}:`, error);
      return new Map();
    }
  }
  
  /**
   * Check if cached data is stale
   */
  private static isDataStale(lastUpdated: Date, maxAgeHours: number): boolean {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    return (Date.now() - lastUpdated.getTime()) > maxAge;
  }
}
