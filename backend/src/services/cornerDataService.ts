/**
 * Corner Data Service
 * 
 * Handles extraction and processing of corner statistics from fixture data
 * Only processes leagues that have statistics_fixtures: true coverage
 */

import { CornerStatistics, LeagueCornerParameters } from '../models/CornerPrediction';
import connectDB from '../config/database';

export class CornerDataService {
  private static readonly SUPPORTED_MATCH_STATUSES = ['FT', 'AET', 'PEN'];
  
  /**
   * Extract corner statistics from a fixture document
   */
  static extractCornerData(fixture: any): CornerStatistics | null {
    try {
      // Validate fixture has required data
      if (!fixture.statistics || fixture.statistics.length < 2) {
        return null;
      }
      
      if (!this.SUPPORTED_MATCH_STATUSES.includes(fixture.fixture?.status?.short)) {
        return null;
      }
      
      const homeStats = fixture.statistics[0]?.statistics || [];
      const awayStats = fixture.statistics[1]?.statistics || [];
      
      // Find corner statistics
      const homeCornerStat = homeStats.find((s: any) => s.type === "Corner Kicks");
      const awayCornerStat = awayStats.find((s: any) => s.type === "Corner Kicks");
      
      if (!homeCornerStat?.value || !awayCornerStat?.value) {
        return null;
      }
      
      const homeCorners = parseInt(homeCornerStat.value);
      const awayCorners = parseInt(awayCornerStat.value);
      
      // Validate corner counts are reasonable
      if (isNaN(homeCorners) || isNaN(awayCorners) || 
          homeCorners < 0 || awayCorners < 0 || 
          homeCorners > 25 || awayCorners > 25) {
        return null;
      }
      
      return {
        fixtureId: fixture.fixture.id,
        homeCorners,
        awayCorners,
        totalCorners: homeCorners + awayCorners,
        homeTeamId: fixture.teams.home.id,
        awayTeamId: fixture.teams.away.id,
        leagueId: fixture.league.id,
        date: new Date(fixture.date)
      };
      
    } catch (error) {
      console.error('Error extracting corner data from fixture:', error);
      return null;
    }
  }
  
  /**
   * Get all fixtures with corner data for a specific league
   */
  static async getLeagueCornerData(
    leagueId: number,
    cutoffDate: Date,
    currentDate: Date = new Date()
  ): Promise<CornerStatistics[]> {
    try {
      const db = await connectDB();
      
      const fixtures = await db.collection('fixtures').find({
        'league.id': leagueId,
        'fixture.status.short': { $in: this.SUPPORTED_MATCH_STATUSES },
        'date': { $gte: cutoffDate, $lte: currentDate },
        'statistics.statistics.type': 'Corner Kicks'
      }).toArray();
      
      const cornerData: CornerStatistics[] = [];
      
      for (const fixture of fixtures) {
        const data = this.extractCornerData(fixture);
        if (data) {
          cornerData.push(data);
        }
      }
      
      console.log(`Extracted corner data for league ${leagueId}: ${cornerData.length} matches`);
      return cornerData;
      
    } catch (error) {
      console.error(`Error getting corner data for league ${leagueId}:`, error);
      return [];
    }
  }
  
  /**
   * Check if a league has corner statistics support
   */
  static async checkLeagueCornerSupport(leagueId: number): Promise<boolean> {
    try {
      const db = await connectDB();
      
      // Check if league has statistics_fixtures: true in any recent season
      const league = await db.collection('leagues').findOne({
        'league.id': leagueId,
        'seasons.coverage.fixtures.statistics_fixtures': true,
        'seasons.year': { $gte: 2024 }
      });
      
      return !!league;
      
    } catch (error) {
      console.error(`Error checking corner support for league ${leagueId}:`, error);
      return false;
    }
  }
  
  /**
   * Get all leagues that support corner statistics
   */
  static async getLeaguesWithCornerSupport(): Promise<number[]> {
    try {
      const db = await connectDB();
      
      const leagues = await db.collection('leagues').find({
        'seasons.coverage.fixtures.statistics_fixtures': true,
        'seasons.year': { $gte: 2024 }
      }).toArray();
      
      const supportedLeagueIds = leagues.map(league => league.league.id);
      
      console.log(`Found ${supportedLeagueIds.length} leagues with corner support:`, supportedLeagueIds);
      return supportedLeagueIds;
      
    } catch (error) {
      console.error('Error getting leagues with corner support:', error);
      return [];
    }
  }
  
  /**
   * Calculate basic league corner parameters
   */
  static calculateLeagueCornerParameters(cornerData: CornerStatistics[]): LeagueCornerParameters {
    if (cornerData.length === 0) {
      return {
        leagueId: 0,
        averageCorners: 9.6, // Global average
        homeAdvantage: 1.2,  // Default home advantage
        varianceAdjustment: 1.0,
        matchesAnalyzed: 0,
        hasCornerSupport: false,
        lastUpdated: new Date()
      };
    }
    
    const leagueId = cornerData[0].leagueId;
    const totalCorners = cornerData.reduce((sum, match) => sum + match.totalCorners, 0);
    const homeCorners = cornerData.reduce((sum, match) => sum + match.homeCorners, 0);
    const awayCorners = cornerData.reduce((sum, match) => sum + match.awayCorners, 0);
    
    const averageCorners = totalCorners / cornerData.length;
    const averageHomeCorners = homeCorners / cornerData.length;
    const averageAwayCorners = awayCorners / cornerData.length;
    
    // Calculate home advantage (how much more corners home teams get on average)
    const homeAdvantage = averageAwayCorners > 0 ? averageHomeCorners / averageAwayCorners : 1.2;
    
    // Calculate variance for adjustment factor
    const cornerCounts = cornerData.map(match => match.totalCorners);
    const variance = this.calculateVariance(cornerCounts, averageCorners);
    const varianceAdjustment = Math.min(Math.max(variance / 10, 0.8), 1.2); // Normalize variance
    
    return {
      leagueId,
      averageCorners,
      homeAdvantage: Math.min(Math.max(homeAdvantage, 1.0), 1.5), // Cap between 1.0 and 1.5
      varianceAdjustment,
      matchesAnalyzed: cornerData.length,
      hasCornerSupport: true,
      lastUpdated: new Date()
    };
  }
  
  /**
   * Calculate variance of an array of numbers
   */
  private static calculateVariance(values: number[], mean: number): number {
    if (values.length === 0) return 0;
    
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    return squaredDifferences.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
  
  /**
   * Get corner statistics for a specific team in a league
   */
  static getTeamCornerStats(
    cornerData: CornerStatistics[],
    teamId: number
  ): {
    matchesPlayed: number;
    cornersFor: number[];
    cornersAgainst: number[];
    homeCornersFor: number[];
    awayCornersFor: number[];
  } {
    const stats = {
      matchesPlayed: 0,
      cornersFor: [] as number[],
      cornersAgainst: [] as number[],
      homeCornersFor: [] as number[],
      awayCornersFor: [] as number[]
    };
    
    for (const match of cornerData) {
      if (match.homeTeamId === teamId) {
        // Team playing at home
        stats.matchesPlayed++;
        stats.cornersFor.push(match.homeCorners);
        stats.cornersAgainst.push(match.awayCorners);
        stats.homeCornersFor.push(match.homeCorners);
      } else if (match.awayTeamId === teamId) {
        // Team playing away
        stats.matchesPlayed++;
        stats.cornersFor.push(match.awayCorners);
        stats.cornersAgainst.push(match.homeCorners);
        stats.awayCornersFor.push(match.awayCorners);
      }
    }
    
    return stats;
  }
  
  /**
   * Validate corner data quality
   */
  static validateCornerDataQuality(cornerData: CornerStatistics[]): {
    isValid: boolean;
    quality: 'low' | 'medium' | 'high';
    issues: string[];
  } {
    const issues: string[] = [];
    
    if (cornerData.length < 5) {
      issues.push('Insufficient data: less than 5 matches');
    }
    
    // Check for reasonable corner ranges
    const totalCorners = cornerData.map(match => match.totalCorners);
    const avgCorners = totalCorners.reduce((sum, corners) => sum + corners, 0) / totalCorners.length;
    
    if (avgCorners < 5 || avgCorners > 20) {
      issues.push(`Unusual average corners: ${avgCorners.toFixed(1)}`);
    }
    
    // Check for outliers
    const outliers = totalCorners.filter(corners => corners > 20 || corners < 2);
    if (outliers.length > cornerData.length * 0.1) {
      issues.push(`Too many outliers: ${outliers.length} matches`);
    }
    
    let quality: 'low' | 'medium' | 'high' = 'high';
    if (issues.length > 2 || cornerData.length < 10) {
      quality = 'low';
    } else if (issues.length > 0 || cornerData.length < 20) {
      quality = 'medium';
    }
    
    return {
      isValid: issues.length === 0 || (issues.length === 1 && cornerData.length >= 10),
      quality,
      issues
    };
  }
}
