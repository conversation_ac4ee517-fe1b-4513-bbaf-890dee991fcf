import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import config from '../config';
import { findUserById } from '../models/User';
import { createMessage, isChatOpen } from '../models/Message';
import { ObjectId } from 'mongodb';

// Define the structure of a socket with authentication data
interface AuthenticatedSocket {
  userId: string;
  userName: string;
  fixtureId?: number;
}

export function setupChatService(io: SocketIOServer): void {
  // Create a namespace for chat
  const chatNamespace = io.of('/chat');

  // Middleware to authenticate socket connections
  chatNamespace.use(async (socket, next) => {
    const token = socket.handshake.auth.token;

    if (!token) {
      return next(new Error('Authentication error: Token missing'));
    }

    try {
      // Verify the JWT token
      const decoded = jwt.verify(token, config.JWT_SECRET) as { id: string };

      // Find the user in the database
      const user = await findUserById(decoded.id);
      if (!user) {
        return next(new Error('Authentication error: User not found'));
      }

      // Attach user data to the socket
      (socket as any).userId = decoded.id;
      (socket as any).userName = user.name || 'Anonymous';

      next();
    } catch (error) {
      return next(new Error('Authentication error: Invalid token'));
    }
  });

  // Handle socket connections
  chatNamespace.on('connection', (socket) => {
    console.log(`User connected to chat: ${(socket as any).userId}`);

    // Handle joining a fixture chat room
    socket.on('join-fixture', async (fixtureId: number) => {
      try {
        // Check if chat is open for this fixture
        const chatOpen = await isChatOpen(fixtureId);
        if (!chatOpen) {
          socket.emit('error', { message: 'Chat is closed for this fixture' });
          return;
        }

        // Leave previous fixture room if any
        if ((socket as any).fixtureId) {
          socket.leave(`fixture:${(socket as any).fixtureId}`);
        }

        // Join the new fixture room
        socket.join(`fixture:${fixtureId}`);
        (socket as any).fixtureId = fixtureId;

        socket.emit('joined', { fixtureId });
        console.log(`User ${(socket as any).userId} joined fixture chat: ${fixtureId}`);
      } catch (error) {
        console.error('Error joining fixture chat:', error);
        socket.emit('error', { message: 'Failed to join fixture chat' });
      }
    });

    // Handle sending a message
    socket.on('send-message', async (data: { content: string }) => {
      try {
        const fixtureId = (socket as any).fixtureId;
        const userId = (socket as any).userId;
        const userName = (socket as any).userName;

        if (!fixtureId) {
          socket.emit('error', { message: 'You must join a fixture chat first' });
          return;
        }

        // Check if chat is open
        const chatOpen = await isChatOpen(fixtureId);
        if (!chatOpen) {
          socket.emit('error', { message: 'Chat is closed for this fixture' });
          return;
        }

        // Validate message content
        if (!data.content || data.content.trim() === '') {
          socket.emit('error', { message: 'Message cannot be empty' });
          return;
        }

        if (data.content.length > 500) {
          socket.emit('error', { message: 'Message is too long (max 500 characters)' });
          return;
        }

        // Create the message in the database
        const message = await createMessage({
          fixtureId,
          userId: new ObjectId(userId),
          userName,
          content: data.content.trim()
        });

        // Broadcast the message to all users in the fixture room
        chatNamespace.to(`fixture:${fixtureId}`).emit('new-message', {
          _id: message._id?.toString(),
          fixtureId,
          userId,
          userName,
          content: message.content,
          createdAt: message.createdAt
        });

        console.log(`Message sent in fixture ${fixtureId} by user ${userId}`);
      } catch (error) {
        console.error('Error sending message:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle leaving a fixture chat
    socket.on('leave-fixture', () => {
      const fixtureId = (socket as any).fixtureId;
      if (fixtureId) {
        socket.leave(`fixture:${fixtureId}`);
        (socket as any).fixtureId = undefined;
        console.log(`User ${(socket as any).userId} left fixture chat: ${fixtureId}`);
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`User disconnected: ${(socket as any).userId}`);
    });
  });

}

// Function to notify all clients that a chat is now open
export function notifyFixtureChatOpening(io: SocketIOServer, fixtureId: number): void {
  io.of('/chat').emit('chat-opened', {
    fixtureId,
    message: 'Chat is now open for this fixture'
  });
}

// Function to notify all clients in a fixture room that the chat is closing
export function notifyFixtureChatClosing(io: SocketIOServer, fixtureId: number): void {
  io.of('/chat').to(`fixture:${fixtureId}`).emit('chat-closing', {
    fixtureId,
    message: 'This chat will be closed in 1 hour'
  });
}

// Function to close a fixture chat and notify all clients
export function closeFixtureChat(io: SocketIOServer, fixtureId: number): void {
  io.of('/chat').to(`fixture:${fixtureId}`).emit('chat-closed', {
    fixtureId,
    message: 'This chat has been closed'
  });
}
