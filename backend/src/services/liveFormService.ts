import { Server as SocketIOServer } from 'socket.io';
import { getRedisClient } from '../config/redis';
import { getDb } from '../config/database';

// Interface for team form data
export interface TeamFormData {
  teamId: number;
  leagueId: number;
  season: number;
  form: string; // e.g., "WWLDW" (last 5 matches)
  recentMatches: FormMatch[];
  lastUpdated: Date;
}

// Interface for a form match
export interface FormMatch {
  fixtureId: number;
  date: Date;
  opponent: number;
  homeAway: 'home' | 'away';
  result: 'W' | 'D' | 'L';
  goalsFor: number;
  goalsAgainst: number;
  isLive?: boolean; // Indicates if this is a live/temporary result
}

// Interface for form update
export interface FormUpdate {
  teamId: number;
  leagueId: number;
  season: number;
  previousForm: string;
  newForm: string;
  newMatch: FormMatch;
  timestamp: Date;
}

/**
 * Live Form Service
 * Manages real-time team form updates when matches finish
 */
export class LiveFormService {
  private io: SocketIOServer;
  private redisClient = getRedisClient();
  private readonly CACHE_PREFIX = 'team_form:';
  private readonly CACHE_TTL = 60 * 60 * 24; // 24 hours

  constructor(io: SocketIOServer) {
    this.io = io;
  }

  /**
   * Get cache key for team form
   */
  private getCacheKey(teamId: number, leagueId: number, season: number): string {
    return `${this.CACHE_PREFIX}${teamId}:${leagueId}:${season}`;
  }

  /**
   * Get team form data
   */
  async getTeamForm(teamId: number, leagueId: number, season: number): Promise<TeamFormData | null> {
    try {
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      const formData = await this.redisClient.get(cacheKey);
      
      if (formData) {
        const parsed = JSON.parse(formData) as TeamFormData;
        // Convert date strings back to Date objects
        parsed.lastUpdated = new Date(parsed.lastUpdated);
        parsed.recentMatches = parsed.recentMatches.map(match => ({
          ...match,
          date: new Date(match.date)
        }));
        return parsed;
      }
      
      // If not in cache, fetch from database
      return await this.fetchTeamFormFromDatabase(teamId, leagueId, season);
    } catch (error) {
      console.error(`Error getting team form for team ${teamId}:`, error);
      return null;
    }
  }

  /**
   * Fetch team form from database
   */
  private async fetchTeamFormFromDatabase(teamId: number, leagueId: number, season: number): Promise<TeamFormData | null> {
    try {
      const db = getDb();
      const fixturesCollection = db.collection('fixtures');
      
      // Get last 5 completed matches for the team in this league/season
      const matches = await fixturesCollection.find({
        $and: [
          { 'league.id': leagueId },
          { 'league.season': season },
          { 'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] } },
          {
            $or: [
              { 'teams.home.id': teamId },
              { 'teams.away.id': teamId }
            ]
          }
        ]
      })
      .sort({ 'fixture.date': -1 })
      .limit(5)
      .toArray();

      if (matches.length === 0) {
        return null;
      }

      // Convert matches to form data
      const recentMatches: FormMatch[] = matches.map(match => {
        const isHome = match.teams.home.id === teamId;
        const goalsFor = isHome ? (match.goals?.home || 0) : (match.goals?.away || 0);
        const goalsAgainst = isHome ? (match.goals?.away || 0) : (match.goals?.home || 0);

        let result: 'W' | 'D' | 'L';
        if (goalsFor > goalsAgainst) {
          result = 'W';
        } else if (goalsFor === goalsAgainst) {
          result = 'D';
        } else {
          result = 'L';
        }

        return {
          fixtureId: Number(match._id),
          date: new Date(match.fixture.date),
          opponent: isHome ? match.teams.away.id : match.teams.home.id,
          homeAway: isHome ? 'home' : 'away',
          result,
          goalsFor,
          goalsAgainst,
          isLive: false
        };
      });

      // Generate form string (most recent first)
      const form = recentMatches.map(match => match.result).join('');

      const teamFormData: TeamFormData = {
        teamId,
        leagueId,
        season,
        form,
        recentMatches,
        lastUpdated: new Date()
      };

      // Cache the form data
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(teamFormData));

      return teamFormData;
    } catch (error) {
      console.error(`Error fetching team form from database for team ${teamId}:`, error);
      return null;
    }
  }

  /**
   * Update team form when a match finishes
   */
  async updateTeamFormOnMatchFinish(
    fixtureId: number,
    leagueId: number,
    season: number,
    homeTeamId: number,
    awayTeamId: number,
    finalScore: { home: number; away: number },
    matchDate: Date
  ): Promise<void> {
    try {
      // Update form for both teams
      await this.updateSingleTeamForm(
        fixtureId,
        leagueId,
        season,
        homeTeamId,
        awayTeamId,
        finalScore,
        matchDate,
        'home'
      );

      await this.updateSingleTeamForm(
        fixtureId,
        leagueId,
        season,
        awayTeamId,
        homeTeamId,
        finalScore,
        matchDate,
        'away'
      );

      console.log(`Form updated for both teams after match ${fixtureId}: ${homeTeamId} vs ${awayTeamId} (${finalScore.home}-${finalScore.away})`);
    } catch (error) {
      console.error(`Error updating team form for match ${fixtureId}:`, error);
    }
  }

  /**
   * Update form for a single team
   */
  private async updateSingleTeamForm(
    fixtureId: number,
    leagueId: number,
    season: number,
    teamId: number,
    opponentId: number,
    finalScore: { home: number; away: number },
    matchDate: Date,
    homeAway: 'home' | 'away'
  ): Promise<void> {
    try {
      // Get current form data
      let formData = await this.getTeamForm(teamId, leagueId, season);
      
      if (!formData) {
        // Initialize new form data
        formData = {
          teamId,
          leagueId,
          season,
          form: '',
          recentMatches: [],
          lastUpdated: new Date()
        };
      }

      const previousForm = formData.form;

      // Calculate result for this team
      const goalsFor = homeAway === 'home' ? finalScore.home : finalScore.away;
      const goalsAgainst = homeAway === 'home' ? finalScore.away : finalScore.home;
      
      let result: 'W' | 'D' | 'L';
      if (goalsFor > goalsAgainst) {
        result = 'W';
      } else if (goalsFor === goalsAgainst) {
        result = 'D';
      } else {
        result = 'L';
      }

      // Create new match entry
      const newMatch: FormMatch = {
        fixtureId,
        date: matchDate,
        opponent: opponentId,
        homeAway,
        result,
        goalsFor,
        goalsAgainst,
        isLive: false
      };

      // Add new match to the beginning (most recent first)
      formData.recentMatches.unshift(newMatch);

      // Keep only last 5 matches
      if (formData.recentMatches.length > 5) {
        formData.recentMatches = formData.recentMatches.slice(0, 5);
      }

      // Update form string
      formData.form = formData.recentMatches.map(match => match.result).join('');
      formData.lastUpdated = new Date();

      // Cache updated form data
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(formData));

      // Broadcast form update
      await this.broadcastFormUpdate({
        teamId,
        leagueId,
        season,
        previousForm,
        newForm: formData.form,
        newMatch,
        timestamp: new Date()
      });

      console.log(`Form updated for team ${teamId}: ${previousForm} -> ${formData.form}`);
    } catch (error) {
      console.error(`Error updating single team form for team ${teamId}:`, error);
    }
  }

  /**
   * Add temporary live result to team form (for live standings)
   */
  async addLiveResultToForm(
    teamId: number,
    leagueId: number,
    season: number,
    fixtureId: number,
    opponentId: number,
    currentScore: { home: number; away: number },
    homeAway: 'home' | 'away',
    matchDate: Date
  ): Promise<void> {
    try {
      // Get current form data
      let formData = await this.getTeamForm(teamId, leagueId, season);
      
      if (!formData) {
        return; // No existing form data
      }

      // Check if this live match is already in the form
      const existingLiveMatchIndex = formData.recentMatches.findIndex(
        match => match.fixtureId === fixtureId && match.isLive
      );

      // Calculate current result
      const goalsFor = homeAway === 'home' ? currentScore.home : currentScore.away;
      const goalsAgainst = homeAway === 'home' ? currentScore.away : currentScore.home;
      
      let result: 'W' | 'D' | 'L';
      if (goalsFor > goalsAgainst) {
        result = 'W';
      } else if (goalsFor === goalsAgainst) {
        result = 'D';
      } else {
        result = 'L';
      }

      const liveMatch: FormMatch = {
        fixtureId,
        date: matchDate,
        opponent: opponentId,
        homeAway,
        result,
        goalsFor,
        goalsAgainst,
        isLive: true
      };

      if (existingLiveMatchIndex >= 0) {
        // Update existing live match
        formData.recentMatches[existingLiveMatchIndex] = liveMatch;
      } else {
        // Add new live match at the beginning
        formData.recentMatches.unshift(liveMatch);
        // Keep only last 5 matches
        if (formData.recentMatches.length > 5) {
          formData.recentMatches = formData.recentMatches.slice(0, 5);
        }
      }

      // Update form string (including live results)
      formData.form = formData.recentMatches.map(match => match.result).join('');
      formData.lastUpdated = new Date();

      // Cache updated form data (temporarily)
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      await this.redisClient.setex(cacheKey, 300, JSON.stringify(formData)); // 5 minute cache for live data

      console.log(`Live result added to form for team ${teamId}: ${result} (${goalsFor}-${goalsAgainst})`);
    } catch (error) {
      console.error(`Error adding live result to form for team ${teamId}:`, error);
    }
  }

  /**
   * Remove live results from form (when match finishes)
   */
  async removeLiveResultsFromForm(teamId: number, leagueId: number, season: number): Promise<void> {
    try {
      let formData = await this.getTeamForm(teamId, leagueId, season);
      
      if (!formData) {
        return;
      }

      // Remove live matches
      formData.recentMatches = formData.recentMatches.filter(match => !match.isLive);
      
      // Update form string
      formData.form = formData.recentMatches.map(match => match.result).join('');
      formData.lastUpdated = new Date();

      // Cache updated form data
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(formData));

      console.log(`Live results removed from form for team ${teamId}`);
    } catch (error) {
      console.error(`Error removing live results from form for team ${teamId}:`, error);
    }
  }

  /**
   * Broadcast form update to connected clients
   */
  private async broadcastFormUpdate(update: FormUpdate): Promise<void> {
    try {
      // Broadcast to clients subscribed to this league
      this.io.of('/fixtures').to(`league:${update.leagueId}`).emit('team-form-update', update);
      
      console.log(`Broadcasted form update for team ${update.teamId}: ${update.previousForm} -> ${update.newForm}`);
    } catch (error) {
      console.error('Error broadcasting form update:', error);
    }
  }

  /**
   * Clear team form cache (useful for testing)
   */
  async clearTeamFormCache(teamId: number, leagueId: number, season: number): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(teamId, leagueId, season);
      await this.redisClient.del(cacheKey);
      console.log(`Form cache cleared for team ${teamId}`);
    } catch (error) {
      console.error(`Error clearing form cache for team ${teamId}:`, error);
    }
  }
}

// Export singleton instance
let liveFormServiceInstance: LiveFormService | null = null;

export function initializeLiveFormService(io: SocketIOServer): LiveFormService {
  if (!liveFormServiceInstance) {
    liveFormServiceInstance = new LiveFormService(io);
  }
  return liveFormServiceInstance;
}

export function getLiveFormService(): LiveFormService | null {
  return liveFormServiceInstance;
}
