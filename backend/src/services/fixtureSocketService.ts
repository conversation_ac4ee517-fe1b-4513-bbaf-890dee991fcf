import { Server as SocketIOServer } from 'socket.io';
import { Fixture } from '../models/Fixture';
import { processFixtureUpdate } from './eventDetectionService';
import { applyStatusMapping, applyStatusMappingToArray } from '../utils/statusMapping';
import { TeamRatingService, TeamRatings } from './teamRatingService';
import { getLiveStandingsService } from './liveStandingsService';
import { getLiveMatchStateService } from './liveMatchStateService';
import { getLiveFormService } from './liveFormService';

// Define the structure of a socket with fixture subscription data
interface FixtureSubscription {
  userId?: string;
  fixtureIds: Set<number>;
  leagueIds: Set<number>;
  isSubscribedToAllLive: boolean;
}

// Map to track socket subscriptions
const socketSubscriptions = new Map<string, FixtureSubscription>();

/**
 * Setup fixture socket service
 * This service handles real-time fixture updates
 */
export function setupFixtureSocketService(io: SocketIOServer): void {
  // Create a namespace for fixture updates
  const fixtureNamespace = io.of('/fixtures');

  // Handle socket connections
  fixtureNamespace.on('connection', (socket) => {
    console.log(`Client connected to fixture updates: ${socket.id}`);

    // Initialize subscription data for this socket
    socketSubscriptions.set(socket.id, {
      userId: (socket as any).userId, // May be undefined for unauthenticated users
      fixtureIds: new Set<number>(),
      leagueIds: new Set<number>(),
      isSubscribedToAllLive: false
    });

    // Handle subscribing to specific fixture updates
    socket.on('subscribe-fixture', (fixtureId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.fixtureIds.add(fixtureId);
          socket.join(`fixture:${fixtureId}`);
          console.log(`Client ${socket.id} subscribed to fixture: ${fixtureId}`);
          socket.emit('subscription-success', { type: 'fixture', id: fixtureId });
        }
      } catch (error) {
        console.error('Error subscribing to fixture:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to fixture' });
      }
    });

    // Handle subscribing to all fixtures in a league
    socket.on('subscribe-league', (leagueId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.leagueIds.add(leagueId);
          socket.join(`league:${leagueId}`);
          console.log(`Client ${socket.id} subscribed to league: ${leagueId}`);
          socket.emit('subscription-success', { type: 'league', id: leagueId });
        }
      } catch (error) {
        console.error('Error subscribing to league:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to league' });
      }
    });

    // Handle subscribing to all live fixtures
    socket.on('subscribe-live', () => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.isSubscribedToAllLive = true;
          socket.join('live-fixtures');
          console.log(`Client ${socket.id} subscribed to all live fixtures`);
          socket.emit('subscription-success', { type: 'live' });
        }
      } catch (error) {
        console.error('Error subscribing to live fixtures:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to live fixtures' });
      }
    });

    // Handle unsubscribing from specific fixture updates
    socket.on('unsubscribe-fixture', (fixtureId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.fixtureIds.delete(fixtureId);
          socket.leave(`fixture:${fixtureId}`);
          console.log(`Client ${socket.id} unsubscribed from fixture: ${fixtureId}`);
          socket.emit('unsubscription-success', { type: 'fixture', id: fixtureId });
        }
      } catch (error) {
        console.error('Error unsubscribing from fixture:', error);
        socket.emit('subscription-error', { message: 'Failed to unsubscribe from fixture' });
      }
    });

    // Handle unsubscribing from league updates
    socket.on('unsubscribe-league', (leagueId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.leagueIds.delete(leagueId);
          socket.leave(`league:${leagueId}`);
          console.log(`Client ${socket.id} unsubscribed from league: ${leagueId}`);
          socket.emit('unsubscription-success', { type: 'league', id: leagueId });
        }
      } catch (error) {
        console.error('Error unsubscribing from league:', error);
        socket.emit('subscription-error', { message: 'Failed to unsubscribe from league' });
      }
    });

    // Handle unsubscribing from all live fixtures
    socket.on('unsubscribe-live', () => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.isSubscribedToAllLive = false;
          socket.leave('live-fixtures');
          console.log(`Client ${socket.id} unsubscribed from all live fixtures`);
          socket.emit('unsubscription-success', { type: 'live' });
        }
      } catch (error) {
        console.error('Error unsubscribing from live fixtures:', error);
        socket.emit('subscription-error', { message: 'Failed to unsubscribe from live fixtures' });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`Client disconnected from fixture updates: ${socket.id}`);
      socketSubscriptions.delete(socket.id);
    });
  });
}

/**
 * Broadcast fixture updates to subscribed clients
 * @param io The Socket.IO server instance
 * @param fixtures The updated fixtures
 */
export function broadcastFixtureUpdates(io: SocketIOServer, fixtures: Fixture[]): void {
  if (!fixtures || fixtures.length === 0) {
    return;
  }

  console.log(`Broadcasting updates for ${fixtures.length} fixtures`);

  // Group fixtures by league for efficient broadcasting
  const fixturesByLeague = new Map<number, Fixture[]>();

  // Process each fixture
  fixtures.forEach(async fixture => {
    const fixtureId = fixture._id;
    const leagueId = fixture.league.id;

    // Add to league group
    if (!fixturesByLeague.has(leagueId)) {
      fixturesByLeague.set(leagueId, []);
    }
    fixturesByLeague.get(leagueId)?.push(fixture);

    // Apply status mapping before broadcasting
    const mappedFixture = applyStatusMapping(fixture);

    // Debug: Log what data is being broadcast
    console.log(`Broadcasting fixture ${fixtureId} with data:`, {
      hasEvents: !!(mappedFixture.events && mappedFixture.events.length > 0),
      hasStatistics: !!(mappedFixture.statistics && mappedFixture.statistics.length > 0),
      hasLineups: !!(mappedFixture.lineups && mappedFixture.lineups.length > 0),
      hasPlayers: !!(mappedFixture.players && mappedFixture.players.length > 0),
      eventsCount: mappedFixture.events?.length || 0,
      statisticsCount: mappedFixture.statistics?.length || 0,
      lineupsCount: mappedFixture.lineups?.length || 0,
      playersCount: mappedFixture.players?.length || 0
    });

    // Broadcast to specific fixture room
    io.of('/fixtures').to(`fixture:${fixtureId}`).emit('fixture-update', mappedFixture);

    // Calculate and broadcast team ratings if player data is available
    await calculateAndBroadcastTeamRatings(io, fixture);

    // Update live standings if fixture is live or finished
    await updateLiveStandingsForFixture(fixture);

    // Process fixture update for event detection and notifications
    await processFixtureUpdate(fixture);
  });

  // Broadcast to league rooms
  fixturesByLeague.forEach((leagueFixtures, leagueId) => {
    // Apply status mapping to all fixtures in this league
    const mappedLeagueFixtures = applyStatusMappingToArray(leagueFixtures);

    io.of('/fixtures').to(`league:${leagueId}`).emit('league-fixtures-update', {
      leagueId,
      fixtures: mappedLeagueFixtures
    });
  });

  // Check if any fixtures are live and broadcast to live subscribers
  const liveFixtures = fixtures.filter(fixture => {
    const status = fixture.fixture.status.short;
    const isLiveStatus = ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status);

    // Add date validation to prevent future fixtures from being considered live
    if (isLiveStatus) {
      const now = new Date();
      const maxLiveDate = new Date(now.getTime() + (24 * 60 * 60 * 1000)); // Allow up to 24 hours in future
      const fixtureTimestamp = fixture.fixture.timestamp * 1000; // Convert to milliseconds
      return fixtureTimestamp <= maxLiveDate.getTime();
    }

    return false;
  });

  if (liveFixtures.length > 0) {
    // Apply status mapping to all live fixtures
    const mappedLiveFixtures = applyStatusMappingToArray(liveFixtures);

    io.of('/fixtures').to('live-fixtures').emit('live-fixtures-update', mappedLiveFixtures);
  }
}

/**
 * Broadcast a status update for a specific fixture
 * @param io The Socket.IO server instance
 * @param fixtureId The fixture ID
 * @param status The new status
 */
export function broadcastFixtureStatusUpdate(
  io: SocketIOServer,
  fixtureId: number,
  status: { long: string; short: string; elapsed: number | null }
): void {
  // Apply status mapping
  const mappedStatus = { ...status };
  if (mappedStatus.short) {
    mappedStatus.short = mappedStatus.short === 'BT' ? 'Aw. ET' : mappedStatus.short;
  }

  io.of('/fixtures').to(`fixture:${fixtureId}`).emit('fixture-status-update', {
    fixtureId,
    status: mappedStatus
  });
}

/**
 * Broadcast a goal update for a specific fixture
 * @param io The Socket.IO server instance
 * @param fixtureId The fixture ID
 * @param goals The updated goals
 * @param event The goal event details (optional)
 */
export function broadcastFixtureGoalUpdate(
  io: SocketIOServer,
  fixtureId: number,
  goals: { home: number | null; away: number | null },
  event?: any
): void {
  io.of('/fixtures').to(`fixture:${fixtureId}`).emit('fixture-goal-update', {
    fixtureId,
    goals,
    event
  });

  // Also trigger live standings update for goal events
  triggerLiveStandingsUpdateForGoal(fixtureId, goals);
}

/**
 * Broadcast a new event for a specific fixture
 * @param io The Socket.IO server instance
 * @param fixtureId The fixture ID
 * @param event The event details
 */
export function broadcastFixtureEvent(
  io: SocketIOServer,
  fixtureId: number,
  event: any
): void {
  io.of('/fixtures').to(`fixture:${fixtureId}`).emit('fixture-event', {
    fixtureId,
    event
  });
}

/**
 * Broadcast team rating updates for a specific fixture
 * @param io The Socket.IO server instance
 * @param fixtureId The fixture ID
 * @param teamRatings The updated team ratings
 */
export function broadcastTeamRatingUpdate(
  io: SocketIOServer,
  fixtureId: number,
  teamRatings: TeamRatings
): void {
  io.of('/fixtures').to(`fixture:${fixtureId}`).emit('team-rating-update', {
    fixtureId,
    teamRatings,
    type: 'team-rating-update'
  });
}

/**
 * Calculate and broadcast team rating updates when fixture data changes
 * @param io The Socket.IO server instance
 * @param fixture The updated fixture with player data
 */
export async function calculateAndBroadcastTeamRatings(
  io: SocketIOServer,
  fixture: Fixture
): Promise<void> {
  try {
    // Only calculate team ratings if player data is available
    if (!fixture.players || fixture.players.length < 2) {
      return;
    }

    // Check if team ratings can be calculated
    if (!TeamRatingService.canCalculateRatings(fixture.players)) {
      return;
    }

    // Determine if this is a live match
    const isLive = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(fixture.fixture.status.short);

    // Calculate team ratings
    const teamRatings = await TeamRatingService.getTeamRatings(
      fixture._id,
      fixture.players,
      isLive
    );

    // Broadcast the team rating update
    broadcastTeamRatingUpdate(io, fixture._id, teamRatings);

    console.log(`Team ratings calculated and broadcasted for fixture ${fixture._id}: Home ${teamRatings.home.rating}, Away ${teamRatings.away.rating}`);

  } catch (error) {
    console.warn(`Failed to calculate and broadcast team ratings for fixture ${fixture._id}:`, error);
  }
}

/**
 * Update live standings for a fixture
 * @param fixture The fixture to update live standings for
 */
async function updateLiveStandingsForFixture(fixture: Fixture): Promise<void> {
  try {
    const liveStandingsService = getLiveStandingsService();
    const liveMatchStateService = getLiveMatchStateService();
    const liveFormService = getLiveFormService();

    if (!liveStandingsService || !liveMatchStateService) {
      return; // Services not initialized
    }

    const fixtureId = fixture._id;
    const leagueId = fixture.league.id;
    const season = fixture.league.season;
    const homeTeamId = fixture.teams.home.id;
    const awayTeamId = fixture.teams.away.id;
    const status = fixture.fixture.status.short;
    const minute = fixture.fixture.status.elapsed || 0;
    const matchDate = new Date(fixture.fixture.date);

    // Only update for live matches or recently finished matches
    const liveStatuses = ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'];
    const finishedStatuses = ['FT', 'AET', 'PEN'];

    if (!liveStatuses.includes(status) && !finishedStatuses.includes(status)) {
      return; // Not a live or finished match
    }

    // Get current score
    const currentScore = {
      home: fixture.goals?.home || 0,
      away: fixture.goals?.away || 0
    };

    // Initialize or update match state to track original vs current scores
    const matchState = await liveMatchStateService.initializeMatchState(
      fixtureId,
      leagueId,
      season,
      homeTeamId,
      awayTeamId,
      currentScore,
      status,
      minute
    );

    // Update live standings using the tracked original score
    await liveStandingsService.updateLiveMatch(
      leagueId,
      season,
      fixtureId,
      homeTeamId,
      awayTeamId,
      currentScore,
      matchState.originalScore,
      status,
      minute
    );

    // Handle form updates
    if (liveFormService) {
      if (finishedStatuses.includes(status)) {
        // Match finished - update official form
        await liveFormService.updateTeamFormOnMatchFinish(
          fixtureId,
          leagueId,
          season,
          homeTeamId,
          awayTeamId,
          currentScore,
          matchDate
        );
      } else if (liveStatuses.includes(status)) {
        // Match is live - add temporary form indicators
        await liveFormService.addLiveResultToForm(
          homeTeamId,
          leagueId,
          season,
          fixtureId,
          awayTeamId,
          currentScore,
          'home',
          matchDate
        );

        await liveFormService.addLiveResultToForm(
          awayTeamId,
          leagueId,
          season,
          fixtureId,
          homeTeamId,
          currentScore,
          'away',
          matchDate
        );
      }
    }

    console.log(`Live standings and form updated for fixture ${fixtureId}: ${homeTeamId} vs ${awayTeamId} (${currentScore.home}-${currentScore.away}, status: ${status})`);

  } catch (error) {
    console.error(`Failed to update live standings for fixture ${fixture._id}:`, error);
  }
}

/**
 * Trigger live standings update for a goal event
 * @param fixtureId The fixture ID where the goal occurred
 * @param goals The updated goals
 */
async function triggerLiveStandingsUpdateForGoal(
  fixtureId: number,
  goals: { home: number | null; away: number | null }
): Promise<void> {
  try {
    const liveStandingsService = getLiveStandingsService();
    if (!liveStandingsService) {
      return;
    }

    // Note: This is a simplified version. In a full implementation,
    // we would need to fetch the fixture data to get league, teams, etc.
    // For now, this serves as a placeholder for the goal-specific update logic
    console.log(`Goal update triggered live standings recalculation for fixture ${fixtureId}: ${goals.home}-${goals.away}`);

  } catch (error) {
    console.error(`Failed to trigger live standings update for goal in fixture ${fixtureId}:`, error);
  }
}
