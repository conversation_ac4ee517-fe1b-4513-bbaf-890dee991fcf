/**
 * Team Corner Strength Service
 * 
 * Calculates team corner strengths using historical data
 * Similar to Dixon-<PERSON> approach but adapted for corner statistics
 */

import { 
  TeamCornerStrength, 
  CornerStatistics, 
  LeagueCornerParameters,
  TeamCornerStrengthDocument 
} from '../models/CornerPrediction';
import { CornerDataService } from './cornerDataService';
import connectDB from '../config/database';

export class TeamCornerStrengthService {
  private static readonly MIN_MATCHES_FOR_STRENGTH = 5; // Minimum matches for reliable corner strength calculation
  private static readonly CACHE_TTL_HOURS = 6;
  private static readonly HISTORICAL_MONTHS = 18;
  
  /**
   * Calculate team corner strengths for a league
   */
  static async calculateTeamCornerStrengths(
    leagueId: number,
    cutoffDate: Date,
    currentDate: Date = new Date()
  ): Promise<Map<number, TeamCornerStrength>> {
    try {
      // Check if league supports corner statistics
      const hasSupport = await CornerDataService.checkLeagueCornerSupport(leagueId);
      if (!hasSupport) {
        console.log(`League ${leagueId} does not support corner statistics`);
        return new Map();
      }
      
      // Get corner data for the league
      const cornerData = await CornerDataService.getLeagueCornerData(
        leagueId,
        cutoffDate,
        currentDate
      );
      
      if (cornerData.length < this.MIN_MATCHES_FOR_STRENGTH) {
        console.warn(`Insufficient corner data for league ${leagueId}: ${cornerData.length} matches`);
        return new Map();
      }
      
      // Calculate league parameters
      const leagueParams = CornerDataService.calculateLeagueCornerParameters(cornerData);
      
      // Get unique teams
      const teamIds = new Set<number>();
      cornerData.forEach(match => {
        teamIds.add(match.homeTeamId);
        teamIds.add(match.awayTeamId);
      });
      
      const teamStrengths = new Map<number, TeamCornerStrength>();
      
      // Calculate strength for each team
      for (const teamId of teamIds) {
        const strength = this.calculateTeamStrength(
          teamId,
          cornerData,
          leagueParams
        );
        
        if (strength && strength.matchCount >= this.MIN_MATCHES_FOR_STRENGTH) {
          teamStrengths.set(teamId, strength);
        }
      }
      
      console.log(`Calculated corner strengths for ${teamStrengths.size} teams in league ${leagueId}`);
      return teamStrengths;
      
    } catch (error) {
      console.error(`Error calculating team corner strengths for league ${leagueId}:`, error);
      return new Map();
    }
  }
  
  /**
   * Calculate corner strength for a specific team
   */
  private static calculateTeamStrength(
    teamId: number,
    cornerData: CornerStatistics[],
    leagueParams: LeagueCornerParameters
  ): TeamCornerStrength | null {
    try {
      const teamStats = CornerDataService.getTeamCornerStats(cornerData, teamId);
      
      if (teamStats.matchesPlayed < this.MIN_MATCHES_FOR_STRENGTH) {
        return null;
      }
      
      // Calculate basic averages
      const avgCornersFor = teamStats.cornersFor.reduce((sum, corners) => sum + corners, 0) / teamStats.cornersFor.length;
      const avgCornersAgainst = teamStats.cornersAgainst.reduce((sum, corners) => sum + corners, 0) / teamStats.cornersAgainst.length;
      
      // Calculate home corner bonus
      let homeCornerBonus = 1.2; // Default
      if (teamStats.homeCornersFor.length > 0 && teamStats.awayCornersFor.length > 0) {
        const avgHomeCornersFor = teamStats.homeCornersFor.reduce((sum, corners) => sum + corners, 0) / teamStats.homeCornersFor.length;
        const avgAwayCornersFor = teamStats.awayCornersFor.reduce((sum, corners) => sum + corners, 0) / teamStats.awayCornersFor.length;
        
        if (avgAwayCornersFor > 0) {
          homeCornerBonus = avgHomeCornersFor / avgAwayCornersFor;
          // Cap the home bonus between 1.0 and 2.0
          homeCornerBonus = Math.min(Math.max(homeCornerBonus, 1.0), 2.0);
        }
      }
      
      // Normalize strengths relative to league average
      const cornerAttack = avgCornersFor / (leagueParams.averageCorners / 2);
      const cornerDefense = avgCornersAgainst / (leagueParams.averageCorners / 2);
      
      // Calculate confidence based on data quality
      const confidence = this.calculateConfidence(
        teamStats.matchesPlayed,
        this.calculateDataQuality(teamStats.cornersFor, teamStats.cornersAgainst)
      );
      
      return {
        teamId,
        leagueId: leagueParams.leagueId,
        cornerAttack,
        cornerDefense,
        homeCornerBonus,
        matchCount: teamStats.matchesPlayed,
        confidence,
        lastUpdated: new Date()
      };
      
    } catch (error) {
      console.error(`Error calculating corner strength for team ${teamId}:`, error);
      return null;
    }
  }
  
  /**
   * Calculate data quality score based on consistency
   */
  private static calculateDataQuality(cornersFor: number[], cornersAgainst: number[]): number {
    if (cornersFor.length === 0) return 0;
    
    // Calculate coefficient of variation for corners for and against
    const avgFor = cornersFor.reduce((sum, corners) => sum + corners, 0) / cornersFor.length;
    const avgAgainst = cornersAgainst.reduce((sum, corners) => sum + corners, 0) / cornersAgainst.length;
    
    const varianceFor = cornersFor.reduce((sum, corners) => sum + Math.pow(corners - avgFor, 2), 0) / cornersFor.length;
    const varianceAgainst = cornersAgainst.reduce((sum, corners) => sum + Math.pow(corners - avgAgainst, 2), 0) / cornersAgainst.length;
    
    const cvFor = avgFor > 0 ? Math.sqrt(varianceFor) / avgFor : 1;
    const cvAgainst = avgAgainst > 0 ? Math.sqrt(varianceAgainst) / avgAgainst : 1;
    
    // Lower coefficient of variation = higher quality (more consistent)
    const avgCV = (cvFor + cvAgainst) / 2;
    return Math.max(0, Math.min(1, 1 - (avgCV - 0.3) / 0.7)); // Normalize to 0-1
  }
  
  /**
   * Calculate confidence score
   */
  private static calculateConfidence(matchCount: number, dataQuality: number): number {
    const matchFactor = Math.min(matchCount / (this.MIN_MATCHES_FOR_STRENGTH * 2), 1);
    return matchFactor * dataQuality;
  }
  
  /**
   * Get cached team corner strengths
   */
  static async getCachedTeamCornerStrengths(leagueId: number): Promise<Map<number, TeamCornerStrength> | null> {
    try {
      const db = await connectDB();
      
      const docs = await db.collection<TeamCornerStrengthDocument>('team_corner_strengths')
        .find({ leagueId })
        .toArray();
      
      if (docs.length === 0) return null;
      
      // Check if data is stale
      const latestUpdate = Math.max(...docs.map(doc => doc.lastUpdated.getTime()));
      const isStale = this.isDataStale(new Date(latestUpdate), this.CACHE_TTL_HOURS);
      
      if (isStale) return null;
      
      const strengths = new Map<number, TeamCornerStrength>();
      for (const doc of docs) {
        strengths.set(doc.teamId, {
          teamId: doc.teamId,
          leagueId: doc.leagueId,
          cornerAttack: doc.strength.cornerAttack,
          cornerDefense: doc.strength.cornerDefense,
          homeCornerBonus: doc.strength.homeCornerBonus,
          matchCount: doc.statistics.matchesPlayed,
          confidence: doc.statistics.matchesPlayed >= this.MIN_MATCHES_FOR_STRENGTH ? 0.8 : 0.5,
          lastUpdated: doc.lastUpdated
        });
      }
      
      return strengths;
      
    } catch (error) {
      console.error(`Error getting cached corner strengths for league ${leagueId}:`, error);
      return null;
    }
  }
  
  /**
   * Cache team corner strengths
   */
  static async cacheTeamCornerStrengths(
    teamStrengths: Map<number, TeamCornerStrength>,
    leagueId: number
  ): Promise<void> {
    try {
      const db = await connectDB();
      const collection = db.collection('team_corner_strengths');
      
      const bulkOps = [];
      for (const [teamId, strength] of teamStrengths) {
        const doc: TeamCornerStrengthDocument = {
          _id: `${teamId}_${leagueId}`,
          teamId,
          leagueId,
          strength: {
            cornerAttack: strength.cornerAttack,
            cornerDefense: strength.cornerDefense,
            homeCornerBonus: strength.homeCornerBonus
          },
          statistics: {
            matchesPlayed: strength.matchCount,
            cornersWon: 0, // TODO: Calculate from historical data
            cornersConceded: 0, // TODO: Calculate from historical data
            homeMatchesPlayed: 0, // TODO: Calculate from historical data
            awayMatchesPlayed: 0, // TODO: Calculate from historical data
            averageCornersHome: 0, // TODO: Calculate from historical data
            averageCornersAway: 0 // TODO: Calculate from historical data
          },
          form: {
            last5Matches: [], // TODO: Calculate from historical data
            recentCornerForm: 0 // TODO: Calculate from historical data
          },
          lastUpdated: new Date(),
          calculatedAt: new Date()
        };
        
        bulkOps.push({
          updateOne: {
            filter: { _id: doc._id },
            update: { $set: doc },
            upsert: true
          }
        });
      }
      
      if (bulkOps.length > 0) {
        await collection.bulkWrite(bulkOps);
        console.log(`Cached corner strengths for ${bulkOps.length} teams in league ${leagueId}`);
      }
      
    } catch (error) {
      console.error(`Error caching team corner strengths for league ${leagueId}:`, error);
    }
  }
  
  /**
   * Get or calculate team corner strengths with caching
   */
  static async getTeamCornerStrengths(
    leagueId: number,
    currentDate: Date = new Date()
  ): Promise<Map<number, TeamCornerStrength>> {
    try {
      // Try to get cached data first
      const cached = await this.getCachedTeamCornerStrengths(leagueId);
      if (cached && cached.size > 0) {
        console.log(`Using cached corner strengths for league ${leagueId}: ${cached.size} teams`);
        return cached;
      }
      
      // Calculate fresh data
      const cutoffDate = new Date(currentDate);
      cutoffDate.setMonth(cutoffDate.getMonth() - this.HISTORICAL_MONTHS);
      
      const strengths = await this.calculateTeamCornerStrengths(
        leagueId,
        cutoffDate,
        currentDate
      );
      
      // Cache the results
      if (strengths.size > 0) {
        await this.cacheTeamCornerStrengths(strengths, leagueId);
      }
      
      return strengths;
      
    } catch (error) {
      console.error(`Error getting team corner strengths for league ${leagueId}:`, error);
      return new Map();
    }
  }
  
  /**
   * Check if cached data is stale
   */
  private static isDataStale(lastUpdated: Date, maxAgeHours: number): boolean {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    return (Date.now() - lastUpdated.getTime()) > maxAge;
  }
}
