/**
 * League Grouping Service
 * 
 * Groups 271 leagues into 8 logical groups for xi parameter optimization
 * Based on football characteristics rather than just popularity tiers
 */

export type LeagueGroup = 
  | 'elite_global'
  | 'big5_european' 
  | 'major_european'
  | 'emerging_americas'
  | 'asian_middle_east'
  | 'african_developing'
  | 'european_second_tier'
  | 'lower_regional';

export interface LeagueGroupInfo {
  name: string;
  description: string;
  characteristics: string;
  expectedXi: number;
  reasoning: string;
  leagues: number[];
}

export class LeagueGroupingService {
  
  /**
   * League group definitions with optimized xi values
   */
  private static readonly LEAGUE_GROUPS: Record<LeagueGroup, LeagueGroupInfo> = {
    elite_global: {
      name: 'Elite Global Competitions',
      description: 'World Cup, Euro, Champions League - highest stakes',
      characteristics: 'Maximum motivation, top teams, high pressure',
      expectedXi: 0.004, // Recent form very important
      reasoning: 'Elite competitions where recent form and momentum are crucial',
      leagues: [1, 8, 4, 2, 3, 5] // World Cup, Euro, Champions League, Europa League, Nations League
    },

    big5_european: {
      name: 'Big 5 European Leagues',
      description: 'Premier League, La Liga, Bundesliga, Serie A, Ligue 1',
      characteristics: 'Consistent quality, well-analyzed, high level',
      expectedXi: 0.0035, // Slightly higher than default
      reasoning: 'Top leagues where recent form matters but quality is consistent',
      leagues: [39, 140, 78, 135, 61] // Premier, La Liga, Bundesliga, Serie A, Ligue 1
    },

    major_european: {
      name: 'Major European Competitions',
      description: 'Strong European leagues, cups, and competitions',
      characteristics: 'Good quality, structured, competitive',
      expectedXi: 0.003, // Moderate recent form weighting
      reasoning: 'Strong leagues with good data quality and competitive balance',
      leagues: [
        // Major cups
        45, 143, 81, 137, 848, 66, 48,
        // Strong European leagues
        88, 94, 203, 144, 179, 218, 207, 197, 106, 119, 235, 286, 210, 333, 283, 271, 345, 332, 315,
        // European second tiers of major countries
        40, 79, 141, 136, 62
      ]
    },

    emerging_americas: {
      name: 'Emerging & South American Leagues',
      description: 'South American leagues and emerging competitions',
      characteristics: 'More volatile, tactical changes, economic factors',
      expectedXi: 0.0045, // Higher recent form weighting
      reasoning: 'More unpredictable leagues where recent form is very important',
      leagues: [
        // South American leagues
        71, 128, 239, 265, 281, 250, 252, 268, 270, 242, 129, 240, 266, 269, 243, 299, 344, 162, 396,
        // North American
        253, 262, 263,
        // South American cups
        9, 13, 11, 73, 130, 241, 267
      ]
    },

    asian_middle_east: {
      name: 'Asian & Middle Eastern Leagues',
      description: 'Asian and Middle Eastern competitions',
      characteristics: 'Different playing styles, climate factors, varying quality',
      expectedXi: 0.004, // Higher recent form weighting
      reasoning: 'Different football culture where recent adaptation is important',
      leagues: [
        // Asian leagues
        292, 98, 169, 323, 274, 278, 340, 296, 380, 290,
        // Middle Eastern
        307, 305, 301, 387, 389, 330, 390, 417, 398, 369, 589,
        // Asian competitions
        17, 1132, 7, 30
      ]
    },

    african_developing: {
      name: 'African & Developing Leagues',
      description: 'African leagues and other developing competitions',
      characteristics: 'Infrastructure challenges, player movement, varying quality',
      expectedXi: 0.005, // Highest recent form weighting
      reasoning: 'Most volatile leagues where recent form is extremely important',
      leagues: [
        // African leagues
        200, 202, 186, 233, 288, 276,
        // African competitions
        6, 12, 36, 29,
        // African cups
        822, 514, 511, 714,
        // Other developing
        188, 545, 298
      ]
    },

    european_second_tier: {
      name: 'European Second Tier & Cups',
      description: 'European second divisions and domestic cups',
      characteristics: 'Promotion/relegation pressure, structured but lower quality',
      expectedXi: 0.0035, // Moderate-high recent form weighting
      reasoning: 'Promotion battles and cup competitions make recent form important',
      leagues: [
        // Second tiers
        89, 95, 204, 145, 219, 208, 107, 120, 236, 287, 211, 334, 284, 272, 346, 506,
        // Smaller European leagues
        310, 342, 419, 116, 172, 318, 329, 367, 327, 373, 365, 362, 261, 393, 394, 355, 371, 357,
        // European cups
        220, 209, 108, 121, 147, 212, 335, 285, 273, 347, 680, 314, 732, 375, 321, 384, 359, 757
      ]
    },

    lower_regional: {
      name: 'Lower Tier & Regional',
      description: 'Lower divisions, regional leagues, and amateur competitions',
      characteristics: 'Limited data quality, amateur/semi-pro, inconsistent',
      expectedXi: 0.002, // Lower recent form weighting
      reasoning: 'Limited data quality requires longer memory for stability',
      leagues: [
        // All remaining leagues from tiers 5 and 6
        41, 80, 435, 436, 942, 943, 138, 63, 865, 494, 237, 316, 42, 492, 109, 173, 319, 297, 300, 311,
        // Regional and lower competitions
        709, 420, 1049, 117, 486, 707, 328, 657, 366, 246, 326, 544, 382, 498, 665, 364, 658, 361, 358, 374,
        // Regional Brazilian, Serbian, Finnish, Swedish
        475, 624, 1113, 573, 575, 574, 572, 476, 625, 247, 248, 249, 564, 115, 563, 565, 33,
        // Women's and youth
        525, 254, 44, 480, 524, 698, 850, 38,
        // Friendlies and others
        667, 10, 22, 856, 15, 32, 34, 31,
        // New Zealand and others
        955, 954
      ]
    }
  };

  /**
   * Get the group for a specific league
   */
  public static getLeagueGroup(leagueId: number): LeagueGroup {
    for (const [groupKey, groupInfo] of Object.entries(this.LEAGUE_GROUPS)) {
      if (groupInfo.leagues.includes(leagueId)) {
        return groupKey as LeagueGroup;
      }
    }
    
    // Default to lower_regional for unknown leagues
    return 'lower_regional';
  }

  /**
   * Get the optimized xi value for a league
   */
  public static getOptimizedXi(leagueId: number): number {
    const group = this.getLeagueGroup(leagueId);
    return this.LEAGUE_GROUPS[group].expectedXi;
  }

  /**
   * Get group information
   */
  public static getGroupInfo(group: LeagueGroup): LeagueGroupInfo {
    return this.LEAGUE_GROUPS[group];
  }

  /**
   * Get all groups
   */
  public static getAllGroups(): Record<LeagueGroup, LeagueGroupInfo> {
    return this.LEAGUE_GROUPS;
  }

  /**
   * Get leagues in a specific group
   */
  public static getLeaguesInGroup(group: LeagueGroup): number[] {
    return this.LEAGUE_GROUPS[group].leagues;
  }

  /**
   * Get group statistics
   */
  public static getGroupStats(): Record<LeagueGroup, { count: number; xiValue: number }> {
    const stats: Record<LeagueGroup, { count: number; xiValue: number }> = {} as any;
    
    for (const [groupKey, groupInfo] of Object.entries(this.LEAGUE_GROUPS)) {
      stats[groupKey as LeagueGroup] = {
        count: groupInfo.leagues.length,
        xiValue: groupInfo.expectedXi
      };
    }
    
    return stats;
  }

  /**
   * Validate that all leagues are properly grouped
   */
  public static validateGrouping(): {
    totalLeagues: number;
    groupedLeagues: number;
    missingLeagues: number[];
    duplicateLeagues: number[];
  } {
    const allGroupedLeagues: number[] = [];
    const duplicates: number[] = [];
    
    // Collect all leagues from groups
    for (const groupInfo of Object.values(this.LEAGUE_GROUPS)) {
      for (const leagueId of groupInfo.leagues) {
        if (allGroupedLeagues.includes(leagueId)) {
          duplicates.push(leagueId);
        }
        allGroupedLeagues.push(leagueId);
      }
    }

    // Import league tiers to check against
    // Note: This would need to be imported from leagueTiers.ts
    // For now, we'll return basic stats
    
    return {
      totalLeagues: allGroupedLeagues.length,
      groupedLeagues: new Set(allGroupedLeagues).size,
      missingLeagues: [], // Would need to compare against leagueTiers
      duplicateLeagues: duplicates
    };
  }

  /**
   * Debug: Show group assignment for specific leagues
   */
  public static debugLeagueAssignment(leagueIds: number[]): Record<number, { group: LeagueGroup; xi: number }> {
    const result: Record<number, { group: LeagueGroup; xi: number }> = {};
    
    for (const leagueId of leagueIds) {
      const group = this.getLeagueGroup(leagueId);
      const xi = this.getOptimizedXi(leagueId);
      result[leagueId] = { group, xi };
    }
    
    return result;
  }
}
