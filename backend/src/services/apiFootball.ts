import axios, { Axios<PERSON>rror, AxiosResponse } from 'axios'; // Use standard import for types
import config from '../config';
import type { TeamStatistic } from '../models/TeamStatistic';
import type { Venue } from '../models/Venue';
import type { Fixture, TeamStatsInfo, EventInfo, LineupInfo, PlayerStatsInfo } from '../models/Fixture'; // Import Fixture sub-types
import type { Prediction } from '../models/Prediction';
import type { Injury } from '../models/Injury';

// Create an Axios instance pre-configured for API-Football
const apiFootballClient = axios.create({
  baseURL: config.apiFootball.baseUrl,
  headers: {
    'x-apisports-key': config.apiFootball.apiKey,
    'x-rapidapi-host': config.apiFootball.hostHeader, // Required even for direct subscriptions
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 10000, // Set a reasonable timeout (e.g., 10 seconds)
});

// Optional: Add interceptors for logging or more complex error handling
// Let TypeScript infer the type for 'request'
apiFootballClient.interceptors.request.use(request => {
  // console.log('Starting Request', JSON.stringify(request, null, 2));
  return request;
});

// Explicitly type the response parameter and its data structure
apiFootballClient.interceptors.response.use((response: AxiosResponse<{ errors?: Record<string, any> }>) => {
  // console.log('Response:', JSON.stringify(response.data, null, 2));
  // Check for API-level errors within the response data
  const errors = response.data?.errors;
  if (errors && Object.keys(errors).length > 0) {
      console.error('API-Football Error:', errors);
      // You might want to throw a specific error type here
      return Promise.reject(new Error(`API Error: ${JSON.stringify(errors)}`));
  }
  return response;
}, (error: AxiosError) => { // Explicitly type the error parameter
  // Enhanced HTTP error handling
  if (error.response) {
    const status = error.response.status;
    const statusText = error.response.statusText;
    console.error(`API request failed [${status}]: ${statusText}`, error.response.data);

    // Handle specific error codes with user-friendly messages
    if (status === 429) {
      return Promise.reject(new Error('API rate limit exceeded. Please try again later.'));
    } else if (status === 401) {
      return Promise.reject(new Error('API authentication failed. Check your API key.'));
    } else if (status === 403) {
      return Promise.reject(new Error('API access forbidden. Check your subscription plan.'));
    } else if (status >= 500) {
      return Promise.reject(new Error('API server error. Please try again later.'));
    }
  } else if (error.request) {
    console.error('No response received from API:', error.request);
    return Promise.reject(new Error('Network error: Unable to reach API-Football servers.'));
  } else {
    console.error('Request setup error:', error.message);
  }
  return Promise.reject(error);
});

/**
 * Generic function to make GET requests to the API-Football API.
 * @param endpoint - The API endpoint path (e.g., '/countries').
 * @param params - Optional query parameters.
 * @returns Promise resolving with the API response data's 'response' field or the full data if structure varies.
 */
async function fetchData<T>(endpoint: string, params?: Record<string, any>): Promise<T> { // Keep generic T for flexibility
  try {
    // The response structure from API-Football often nests the actual data array in a 'response' property.
    // The interceptor already handles API-level errors in the 'errors' field.
    // Define the expected structure for the generic response data
    const response: AxiosResponse<{ response: T, errors?: Record<string, any>, [key: string]: any }> = await apiFootballClient.get(endpoint, { params });
    // Log rate limit headers (important for monitoring) - Use lowercase header names as Axios might normalize them
    console.log(`Rate Limit Info - Daily Remaining: ${response.headers['x-ratelimit-requests-remaining']}, Minute Remaining: ${response.headers['x-ratelimit-remaining']}`);

    // Check if the expected 'response' property exists before returning it
    if (response.data && response.data.response) {
       return response.data.response as T; // Return the nested 'response' array/object
    }
    // Fallback: return the whole data if 'response' property is missing (adjust T accordingly if needed)
    return response.data as T;
  } catch (error) {
    // Errors are logged by the interceptor, re-throw or handle as needed
    console.error(`Failed to fetch data from ${endpoint}:`, error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

// --- Example specific function (can add more as needed) ---

// Define a specific type for a Country object based on expected API response
interface Country {
    name: string;
    code: string | null; // Code might be null
    flag: string | null; // Flag might be null
}

// Update the function to use the specific Country type and expect an array
export async function fetchCountries(params?: { name?: string; code?: string; search?: string }): Promise<Country[]> {
    // The generic fetchData will return the `response.data.response` part, which should be Country[]
    return fetchData<Country[]>('/countries', params);
}

// --- League Seasons Function ---
// The API returns an array of numbers (years) directly in the 'response' field
export async function fetchLeagueSeasons(): Promise<number[]> {
    return fetchData<number[]>('/leagues/seasons');
}

// --- Leagues Function ---
// Define the structure matching the League model (adjust if needed based on exact API response)
interface ApiLeagueResponse {
    league: {
        id: number;
        name: string;
        type: string;
        logo: string;
    };
    country: {
        name: string;
        code: string | null;
        flag: string | null;
    };
    seasons: {
        year: number;
        start: string;
        end: string;
        current: boolean;
        coverage: any; // Define Coverage type more accurately if needed
    }[];
}

// Define allowed query parameters based on documentation
interface FetchLeaguesParams {
    id?: number;
    name?: string;
    country?: string;
    code?: string;
    season?: number;
    team?: number;
    type?: 'league' | 'cup';
    current?: 'true' | 'false';
    search?: string;
    last?: number;
}

export async function fetchLeagues(params?: FetchLeaguesParams): Promise<ApiLeagueResponse[]> {
    return fetchData<ApiLeagueResponse[]>('/leagues', params);
}

// --- Teams Function ---
// Define the structure matching the Team model
interface ApiTeamResponse {
    team: {
        id: number;
        name: string;
        code: string | null;
        country: string | null;
        founded: number | null;
        national: boolean;
        logo: string | null;
    };
    venue: {
        id: number | null;
        name: string | null;
        address: string | null;
        city: string | null;
        country: string | null;
        capacity: number | null;
        surface: string | null;
        image: string | null;
    };
}

// Define allowed query parameters based on documentation
// Requires at least one parameter
interface FetchTeamsParams {
    id?: number;
    name?: string;
    league?: number;
    season?: number; // Requires league or id
    country?: string;
    code?: string; // 3-letter code
    venue?: number;
    search?: string; // >= 3 chars
}

export async function fetchTeams(params: FetchTeamsParams): Promise<ApiTeamResponse[]> {
    // Add validation to ensure at least one parameter is provided if needed,
    // although the API itself should enforce this.
    if (Object.keys(params).length === 0) {
        throw new Error("At least one parameter is required to fetch teams.");
    }
    return fetchData<ApiTeamResponse[]>('/teams', params);
}

// --- Team Seasons Function ---
// The API returns an array of numbers (years) directly in the 'response' field
export async function fetchTeamSeasons(params: { team: number }): Promise<number[]> {
    return fetchData<number[]>('/teams/seasons', params);
}

// --- Team Countries Function ---
// The API returns an array of strings (country names) directly in the 'response' field
export async function fetchTeamCountries(): Promise<string[]> {
    return fetchData<string[]>('/teams/countries');
}

// --- Timezone Function ---
// The API returns an array of timezone strings directly in the 'response' field
export async function fetchTimezones(): Promise<string[]> {
    return fetchData<string[]>('/timezone');
}

// --- Venues Function ---
// Define the structure matching the Venue model
type ApiVenueResponse = Omit<Venue, '_id' | 'lastUpdated'> & { id: number }; // API returns 'id', not '_id'

// Define allowed query parameters based on documentation
// Requires at least one parameter
interface FetchVenuesParams {
    id?: number;
    name?: string;
    city?: string;
    country?: string;
    search?: string; // >= 3 chars
}

export async function fetchVenues(params: FetchVenuesParams): Promise<ApiVenueResponse[]> {
    if (Object.keys(params).length === 0) {
        throw new Error("At least one parameter is required to fetch venues.");
    }
    return fetchData<ApiVenueResponse[]>('/venues', params);
}

// --- Fixture Rounds Function ---
// The API returns an array of strings (round names) directly in the 'response' field
interface FetchFixtureRoundsParams {
    league: number;
    season: number;
    current?: 'true' | 'false';
    dates?: 'true' | 'false'; // Parameter to include dates
    timezone?: string; // Optional timezone parameter
}

// Define the response type when dates=true is used
interface RoundWithDates {
    round: string;
    start: string; // ISO date string
    end: string;   // ISO date string
}

// Function overloads to handle different return types based on params
export async function fetchFixtureRounds(params: FetchFixtureRoundsParams & { dates: 'true' }): Promise<RoundWithDates[]>;
export async function fetchFixtureRounds(params: FetchFixtureRoundsParams): Promise<string[]>;
export async function fetchFixtureRounds(params: FetchFixtureRoundsParams): Promise<string[] | RoundWithDates[]> {
    // If dates=true is used, the response structure changes to include date ranges
    if (params.dates === 'true') {
        return fetchData<RoundWithDates[]>('/fixtures/rounds', params);
    }
    // Default behavior (dates=false or not specified)
    return fetchData<string[]>('/fixtures/rounds', params);
}

// --- Fixtures Function ---
// Define the structure matching the Fixture model (or the raw API response)
// The detailed sub-documents (events, lineups etc.) might only be present when fetching by id/ids
type ApiFixtureResponse = Omit<Fixture, '_id' | 'lastUpdated'>; // API won't return our internal fields

// Define allowed query parameters based on documentation
interface FetchFixturesParams {
    id?: number;
    ids?: string; // e.g., "id1-id2-id3" (max 20)
    live?: string; // "all" or "L1-L2"
    date?: string; // YYYY-MM-DD
    league?: number;
    season?: number;
    team?: number;
    last?: number;
    next?: number;
    from?: string; // YYYY-MM-DD
    to?: string; // YYYY-MM-DD
    round?: string;
    status?: string; // e.g., "NS", "FT", "NS-PST-FT"
    venue?: number;
    timezone?: string;
}

export async function fetchFixtures(params?: FetchFixturesParams): Promise<ApiFixtureResponse[]> {
    // Use the generic fetchData which expects the array under 'response'
    return fetchData<ApiFixtureResponse[]>('/fixtures', params);
}

// --- Head to Head (H2H) Function ---
// Define allowed query parameters based on documentation
interface FetchH2HParams {
    h2h: string; // Required: "teamId1-teamId2"
    date?: string;
    league?: number;
    season?: number;
    last?: number;
    next?: number;
    from?: string;
    to?: string;
    status?: string;
    venue?: number;
    timezone?: string;
}

export async function fetchHeadToHead(params: FetchH2HParams): Promise<ApiFixtureResponse[]> {
    // Use the generic fetchData which expects the array under 'response'
    return fetchData<ApiFixtureResponse[]>('/fixtures/headtohead', params);
}

// --- Predictions Function ---
// Define the structure matching the Prediction model
type ApiPredictionResponse = Omit<Prediction, '_id' | 'lastUpdated'>; // API won't return our internal fields

interface FetchPredictionsParams {
    fixture: number; // Required: Fixture ID
}

export async function fetchPredictions(params: FetchPredictionsParams): Promise<ApiPredictionResponse[]> {
    // Predictions API returns an array containing the prediction object
    return fetchData<ApiPredictionResponse[]>('/predictions', params);
}

// --- Injuries Function ---
// Define the structure matching the Injury model
type ApiInjuryResponse = Omit<Injury, '_id' | 'lastUpdated'>; // API won't return our internal fields

// Define allowed query parameters based on documentation
interface FetchInjuriesParams {
    league?: number;
    season?: number; // Required with league, team, player
    fixture?: number;
    team?: number;
    player?: number;
    date?: string; // YYYY-MM-DD
    ids?: string; // e.g., "fixtureId1-fixtureId2" (max 20)
    timezone?: string;
}

export async function fetchInjuries(params: FetchInjuriesParams): Promise<ApiInjuryResponse[]> {
    // Requires at least one parameter (league, fixture, team, player, date, ids)
    if (Object.keys(params).length === 0 || (!params.league && !params.fixture && !params.team && !params.player && !params.date && !params.ids)) {
        throw new Error("At least one parameter (league, fixture, team, player, date, ids) is required to fetch injuries.");
    }
    // Season is required if league, team, or player is used
    if ((params.league || params.team || params.player) && !params.season) {
         throw new Error("Parameter 'season' is required when using 'league', 'team', or 'player'.");
    }
    return fetchData<ApiInjuryResponse[]>('/injuries', params);
}

// --- Fixture Statistics Function ---
// Define structure based on Fixture model's TeamStatsInfo
type ApiFixtureStatsResponse = TeamStatsInfo[]; // API returns an array of stats per team

interface FetchFixtureStatsParams {
    fixture: number; // Required
    team?: number; // Optional
    type?: string; // Optional
    half?: 'true' | 'false'; // Optional - if true, returns halftime statistics
}

export async function fetchFixtureStatistics(params: FetchFixtureStatsParams): Promise<ApiFixtureStatsResponse> {
    // Use generic fetchData, expects array under 'response'
    return fetchData<ApiFixtureStatsResponse>('/fixtures/statistics', params);
}

// --- Fixture Events Function ---
// Define structure based on Fixture model's EventInfo
type ApiFixtureEventsResponse = EventInfo[]; // API returns an array of events

interface FetchFixtureEventsParams {
    fixture: number; // Required
    team?: number; // Optional
    player?: number; // Optional
    type?: 'Goal' | 'Card' | 'Subst' | 'Var'; // Optional
}

export async function fetchFixtureEvents(params: FetchFixtureEventsParams): Promise<ApiFixtureEventsResponse> {
    // Use generic fetchData, expects array under 'response'
    return fetchData<ApiFixtureEventsResponse>('/fixtures/events', params);
}

// --- Fixture Lineups Function ---
// Define structure based on Fixture model's LineupInfo
type ApiFixtureLineupsResponse = LineupInfo[]; // API returns an array of lineups (one per team)

interface FetchFixtureLineupsParams {
    fixture: number; // Required
    team?: number; // Optional
    player?: number; // Optional (check API docs for response format change)
    type?: string; // Optional (e.g., 'formation')
}

export async function fetchFixtureLineups(params: FetchFixtureLineupsParams): Promise<ApiFixtureLineupsResponse> {
    // Use generic fetchData, expects array under 'response'
    return fetchData<ApiFixtureLineupsResponse>('/fixtures/lineups', params);
}

// --- Fixture Players Function ---
// Define structure based on Fixture model's PlayerStatsInfo
type ApiFixturePlayersResponse = PlayerStatsInfo[]; // API returns an array (one per team) containing players array

interface FetchFixturePlayersParams {
    fixture: number; // Required
    team?: number; // Optional
}

export async function fetchFixturePlayers(params: FetchFixturePlayersParams): Promise<ApiFixturePlayersResponse> {
    // Use generic fetchData, expects array under 'response'
    return fetchData<ApiFixturePlayersResponse>('/fixtures/players', params);
}

// --- Standings Function ---
// Define the structure matching the Standing model's expected API response part
// The API response nests the actual standings array inside league -> standings
interface ApiStandingResponse {
    league: {
        id: number;
        name: string;
        country: string;
        logo: string;
        flag: string | null;
        season: number;
        standings: any[][]; // Use 'any' for now, will match TeamStanding[][] structure
    };
}

// Define required query parameters based on documentation
interface FetchStandingsParams {
    league: number;
    season: number;
    team?: number; // Optional: filter standings to only show the specified team's standing
}

export async function fetchStandings(params: FetchStandingsParams): Promise<ApiStandingResponse[]> {
    // The generic fetchData expects the data array under 'response' key.
    // Standings API returns an array containing one league object which contains the standings.
    // We need to handle this specific structure.
    try {
        console.log(`Fetching standings with params: ${JSON.stringify(params)}`);
        // Use the client directly, not fetchData, because the response structure is different
        const response = await apiFootballClient.get<{ response: ApiStandingResponse[] }>('/standings', { params });
        // Log rate limit headers
        console.log(`Rate Limit Info - Daily Remaining: ${response.headers['x-ratelimit-requests-remaining']}, Minute Remaining: ${response.headers['x-ratelimit-remaining']}`);

        // Check for API-level errors (interceptor might handle this)
        if ((response.data as any).errors && Object.keys((response.data as any).errors).length > 0) {
            console.error('API-Football Error in fetchStandings:', (response.data as any).errors);
            throw new Error(`API Error: ${JSON.stringify((response.data as any).errors)}`);
        }

        // Validate the expected structure
        if (!response.data || !response.data.response || !Array.isArray(response.data.response)) {
            console.warn('Received unexpected data structure from /standings:', response.data);
            throw new Error('Unexpected data structure received for standings.');
        }

        // The 'response' array usually contains a single element with the league/standings info
        return response.data.response;

    } catch (error) {
        console.error(`Failed to fetch standings for league ${params.league}, season ${params.season}:`, error);
        throw error; // Re-throw the error
    }
}

// --- Team Statistics Function ---
// Define the structure matching the TeamStatistic model (or the raw API response if different)
// Note: The API response might be the TeamStatistic structure directly, not nested under 'response'.
// We need to adjust fetchData or handle this specifically if the structure differs.
// Assuming for now the API returns the TeamStatistic structure directly.
type ApiTeamStatisticResponse = Omit<TeamStatistic, '_id' | 'lastUpdated'>; // API won't return our internal fields

// Define required query parameters based on documentation
interface FetchTeamStatisticsParams {
    league: number;
    season: number;
    team: number;
    date?: string; // YYYY-MM-DD
}

export async function fetchTeamStatistics(params: FetchTeamStatisticsParams): Promise<ApiTeamStatisticResponse> {
    // This endpoint might return the object directly, not nested in 'response'
    // Let's try fetching without the generic fetchData wrapper first to see the structure
    try {
        console.log(`Fetching team statistics with params: ${JSON.stringify(params)}`);
        const response = await apiFootballClient.get<any>('/teams/statistics', { params });
        // Log rate limit headers
        console.log(`Rate Limit Info - Daily Remaining: ${response.headers['x-ratelimit-requests-remaining']}, Minute Remaining: ${response.headers['x-ratelimit-remaining']}`);

        // Check for API-level errors within the response data (interceptor might handle this, but double-check)
        if ((response.data as any).errors && Object.keys((response.data as any).errors).length > 0) {
            console.error('API-Football Error in fetchTeamStatistics:', (response.data as any).errors);
            throw new Error(`API Error: ${JSON.stringify((response.data as any).errors)}`);
        }

        // Handle different response structures
        let statsData: ApiTeamStatisticResponse;

        // Check if the response has a nested 'response' property (new API structure)
        if (response.data && response.data.response) {
            console.warn('Received unexpected data structure from /teams/statistics:', JSON.stringify(response.data, null, 2));
            statsData = response.data.response;
        } else {
            // Original expected structure
            statsData = response.data;
        }

        // Check if the expected data structure is present
        if (!statsData || !statsData.league || !statsData.team) {
            console.warn('Invalid team statistics data structure:', statsData);
            throw new Error('Unexpected data structure received for team statistics.');
        }

        return statsData;
    } catch (error) {
        console.error(`Failed to fetch team statistics for team ${params.team}, league ${params.league}, season ${params.season}:`, error);
        throw error; // Re-throw the error
    }
}


// --- Status Check Function ---
interface StatusResponse {
    account: any; // Define specific account type later
    subscription: any; // Define specific subscription type later
    requests: {
        current: number;
        limit_day: number;
        limit_minute?: number; // Optional minute limit
    };
}

export async function checkApiStatus(): Promise<StatusResponse> {
    // Status endpoint might not have the nested 'response' structure
    try {
        // Type the status response explicitly
        const response: AxiosResponse<StatusResponse> = await apiFootballClient.get('/status');
         // Log rate limit headers (important for monitoring) - Use lowercase header names
        console.log(`Rate Limit Info - Daily Remaining: ${response.headers['x-ratelimit-requests-remaining']}, Minute Remaining: ${response.headers['x-ratelimit-remaining']}`);
        return response.data; // Return the full data for status
    } catch (error) {
        console.error(`Failed to fetch data from /status:`, error);
        throw error;
    }
}


// Export the functions and potentially the client instance if needed elsewhere
export { fetchData, apiFootballClient };
