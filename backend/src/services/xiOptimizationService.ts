/**
 * Xi Optimization Service
 * 
 * Optimizes xi (time decay) parameters for different league groups
 * Uses historical data validation to find optimal values
 */

import { LeagueGroupingService, LeagueGroup } from './leagueGroupingService';
import { ValidationService, ValidationResult } from './validationService';
import { TeamStrengthService, MatchData } from './teamStrengthService';
import connectDB from '../config/database';

export interface XiOptimizationResult {
  group: LeagueGroup;
  optimalXi: number;
  baselineXi: number;
  improvement: {
    logLikelihoodImprovement: number;
    accuracyImprovement: number;
    confidenceLevel: number;
  };
  testedValues: {
    xi: number;
    logLikelihood: number;
    accuracy: number;
  }[];
  validationPeriod: {
    from: Date;
    to: Date;
    matchesAnalyzed: number;
  };
}

export interface GroupOptimizationSummary {
  totalGroups: number;
  optimizedGroups: number;
  overallImprovement: number;
  results: XiOptimizationResult[];
  recommendedDeployment: boolean;
}

export class XiOptimizationService {
  
  private static readonly XI_CANDIDATES = [
    0.001, 0.0015, 0.002, 0.0025, 0.003, 0.0035, 0.004, 0.0045, 0.005, 0.006
  ];
  
  private static readonly BASELINE_XI = 0.0025; // Current default
  private static readonly MIN_MATCHES_FOR_OPTIMIZATION = 100;
  private static readonly VALIDATION_MONTHS = 6; // Use last 6 months for validation

  /**
   * Optimize xi parameter for a specific league group
   */
  public static async optimizeGroupXi(
    group: LeagueGroup,
    customXiCandidates?: number[]
  ): Promise<XiOptimizationResult | null> {
    console.log(`🎯 Optimizing xi parameter for group: ${group}`);
    
    const groupInfo = LeagueGroupingService.getGroupInfo(group);
    const leagues = groupInfo.leagues;
    
    if (leagues.length === 0) {
      console.log(`⚠️ No leagues found for group ${group}`);
      return null;
    }

    // Get historical data for all leagues in group
    const historicalData = await this.getGroupHistoricalData(leagues);
    
    if (historicalData.length < this.MIN_MATCHES_FOR_OPTIMIZATION) {
      console.log(`⚠️ Insufficient data for group ${group}: ${historicalData.length} matches`);
      return null;
    }

    console.log(`📊 Testing ${group} with ${historicalData.length} matches across ${leagues.length} leagues`);

    // Test different xi values
    const xiCandidates = customXiCandidates || this.XI_CANDIDATES;
    const testResults: { xi: number; logLikelihood: number; accuracy: number }[] = [];
    
    let bestXi = this.BASELINE_XI;
    let bestLogLikelihood = -Infinity;
    let baselineLogLikelihood = -Infinity;

    for (const xi of xiCandidates) {
      console.log(`  Testing xi = ${xi}...`);
      
      try {
        const result = await this.validateXiValue(historicalData, xi);
        
        testResults.push({
          xi,
          logLikelihood: result.averageLogLikelihood,
          accuracy: result.accuracy.percentage
        });

        if (xi === this.BASELINE_XI) {
          baselineLogLikelihood = result.averageLogLikelihood;
        }

        if (result.averageLogLikelihood > bestLogLikelihood) {
          bestLogLikelihood = result.averageLogLikelihood;
          bestXi = xi;
        }

        console.log(`    Xi ${xi}: Log-likelihood ${result.averageLogLikelihood.toFixed(4)}, Accuracy ${result.accuracy.percentage.toFixed(1)}%`);
        
      } catch (error) {
        console.error(`    Error testing xi ${xi}:`, error.message);
      }
    }

    if (testResults.length === 0) {
      console.log(`❌ No valid test results for group ${group}`);
      return null;
    }

    // Calculate improvements
    const baselineResult = testResults.find(r => r.xi === this.BASELINE_XI);
    const bestResult = testResults.find(r => r.xi === bestXi);
    
    if (!baselineResult || !bestResult) {
      console.log(`❌ Missing baseline or best result for group ${group}`);
      return null;
    }

    const logLikelihoodImprovement = bestResult.logLikelihood - baselineResult.logLikelihood;
    const accuracyImprovement = bestResult.accuracy - baselineResult.accuracy;
    
    // Calculate confidence level (simple heuristic)
    const confidenceLevel = Math.min(95, Math.max(50, 
      50 + (logLikelihoodImprovement * 1000) + (accuracyImprovement * 5)
    ));

    const validationPeriod = this.getValidationPeriod(historicalData);

    console.log(`✅ Optimization complete for ${group}:`);
    console.log(`   Optimal xi: ${bestXi} (vs baseline ${this.BASELINE_XI})`);
    console.log(`   Log-likelihood improvement: ${logLikelihoodImprovement.toFixed(4)}`);
    console.log(`   Accuracy improvement: ${accuracyImprovement.toFixed(2)}%`);
    console.log(`   Confidence: ${confidenceLevel.toFixed(0)}%`);

    return {
      group,
      optimalXi: bestXi,
      baselineXi: this.BASELINE_XI,
      improvement: {
        logLikelihoodImprovement,
        accuracyImprovement,
        confidenceLevel
      },
      testedValues: testResults.sort((a, b) => b.logLikelihood - a.logLikelihood),
      validationPeriod: {
        from: validationPeriod.from,
        to: validationPeriod.to,
        matchesAnalyzed: historicalData.length
      }
    };
  }

  /**
   * Optimize xi parameters for all league groups
   */
  public static async optimizeAllGroups(): Promise<GroupOptimizationSummary> {
    console.log('🚀 Starting xi optimization for all league groups...\n');
    
    const allGroups = Object.keys(LeagueGroupingService.getAllGroups()) as LeagueGroup[];
    const results: XiOptimizationResult[] = [];
    let totalImprovement = 0;
    let optimizedCount = 0;

    for (const group of allGroups) {
      try {
        const result = await this.optimizeGroupXi(group);
        if (result) {
          results.push(result);
          totalImprovement += result.improvement.logLikelihoodImprovement;
          optimizedCount++;
        }
        console.log(''); // Add spacing between groups
      } catch (error) {
        console.error(`❌ Failed to optimize group ${group}:`, error);
      }
    }

    const overallImprovement = optimizedCount > 0 ? totalImprovement / optimizedCount : 0;
    const recommendedDeployment = overallImprovement > 0.01 && optimizedCount >= 4;

    console.log('📊 Xi Optimization Summary:');
    console.log(`   Groups optimized: ${optimizedCount}/${allGroups.length}`);
    console.log(`   Average improvement: ${overallImprovement.toFixed(4)}`);
    console.log(`   Recommended for deployment: ${recommendedDeployment ? '✅ YES' : '❌ NO'}`);

    return {
      totalGroups: allGroups.length,
      optimizedGroups: optimizedCount,
      overallImprovement,
      results,
      recommendedDeployment
    };
  }

  /**
   * Get historical data for leagues in a group
   */
  private static async getGroupHistoricalData(leagues: number[]): Promise<MatchData[]> {
    const db = await connectDB();
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - this.VALIDATION_MONTHS);

    const fixtures = await db.collection('fixtures').find({
      'league.id': { $in: leagues },
      'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] },
      'date': { $gte: cutoffDate.toISOString() },
      'goals.home': { $exists: true, $ne: null },
      'goals.away': { $exists: true, $ne: null }
    }).toArray();

    return fixtures.map(fixture => ({
      homeTeamId: fixture.teams.home.id,
      awayTeamId: fixture.teams.away.id,
      homeGoals: fixture.goals.home,
      awayGoals: fixture.goals.away,
      date: new Date(fixture.date),
      leagueId: fixture.league.id
    }));
  }

  /**
   * Validate a specific xi value using time-series cross-validation
   */
  private static async validateXiValue(
    matches: MatchData[],
    xi: number
  ): Promise<ValidationResult> {
    // Sort matches by date
    const sortedMatches = matches.sort((a, b) => a.date.getTime() - b.date.getTime());
    
    // Use 80% for training, 20% for validation
    const splitIndex = Math.floor(sortedMatches.length * 0.8);
    const trainingMatches = sortedMatches.slice(0, splitIndex);
    const validationMatches = sortedMatches.slice(splitIndex);

    if (validationMatches.length < 10) {
      throw new Error('Insufficient validation data');
    }

    // Temporarily override xi parameter
    const originalXi = TeamStrengthService.getTimeDecayXi();
    TeamStrengthService.setTimeDecayXi(xi);

    try {
      // Calculate team strengths with custom xi
      const { teamStrengths, leagueParams } = await TeamStrengthService.calculateFromMatches(trainingMatches);

      // Validate on test set
      let totalLogLikelihood = 0;
      let correctPredictions = 0;
      let validPredictions = 0;

      for (const match of validationMatches) {
        try {
          const prediction = await this.makeSinglePrediction(match, teamStrengths, leagueParams);
          if (prediction) {
            totalLogLikelihood += prediction.logLikelihood;
            if (prediction.correct) correctPredictions++;
            validPredictions++;
          }
        } catch (error) {
          // Skip invalid predictions
        }
      }

      if (validPredictions === 0) {
        throw new Error('No valid predictions generated');
      }

      const averageLogLikelihood = totalLogLikelihood / validPredictions;
      const accuracy = (correctPredictions / validPredictions) * 100;

      return {
        totalLogLikelihood,
        averageLogLikelihood,
        predictionsCount: validPredictions,
        accuracy: {
          correctResults: correctPredictions,
          totalPredictions: validPredictions,
          percentage: accuracy
        },
        calibration: {
          homeWinAccuracy: 0, // Simplified for xi optimization
          drawAccuracy: 0,
          awayWinAccuracy: 0
        },
        timeRange: {
          from: validationMatches[0].date,
          to: validationMatches[validationMatches.length - 1].date
        }
      };

    } finally {
      // Restore original xi
      TeamStrengthService.setTimeDecayXi(originalXi);
    }
  }

  /**
   * Make a single prediction for validation
   */
  private static async makeSinglePrediction(
    match: MatchData,
    teamStrengths: Map<number, any>,
    leagueParams: any
  ): Promise<{ logLikelihood: number; correct: boolean } | null> {
    // This is a simplified prediction for validation purposes
    // In practice, you'd use the full prediction pipeline
    
    const homeStrength = teamStrengths.get(match.homeTeamId);
    const awayStrength = teamStrengths.get(match.awayTeamId);

    if (!homeStrength || !awayStrength) {
      return null;
    }

    // Simple outcome prediction
    const homeWinProb = 0.4; // Simplified - would use actual calculation
    const drawProb = 0.3;
    const awayWinProb = 0.3;

    // Determine actual result
    let actualProb: number;
    let correct = false;

    if (match.homeGoals > match.awayGoals) {
      actualProb = homeWinProb;
      correct = homeWinProb >= Math.max(drawProb, awayWinProb);
    } else if (match.homeGoals < match.awayGoals) {
      actualProb = awayWinProb;
      correct = awayWinProb >= Math.max(homeWinProb, drawProb);
    } else {
      actualProb = drawProb;
      correct = drawProb >= Math.max(homeWinProb, awayWinProb);
    }

    const logLikelihood = actualProb > 0 ? Math.log(actualProb) : -10;

    return { logLikelihood, correct };
  }

  /**
   * Get validation period from match data
   */
  private static getValidationPeriod(matches: MatchData[]): { from: Date; to: Date } {
    const dates = matches.map(m => m.date).sort((a, b) => a.getTime() - b.getTime());
    return {
      from: dates[0],
      to: dates[dates.length - 1]
    };
  }
}
