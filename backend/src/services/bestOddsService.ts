/**
 * Best Odds Service
 * 
 * This service finds the best odds across multiple bookmakers for specific bet types and selections.
 * It provides functionality to get the highest odds (best value for bettors) for any supported bet type.
 */

import { getOddsCollection, getLiveOddsCollection, BookmakerOdds } from '../models/Odds';
import { TipType, TipDetails } from '../models/Tip';
import { getBetIdForTipType, tipDetailsToBetValue } from '../utils/betTypeMapping';
import { getRedisClient } from '../config/redis';

// Interface for best odds response
export interface BestOddsResult {
  fixtureId: number;
  betType: TipType;
  selection: string;
  bestOdds: {
    value: string;
    decimal: number;
    bookmaker: {
      id: number;
      name: string;
    };
    lastUpdated: string;
  } | null;
  allOdds: Array<{
    value: string;
    decimal: number;
    bookmaker: {
      id: number;
      name: string;
    };
  }>;
  available: boolean;
  error?: string;
}

// Interface for multiple odds comparison
export interface OddsComparison {
  fixtureId: number;
  betType: TipType;
  selections: Record<string, {
    bestOdds: {
      value: string;
      decimal: number;
      bookmaker: {
        id: number;
        name: string;
      };
    } | null;
    allOdds: Array<{
      value: string;
      decimal: number;
      bookmaker: {
        id: number;
        name: string;
      };
    }>;
  }>;
  lastUpdated: string;
}

/**
 * Find the best odds for a specific tip type and selection
 */
export async function getBestOdds(
  fixtureId: number,
  tipType: TipType,
  details: TipDetails,
  useLiveOdds: boolean = false
): Promise<BestOddsResult> {
  try {
    // Get the bet ID for this tip type and market
    const market = details.market || 'fulltime';
    const betId = getBetIdForTipType(tipType, market);
    if (!betId) {
      return {
        fixtureId,
        betType: tipType,
        selection: 'unknown',
        bestOdds: null,
        allOdds: [],
        available: false,
        error: `Unsupported tip type: ${tipType} for market: ${market}`
      };
    }

    // Convert tip details to bet value
    const selection = tipDetailsToBetValue(tipType, details);
    if (!selection) {
      return {
        fixtureId,
        betType: tipType,
        selection: 'unknown',
        bestOdds: null,
        allOdds: [],
        available: false,
        error: 'Invalid tip details for bet value conversion'
      };
    }

    // Check cache first
    const cacheKey = `best_odds:${fixtureId}:${betId}:${selection}:${useLiveOdds ? 'live' : 'prematch'}`;
    const redisClient = getRedisClient();
    const cachedResult = await redisClient.get(cacheKey);
    
    if (cachedResult) {
      return JSON.parse(cachedResult);
    }

    // Query the appropriate odds collection
    const collection = useLiveOdds ? getLiveOddsCollection() : getOddsCollection();
    
    // Use aggregation pipeline to find odds for this fixture and bet type
    const pipeline = [
      { $match: { fixtureId: fixtureId } },
      { $unwind: '$bookmakers' },
      { $unwind: '$bookmakers.bets' },
      { $match: { 'bookmakers.bets.id': betId } },
      { $unwind: '$bookmakers.bets.values' },
      { $match: { 'bookmakers.bets.values.value': selection } },
      {
        $project: {
          fixtureId: 1,
          update: 1,
          bookmaker: {
            id: '$bookmakers.id',
            name: '$bookmakers.name'
          },
          bet: {
            id: '$bookmakers.bets.id',
            name: '$bookmakers.bets.name'
          },
          value: '$bookmakers.bets.values.value',
          odd: '$bookmakers.bets.values.odd'
        }
      },
      {
        $addFields: {
          decimalOdd: { $toDouble: '$odd' }
        }
      },
      { $sort: { decimalOdd: -1 } } // Sort by highest odds first
    ];

    const oddsResults = await collection.aggregate(pipeline).toArray();

    if (oddsResults.length === 0) {
      const result: BestOddsResult = {
        fixtureId,
        betType: tipType,
        selection,
        bestOdds: null,
        allOdds: [],
        available: false,
        error: 'No odds found for this selection'
      };

      // Cache the result for 5 minutes
      await redisClient.setex(cacheKey, 300, JSON.stringify(result));
      return result;
    }

    // Process results
    const allOdds = oddsResults.map(result => ({
      value: result.odd,
      decimal: result.decimalOdd,
      bookmaker: result.bookmaker
    }));

    const bestOdds = allOdds[0]; // First item has highest odds due to sorting
    const lastUpdated = oddsResults[0].update;

    const result: BestOddsResult = {
      fixtureId,
      betType: tipType,
      selection,
      bestOdds: {
        value: bestOdds.value,
        decimal: bestOdds.decimal,
        bookmaker: bestOdds.bookmaker,
        lastUpdated
      },
      allOdds,
      available: true
    };

    // Cache the result for 2 minutes for live odds, 10 minutes for pre-match
    const cacheTTL = useLiveOdds ? 120 : 600;
    await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(result));

    return result;

  } catch (error) {
    console.error('Error in getBestOdds:', error);
    return {
      fixtureId,
      betType: tipType,
      selection: 'unknown',
      bestOdds: null,
      allOdds: [],
      available: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Get odds comparison for all possible selections of a bet type
 */
export async function getOddsComparison(
  fixtureId: number,
  tipType: TipType,
  useLiveOdds: boolean = false
): Promise<OddsComparison> {
  try {
    const betId = getBetIdForTipType(tipType);
    if (!betId) {
      throw new Error(`Unsupported tip type: ${tipType}`);
    }

    // Check cache first
    const cacheKey = `odds_comparison:${fixtureId}:${betId}:${useLiveOdds ? 'live' : 'prematch'}`;
    const redisClient = getRedisClient();
    const cachedResult = await redisClient.get(cacheKey);
    
    if (cachedResult) {
      return JSON.parse(cachedResult);
    }

    // Query the appropriate odds collection
    const collection = useLiveOdds ? getLiveOddsCollection() : getOddsCollection();
    
    // Get all odds for this fixture and bet type
    const pipeline = [
      { $match: { fixtureId: fixtureId } },
      { $unwind: '$bookmakers' },
      { $unwind: '$bookmakers.bets' },
      { $match: { 'bookmakers.bets.id': betId } },
      { $unwind: '$bookmakers.bets.values' },
      {
        $group: {
          _id: '$bookmakers.bets.values.value',
          update: { $first: '$update' },
          odds: {
            $push: {
              value: '$bookmakers.bets.values.odd',
              decimal: { $toDouble: '$bookmakers.bets.values.odd' },
              bookmaker: {
                id: '$bookmakers.id',
                name: '$bookmakers.name'
              }
            }
          }
        }
      },
      {
        $project: {
          selection: '$_id',
          update: 1,
          allOdds: { $sortArray: { input: '$odds', sortBy: { decimal: -1 } } },
          bestOdds: { $arrayElemAt: [{ $sortArray: { input: '$odds', sortBy: { decimal: -1 } } }, 0] }
        }
      }
    ];

    const results = await collection.aggregate(pipeline).toArray();
    
    const selections: Record<string, any> = {};
    let lastUpdated = '';

    for (const result of results) {
      selections[result.selection] = {
        bestOdds: result.bestOdds,
        allOdds: result.allOdds
      };
      if (!lastUpdated || result.update > lastUpdated) {
        lastUpdated = result.update;
      }
    }

    const comparison: OddsComparison = {
      fixtureId,
      betType: tipType,
      selections,
      lastUpdated
    };

    // Cache the result
    const cacheTTL = useLiveOdds ? 120 : 600;
    await redisClient.setex(cacheKey, cacheTTL, JSON.stringify(comparison));

    return comparison;

  } catch (error) {
    console.error('Error in getOddsComparison:', error);
    throw error;
  }
}

/**
 * Get the best odds for multiple tip types at once
 */
export async function getBestOddsMultiple(
  fixtureId: number,
  tips: Array<{ tipType: TipType; details: TipDetails }>,
  useLiveOdds: boolean = false
): Promise<BestOddsResult[]> {
  const promises = tips.map(tip => 
    getBestOdds(fixtureId, tip.tipType, tip.details, useLiveOdds)
  );
  
  return Promise.all(promises);
}

/**
 * Clear odds cache for a specific fixture
 */
export async function clearOddsCache(fixtureId: number): Promise<void> {
  const redisClient = getRedisClient();
  const pattern = `*odds*:${fixtureId}:*`;
  
  const keys = await redisClient.keys(pattern);
  if (keys.length > 0) {
    await redisClient.del(...keys);
  }
}
