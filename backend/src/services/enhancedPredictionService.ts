/**
 * Enhanced Prediction Service
 * 
 * Orchestrates the generation of enhanced predictions using Dixon-Coles model
 * Combines team strength calculation, Dixon-Coles mathematics, and data persistence
 */

import { DixonColesService, DixonColesParams, MatchPrediction } from './dixonColesService';
import { TeamStrengthService, MatchData, TeamStrength, LeagueParameters } from './teamStrengthService';
import {
  EnhancedPrediction,
  CorrectScorePrediction,
  BTTSPrediction,
  PredictionConfidence,
  TeamStrengthDocument,
  LeagueParametersDocument
} from '../models/EnhancedPrediction';
import { getEloStrength } from './eloService';
import { EloEnhancedStrength } from '../models/EloRating';
import { getLeagueTier } from '../config/leagueTiers';
import { CornerPredictionService } from './cornerPredictionService';
import { CardPredictionService } from './cardPredictionService';
import connectDB from '../config/database';

export class EnhancedPredictionService {
  private static readonly CACHE_TTL_HOURS = 6; // Cache team strengths and league parameters for 6 hours
  private static readonly MIN_MATCHES_FOR_PREDICTION = 10; // Minimum matches needed for reliable predictions
  private static readonly HISTORICAL_MONTHS = 18; // Use last 18 months of data

  // ELO Integration Configuration
  private static readonly ELO_WEIGHT_HIGH_DATA = 0.0; // No ELO for domestic leagues with good data
  private static readonly ELO_WEIGHT_LOW_DATA = 0.0; // No ELO for domestic leagues with limited data
  private static readonly ELO_WEIGHT_CROSS_COMPETITION = 0.6; // ELO only for cross-competitions (reduced from 80%)
  private static readonly MIN_MATCHES_FOR_LOW_ELO_WEIGHT = 15; // Threshold for high vs low data

  /**
   * Generate enhanced prediction for a specific fixture
   */
  public static async generatePrediction(
    fixtureId: number,
    homeTeamId: number,
    awayTeamId: number,
    leagueId: number,
    fixtureDate: Date
  ): Promise<EnhancedPrediction | null> {
    try {
      const startTime = Date.now();

      // Get or calculate team strengths and league parameters
      const { teamStrengths, leagueParams } = await this.getTeamStrengthsAndLeagueParams(
        leagueId,
        fixtureDate
      );

      // Early exit if insufficient data (avoid expensive ELO lookups)
      if (teamStrengths.size === 0 || leagueParams.matchesAnalyzed < this.MIN_MATCHES_FOR_PREDICTION) {
        console.warn(`Early exit: Insufficient data for fixture ${fixtureId} - League: ${leagueId}, Matches: ${leagueParams.matchesAnalyzed}`);
        return null;
      }

      // Get ELO-enhanced strengths for both teams
      const [homeEloStrength, awayEloStrength] = await Promise.all([
        getEloStrength(homeTeamId),
        getEloStrength(awayTeamId)
      ]);

      // Calculate Dixon-Coles parameters for this match
      let dixonColesParams = TeamStrengthService.calculateMatchParams(
        homeTeamId,
        awayTeamId,
        leagueId,
        teamStrengths,
        leagueParams
      );

      // Apply ELO adjustments if ELO data is available
      if (dixonColesParams && homeEloStrength && awayEloStrength) {
        dixonColesParams = this.applyEloAdjustments(
          dixonColesParams,
          homeEloStrength,
          awayEloStrength,
          teamStrengths.get(homeTeamId),
          teamStrengths.get(awayTeamId),
          leagueId
        );
      }

      if (!dixonColesParams) {
        console.warn(`Could not calculate Dixon-Coles parameters for fixture ${fixtureId}`);
        return null;
      }

      // Validate parameters
      if (!DixonColesService.validateParams(dixonColesParams)) {
        console.warn(`Invalid Dixon-Coles parameters for fixture ${fixtureId}`);
        return null;
      }

      // Generate prediction using Dixon-Coles model
      const matchPrediction = DixonColesService.generatePrediction(dixonColesParams);

      // Get team and league information
      const [homeTeam, awayTeam, league] = await Promise.all([
        this.getTeamInfo(homeTeamId),
        this.getTeamInfo(awayTeamId),
        this.getLeagueInfo(leagueId)
      ]);

      // Convert to enhanced prediction format
      const enhancedPrediction = await this.convertToEnhancedPrediction(
        fixtureId,
        fixtureDate,
        homeTeam,
        awayTeam,
        league,
        matchPrediction,
        dixonColesParams,
        teamStrengths,
        leagueParams,
        Date.now() - startTime,
        homeEloStrength,
        awayEloStrength
      );

      return enhancedPrediction;

    } catch (error) {
      console.error(`Error generating enhanced prediction for fixture ${fixtureId}:`, error);
      return null;
    }
  }

  /**
   * Get team strengths and league parameters (with caching)
   */
  private static async getTeamStrengthsAndLeagueParams(
    leagueId: number,
    currentDate: Date
  ): Promise<{
    teamStrengths: Map<number, TeamStrength>;
    leagueParams: LeagueParameters;
  }> {
    const db = await connectDB();
    
    // Try to get cached league parameters
    const cachedLeagueParams = await db.collection<LeagueParametersDocument>('league_parameters')
      .findOne({ _id: leagueId });

    const shouldRecalculate = !cachedLeagueParams || 
      this.isDataStale(cachedLeagueParams.lastUpdated, this.CACHE_TTL_HOURS);

    if (shouldRecalculate) {
      console.log(`Recalculating team strengths and league parameters for league ${leagueId}`);
      return await this.calculateAndCacheStrengths(leagueId, currentDate);
    }

    // Load cached team strengths
    const teamStrengthDocs = await db.collection<TeamStrengthDocument>('team_strengths')
      .find({ leagueId })
      .toArray();

    const teamStrengths = new Map<number, TeamStrength>();
    for (const doc of teamStrengthDocs) {
      teamStrengths.set(doc.teamId, {
        teamId: doc.teamId,
        attack: doc.strength.attack,
        defense: doc.strength.defense,
        matchesPlayed: doc.statistics.matchesPlayed,
        lastUpdated: doc.lastUpdated
      });
    }

    const leagueParams: LeagueParameters = {
      leagueId: cachedLeagueParams.leagueId,
      homeAdvantage: cachedLeagueParams.parameters.homeAdvantage,
      averageGoalsPerGame: cachedLeagueParams.parameters.averageGoalsPerGame,
      rho: cachedLeagueParams.parameters.rho,
      matchesAnalyzed: cachedLeagueParams.statistics.matchesAnalyzed,
      lastUpdated: cachedLeagueParams.lastUpdated
    };

    return { teamStrengths, leagueParams };
  }

  /**
   * Calculate and cache team strengths and league parameters
   */
  private static async calculateAndCacheStrengths(
    leagueId: number,
    currentDate: Date
  ): Promise<{
    teamStrengths: Map<number, TeamStrength>;
    leagueParams: LeagueParameters;
  }> {
    const db = await connectDB();

    // Get historical match data
    const cutoffDate = new Date(currentDate);
    cutoffDate.setMonth(cutoffDate.getMonth() - this.HISTORICAL_MONTHS);

    const fixtures = await db.collection('fixtures').find({
      'league.id': leagueId,
      // Include all finished match types:
      // FT = Finished in regular time
      // AET = Finished after extra time (without penalties)
      // PEN = Finished after penalty shootout
      'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] },
      'date': { $gte: cutoffDate, $lte: currentDate },
      'goals.home': { $exists: true, $ne: null },
      'goals.away': { $exists: true, $ne: null }
    }).toArray();

    if (fixtures.length < this.MIN_MATCHES_FOR_PREDICTION) {
      console.warn(`Insufficient match data for league ${leagueId}: ${fixtures.length} matches (minimum ${this.MIN_MATCHES_FOR_PREDICTION})`);
      // Return default values for very limited data
      return {
        teamStrengths: new Map(),
        leagueParams: {
          leagueId,
          homeAdvantage: 1.3, // Proper default home advantage
          averageGoalsPerGame: 2.5,
          rho: -0.13,
          matchesAnalyzed: fixtures.length,
          lastUpdated: new Date()
        }
      };
    }

    // Convert to MatchData format
    const matchData: MatchData[] = fixtures.map(fixture => ({
      homeTeamId: fixture.teams.home.id,
      awayTeamId: fixture.teams.away.id,
      homeGoals: fixture.goals.home,
      awayGoals: fixture.goals.away,
      date: new Date(fixture.date),
      leagueId: fixture.league.id
    }));

    // Calculate team strengths and league parameters with seasonal adjustments
    const { teamStrengths, leagueParams } = await TeamStrengthService.calculateFromMatches(
      matchData,
      currentDate // Pass fixture date for seasonal adjustments
    );

    // Cache the results
    await this.cacheTeamStrengths(teamStrengths, leagueId);
    await this.cacheLeagueParameters(leagueParams);

    return { teamStrengths, leagueParams };
  }

  /**
   * Cache team strengths in database
   */
  private static async cacheTeamStrengths(
    teamStrengths: Map<number, TeamStrength>,
    leagueId: number
  ): Promise<void> {
    const db = await connectDB();
    const collection = db.collection('team_strengths');

    const bulkOps = [];
    for (const [teamId, strength] of teamStrengths) {
      const doc: TeamStrengthDocument = {
        _id: `${teamId}_${leagueId}`,
        teamId,
        leagueId,
        strength: {
          attack: strength.attack,
          defense: strength.defense
        },
        statistics: {
          matchesPlayed: strength.matchesPlayed,
          goalsScored: 0, // TODO: Calculate from historical data
          goalsConceded: 0, // TODO: Calculate from historical data
          homeMatchesPlayed: 0, // TODO: Calculate from historical data
          awayMatchesPlayed: 0 // TODO: Calculate from historical data
        },
        form: {
          last5Matches: [], // TODO: Calculate from historical data
          recentForm: '' // TODO: Calculate from historical data
        },
        lastUpdated: new Date(),
        calculatedAt: new Date()
      };

      bulkOps.push({
        updateOne: {
          filter: { _id: doc._id },
          update: { $set: doc },
          upsert: true
        }
      });
    }

    if (bulkOps.length > 0) {
      await collection.bulkWrite(bulkOps);
    }
  }

  /**
   * Cache league parameters in database
   */
  private static async cacheLeagueParameters(leagueParams: LeagueParameters): Promise<void> {
    const db = await connectDB();
    const collection = db.collection('league_parameters');

    const doc: LeagueParametersDocument = {
      _id: leagueParams.leagueId,
      leagueId: leagueParams.leagueId,
      parameters: {
        homeAdvantage: leagueParams.homeAdvantage,
        averageGoalsPerGame: leagueParams.averageGoalsPerGame,
        rho: leagueParams.rho
      },
      statistics: {
        matchesAnalyzed: leagueParams.matchesAnalyzed,
        dateRange: {
          from: new Date(), // TODO: Calculate actual range
          to: new Date()
        },
        totalGoals: 0, // TODO: Calculate from data
        homeWins: 0, // TODO: Calculate from data
        draws: 0, // TODO: Calculate from data
        awayWins: 0 // TODO: Calculate from data
      },
      lastUpdated: new Date(),
      calculatedAt: new Date()
    };

    await collection.updateOne(
      { _id: doc._id as any },
      { $set: doc },
      { upsert: true }
    );
  }

  /**
   * Convert Dixon-Coles prediction to enhanced prediction format
   */
  private static async convertToEnhancedPrediction(
    fixtureId: number,
    fixtureDate: Date,
    homeTeam: any,
    awayTeam: any,
    league: any,
    matchPrediction: MatchPrediction,
    dixonColesParams: DixonColesParams,
    teamStrengths: Map<number, TeamStrength>,
    leagueParams: LeagueParameters,
    processingTime: number,
    homeEloStrength?: EloEnhancedStrength | null,
    awayEloStrength?: EloEnhancedStrength | null
  ): Promise<EnhancedPrediction> {
    const homeStrength = teamStrengths.get(homeTeam.id);
    const awayStrength = teamStrengths.get(awayTeam.id);
    const matchesUsed = Math.min(
      homeStrength?.matchesPlayed || 0,
      awayStrength?.matchesPlayed || 0
    );

    // Convert matrix to flattened format for MongoDB
    const flatMatrix: { [homeGoals: string]: { [awayGoals: string]: number } } = {};
    for (let home = 0; home < matchPrediction.correctScore.matrix.length; home++) {
      flatMatrix[home.toString()] = {};
      for (let away = 0; away < matchPrediction.correctScore.matrix[home].length; away++) {
        flatMatrix[home.toString()][away.toString()] = matchPrediction.correctScore.matrix[home][away];
      }
    }

    const correctScore: CorrectScorePrediction = {
      mostLikely: matchPrediction.correctScore.mostLikely,
      top5Scores: matchPrediction.correctScore.top5Scores,
      matrix: flatMatrix,
      confidence: PredictionConfidence.calculateConfidence(
        matchPrediction.correctScore.mostLikely.probability,
        matchesUsed
      ),
      algorithm: 'dixon-coles'
    };

    const btts: BTTSPrediction = {
      prediction: matchPrediction.bothTeamsToScore.yes > 0.5,
      probability: matchPrediction.bothTeamsToScore.yes,
      confidence: PredictionConfidence.calculateConfidence(
        Math.max(matchPrediction.bothTeamsToScore.yes, matchPrediction.bothTeamsToScore.no),
        matchesUsed
      )
    };

    // Generate corner predictions if league supports corner statistics
    const cornerPrediction = await CornerPredictionService.generateCornerPrediction(
      homeTeam.id,
      awayTeam.id,
      league.id,
      fixtureDate
    );

    // Generate card predictions if league supports card statistics
    const cardPrediction = await CardPredictionService.generateCardPrediction(
      homeTeam.id,
      awayTeam.id,
      league.id,
      fixtureDate
    );

    // Calculate base confidence and apply data availability multiplier
    const baseConfidence = PredictionConfidence.calculateOverallConfidence(
      matchPrediction.correctScore.mostLikely.probability * 100,
      Math.max(matchPrediction.bothTeamsToScore.yes, matchPrediction.bothTeamsToScore.no) * 100,
      matchesUsed,
      0 // Fresh calculation
    );

    const dataConfidenceMultiplier = this.calculateDataConfidenceMultiplier(leagueParams.matchesAnalyzed);
    const overallConfidence = Math.min(100, baseConfidence * dataConfidenceMultiplier); // Cap at 100%

    // Calculate ELO influence for metadata
    const eloInfluence = homeEloStrength && awayEloStrength ?
      this.calculateEloWeight(homeStrength, awayStrength, league.id) : 0;

    return {
      _id: fixtureId,
      fixture: {
        id: fixtureId,
        date: fixtureDate,
        status: 'NS'
      },
      league: {
        id: league.id,
        name: league.name,
        country: league.country,
        logo: league.logo,
        flag: league.flag,
        season: league.season,
        round: league.round,
        standings: league.standings
      },
      teams: {
        home: {
          id: homeTeam.id,
          name: homeTeam.name,
          logo: homeTeam.logo
        },
        away: {
          id: awayTeam.id,
          name: awayTeam.name,
          logo: awayTeam.logo
        }
      },
      predictions: {
        correctScore,
        bothTeamsToScore: btts,
        matchOutcome: {
          homeWin: matchPrediction.probabilities.homeWin,
          draw: matchPrediction.probabilities.draw,
          awayWin: matchPrediction.probabilities.awayWin,
          confidence: PredictionConfidence.calculateConfidence(
            Math.max(
              matchPrediction.probabilities.homeWin,
              matchPrediction.probabilities.draw,
              matchPrediction.probabilities.awayWin
            ),
            matchesUsed
          )
        },
        expectedGoals: {
          home: matchPrediction.homeExpectedGoals,
          away: matchPrediction.awayExpectedGoals,
          total: matchPrediction.homeExpectedGoals + matchPrediction.awayExpectedGoals,
          confidence: PredictionConfidence.calculateConfidence(
            (matchPrediction.homeExpectedGoals + matchPrediction.awayExpectedGoals) / 5, // Normalize
            matchesUsed
          )
        },
        goalDistribution: matchPrediction.goalDistribution,
        corners: cornerPrediction || undefined, // Only include if corner prediction is available
        cards: cardPrediction || undefined // Only include if card prediction is available
      },
      dixonColesParams: {
        homeAttack: dixonColesParams.homeAttack,
        homeDefense: dixonColesParams.homeDefense,
        awayAttack: dixonColesParams.awayAttack,
        awayDefense: dixonColesParams.awayDefense,
        homeAdvantage: dixonColesParams.homeAdvantage,
        rho: dixonColesParams.rho,
        leagueId: leagueParams.leagueId,
        calculatedAt: new Date()
      },
      metadata: {
        algorithm: 'dixon-coles',
        dataSource: 'historical',
        confidence: overallConfidence,
        processingTime,
        lastUpdated: new Date(),
        modelVersion: '1.1.0', // Updated version with ELO integration
        matchesUsedForTraining: leagueParams.matchesAnalyzed,
        eloData: homeEloStrength && awayEloStrength ? {
          homeEloRating: homeEloStrength.eloRating,
          awayEloRating: awayEloStrength.eloRating,
          homeEloRank: homeEloStrength.eloRank,
          awayEloRank: awayEloStrength.eloRank,
          eloInfluence: eloInfluence,
          eloRatio: homeEloStrength.eloRating / awayEloStrength.eloRating
        } : null
      },
      createdAt: new Date(),
      lastUpdated: new Date()
    };
  }

  /**
   * Check if cached data is stale
   */
  private static isDataStale(lastUpdated: Date, maxAgeHours: number): boolean {
    const maxAge = maxAgeHours * 60 * 60 * 1000; // Convert to milliseconds
    return (Date.now() - lastUpdated.getTime()) > maxAge;
  }

  /**
   * Get team information from database
   */
  private static async getTeamInfo(teamId: number): Promise<any> {
    const db = await connectDB();
    const team = await db.collection('teams').findOne({ 'team.id': teamId });
    return team?.team || { id: teamId, name: `Team ${teamId}` };
  }

  /**
   * Get league information from database
   */
  private static async getLeagueInfo(leagueId: number): Promise<any> {
    const db = await connectDB();
    const league = await db.collection('leagues').findOne({ 'league.id': leagueId });
    return league?.league || { id: leagueId, name: `League ${leagueId}`, country: 'Unknown' };
  }

  /**
   * Apply ELO adjustments to Dixon-Coles parameters
   */
  private static applyEloAdjustments(
    originalParams: DixonColesParams,
    homeEloStrength: EloEnhancedStrength,
    awayEloStrength: EloEnhancedStrength,
    homeTeamStrength?: TeamStrength,
    awayTeamStrength?: TeamStrength,
    leagueId?: number
  ): DixonColesParams {
    try {
      // Calculate ELO ratio (how much stronger home team is)
      const eloRatio = homeEloStrength.eloRating / awayEloStrength.eloRating;

      // Convert to logarithmic adjustment (Dixon-Coles works in log space)
      const eloAdjustment = Math.log(eloRatio);

      // Determine ELO weight based on available data and league type
      const eloWeight = this.calculateEloWeight(
        homeTeamStrength,
        awayTeamStrength,
        leagueId
      );

      // Apply adjustments
      const adjustedParams: DixonColesParams = {
        homeAttack: originalParams.homeAttack + (eloWeight * eloAdjustment * 0.5),
        homeDefense: originalParams.homeDefense - (eloWeight * eloAdjustment * 0.3),
        awayAttack: originalParams.awayAttack - (eloWeight * eloAdjustment * 0.5),
        awayDefense: originalParams.awayDefense + (eloWeight * eloAdjustment * 0.3),
        homeAdvantage: originalParams.homeAdvantage,
        rho: originalParams.rho
      };

      console.log(`ELO Adjustment Applied - Ratio: ${eloRatio.toFixed(3)}, Weight: ${eloWeight.toFixed(3)}, Adjustment: ${eloAdjustment.toFixed(3)}`);

      return adjustedParams;

    } catch (error) {
      console.error('Error applying ELO adjustments:', error);
      return originalParams; // Fallback to original parameters
    }
  }

  /**
   * Calculate appropriate ELO weight based on competition type
   * ELO is only used for cross-competitions (Champions League, Europa League, etc.)
   */
  private static calculateEloWeight(
    homeTeamStrength?: TeamStrength,
    awayTeamStrength?: TeamStrength,
    leagueId?: number
  ): number {
    // Only use ELO for cross-competitions
    const leagueTier = leagueId ? getLeagueTier(leagueId) : 3;
    const isCrossCompetition = this.isCrossCompetition(leagueId);

    if (isCrossCompetition) {
      return this.ELO_WEIGHT_CROSS_COMPETITION; // 60% for cross-competitions
    }

    // No ELO for domestic leagues - rely purely on historical performance
    return 0.0;
  }

  /**
   * Determine if a league is a cross-competition that should use ELO
   */
  private static isCrossCompetition(leagueId?: number): boolean {
    if (!leagueId) return false;

    // Cross-competitions that should use ELO
    const crossCompetitions = [
      2,    // Champions League
      3,    // Europa League
      848,  // Conference League
      1,    // World Cup
      4,    // Euro Championship
      5,    // Nations League
      15,   // Copa America
      16,   // African Cup of Nations
      17,   // Asian Cup
      // Add more cross-competitions as needed
    ];

    // Also check by league tier (Tier 1 = international/cross-league)
    const leagueTier = getLeagueTier(leagueId);

    return crossCompetitions.includes(leagueId) || leagueTier === 1;
  }

  /**
   * Calculate confidence adjustment based on data availability
   */
  private static calculateDataConfidenceMultiplier(matchesAnalyzed: number): number {
    if (matchesAnalyzed >= 20) return 1.0;      // High confidence
    if (matchesAnalyzed >= 10) return 0.8;      // Medium-high confidence
    if (matchesAnalyzed >= 5) return 0.6;       // Medium confidence
    if (matchesAnalyzed >= 3) return 0.4;       // Low confidence
    return 0.2;                                 // Very low confidence
  }
}
