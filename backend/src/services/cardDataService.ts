/**
 * Card Data Service
 * 
 * Handles extraction and processing of card statistics from fixture data
 * Only processes leagues that have statistics_fixtures: true coverage
 */

import { CardStatistics, LeagueCardParameters, CardPredictionUtils } from '../models/CardPrediction';
import connectDB from '../config/database';

export class CardDataService {
  private static readonly SUPPORTED_MATCH_STATUSES = ['FT', 'AET', 'PEN'];
  
  /**
   * Extract card statistics from a fixture document
   */
  static extractCardData(fixture: any): CardStatistics | null {
    try {
      // Validate fixture has required data
      if (!fixture.statistics || fixture.statistics.length < 2) {
        return null;
      }
      
      if (!this.SUPPORTED_MATCH_STATUSES.includes(fixture.fixture?.status?.short)) {
        return null;
      }
      
      const homeStats = fixture.statistics[0]?.statistics || [];
      const awayStats = fixture.statistics[1]?.statistics || [];
      
      // Find card statistics
      const homeYellowStat = homeStats.find((s: any) => s.type === "Yellow Cards");
      const awayYellowStat = awayStats.find((s: any) => s.type === "Yellow Cards");
      const homeRedStat = homeStats.find((s: any) => s.type === "Red Cards");
      const awayRedStat = awayStats.find((s: any) => s.type === "Red Cards");
      
      // Parse card values (handle null values)
      const homeYellowCards = parseInt(homeYellowStat?.value) || 0;
      const awayYellowCards = parseInt(awayYellowStat?.value) || 0;
      const homeRedCards = parseInt(homeRedStat?.value) || 0;
      const awayRedCards = parseInt(awayRedStat?.value) || 0;
      
      // Validate card counts are reasonable
      if (isNaN(homeYellowCards) || isNaN(awayYellowCards) || 
          isNaN(homeRedCards) || isNaN(awayRedCards)) {
        return null;
      }
      
      const totalYellowCards = homeYellowCards + awayYellowCards;
      const totalRedCards = homeRedCards + awayRedCards;
      const totalCards = totalYellowCards + totalRedCards;
      
      const cardStats: CardStatistics = {
        fixtureId: fixture.fixture.id,
        homeYellowCards,
        awayYellowCards,
        homeRedCards,
        awayRedCards,
        totalCards,
        totalYellowCards,
        totalRedCards,
        homeTeamId: fixture.teams.home.id,
        awayTeamId: fixture.teams.away.id,
        leagueId: fixture.league.id,
        date: new Date(fixture.date)
      };
      
      // Validate using utility function
      if (!CardPredictionUtils.validateCardStatistics(cardStats)) {
        return null;
      }
      
      return cardStats;
      
    } catch (error) {
      console.error('Error extracting card data from fixture:', error);
      return null;
    }
  }
  
  /**
   * Get all fixtures with card data for a specific league
   */
  static async getLeagueCardData(
    leagueId: number,
    cutoffDate: Date,
    currentDate: Date = new Date()
  ): Promise<CardStatistics[]> {
    try {
      const db = await connectDB();
      
      const fixtures = await db.collection('fixtures').find({
        'league.id': leagueId,
        'fixture.status.short': { $in: this.SUPPORTED_MATCH_STATUSES },
        'date': { $gte: cutoffDate, $lte: currentDate },
        'statistics.statistics.type': { $in: ['Yellow Cards', 'Red Cards'] }
      }).toArray();
      
      const cardData: CardStatistics[] = [];
      
      for (const fixture of fixtures) {
        const data = this.extractCardData(fixture);
        if (data) {
          cardData.push(data);
        }
      }
      
      console.log(`Extracted card data for league ${leagueId}: ${cardData.length} matches`);
      return cardData;
      
    } catch (error) {
      console.error(`Error getting card data for league ${leagueId}:`, error);
      return [];
    }
  }
  
  /**
   * Check if a league has card statistics support
   */
  static async checkLeagueCardSupport(leagueId: number): Promise<boolean> {
    try {
      const db = await connectDB();
      
      // Check if league has statistics_fixtures: true in any recent season
      const league = await db.collection('leagues').findOne({
        'league.id': leagueId,
        'seasons.coverage.fixtures.statistics_fixtures': true,
        'seasons.year': { $gte: 2024 }
      });
      
      return !!league;
      
    } catch (error) {
      console.error(`Error checking card support for league ${leagueId}:`, error);
      return false;
    }
  }
  
  /**
   * Get all leagues that support card statistics
   */
  static async getLeaguesWithCardSupport(): Promise<number[]> {
    try {
      const db = await connectDB();
      
      const leagues = await db.collection('leagues').find({
        'seasons.coverage.fixtures.statistics_fixtures': true,
        'seasons.year': { $gte: 2024 }
      }).toArray();
      
      const supportedLeagueIds = leagues.map(league => league.league.id);
      
      console.log(`Found ${supportedLeagueIds.length} leagues with card support:`, supportedLeagueIds);
      return supportedLeagueIds;
      
    } catch (error) {
      console.error('Error getting leagues with card support:', error);
      return [];
    }
  }
  
  /**
   * Calculate basic league card parameters
   */
  static calculateLeagueCardParameters(cardData: CardStatistics[]): LeagueCardParameters {
    if (cardData.length === 0) {
      return {
        leagueId: 0,
        averageCards: 4.2, // Global average
        averageYellowCards: 3.8,
        averageRedCards: 0.4,
        homeAdvantage: 0.95, // Home teams typically get slightly fewer cards
        awayPenalty: 1.15,   // Away teams typically get more cards
        varianceAdjustment: 1.0,
        matchesAnalyzed: 0,
        hasCardSupport: false,
        lastUpdated: new Date()
      };
    }
    
    const leagueId = cardData[0].leagueId;
    const totalCards = cardData.reduce((sum, match) => sum + match.totalCards, 0);
    const totalYellowCards = cardData.reduce((sum, match) => sum + match.totalYellowCards, 0);
    const totalRedCards = cardData.reduce((sum, match) => sum + match.totalRedCards, 0);
    const homeCards = cardData.reduce((sum, match) => sum + match.homeYellowCards + match.homeRedCards, 0);
    const awayCards = cardData.reduce((sum, match) => sum + match.awayYellowCards + match.awayRedCards, 0);
    
    const averageCards = totalCards / cardData.length;
    const averageYellowCards = totalYellowCards / cardData.length;
    const averageRedCards = totalRedCards / cardData.length;
    const averageHomeCards = homeCards / cardData.length;
    const averageAwayCards = awayCards / cardData.length;
    
    // Calculate home advantage (home teams typically get fewer cards)
    const homeAdvantage = averageAwayCards > 0 ? averageHomeCards / averageAwayCards : 0.95;
    const awayPenalty = averageHomeCards > 0 ? averageAwayCards / averageHomeCards : 1.15;
    
    // Calculate variance for adjustment factor
    const cardCounts = cardData.map(match => match.totalCards);
    const variance = this.calculateVariance(cardCounts, averageCards);
    const varianceAdjustment = Math.min(Math.max(variance / 8, 0.8), 1.2); // Normalize variance
    
    return {
      leagueId,
      averageCards,
      averageYellowCards,
      averageRedCards,
      homeAdvantage: Math.min(Math.max(homeAdvantage, 0.8), 1.2), // Cap between 0.8 and 1.2
      awayPenalty: Math.min(Math.max(awayPenalty, 1.0), 1.5), // Cap between 1.0 and 1.5
      varianceAdjustment,
      matchesAnalyzed: cardData.length,
      hasCardSupport: true,
      lastUpdated: new Date()
    };
  }
  
  /**
   * Calculate variance of an array of numbers
   */
  private static calculateVariance(values: number[], mean: number): number {
    if (values.length === 0) return 0;
    
    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    return squaredDifferences.reduce((sum, diff) => sum + diff, 0) / values.length;
  }
  
  /**
   * Get card statistics for a specific team in a league
   */
  static getTeamCardStats(
    cardData: CardStatistics[],
    teamId: number
  ): {
    matchesPlayed: number;
    cardsReceived: number[];
    cardsCaused: number[];
    homeCardsReceived: number[];
    awayCardsReceived: number[];
    yellowCardsReceived: number[];
    redCardsReceived: number[];
  } {
    const stats = {
      matchesPlayed: 0,
      cardsReceived: [] as number[],
      cardsCaused: [] as number[],
      homeCardsReceived: [] as number[],
      awayCardsReceived: [] as number[],
      yellowCardsReceived: [] as number[],
      redCardsReceived: [] as number[]
    };
    
    for (const match of cardData) {
      if (match.homeTeamId === teamId) {
        // Team playing at home
        stats.matchesPlayed++;
        const cardsReceived = match.homeYellowCards + match.homeRedCards;
        const cardsCaused = match.awayYellowCards + match.awayRedCards;
        
        stats.cardsReceived.push(cardsReceived);
        stats.cardsCaused.push(cardsCaused);
        stats.homeCardsReceived.push(cardsReceived);
        stats.yellowCardsReceived.push(match.homeYellowCards);
        stats.redCardsReceived.push(match.homeRedCards);
      } else if (match.awayTeamId === teamId) {
        // Team playing away
        stats.matchesPlayed++;
        const cardsReceived = match.awayYellowCards + match.awayRedCards;
        const cardsCaused = match.homeYellowCards + match.homeRedCards;
        
        stats.cardsReceived.push(cardsReceived);
        stats.cardsCaused.push(cardsCaused);
        stats.awayCardsReceived.push(cardsReceived);
        stats.yellowCardsReceived.push(match.awayYellowCards);
        stats.redCardsReceived.push(match.awayRedCards);
      }
    }
    
    return stats;
  }
  
  /**
   * Validate card data quality
   */
  static validateCardDataQuality(cardData: CardStatistics[]): {
    isValid: boolean;
    quality: 'low' | 'medium' | 'high';
    issues: string[];
  } {
    const issues: string[] = [];
    
    if (cardData.length < 5) {
      issues.push('Insufficient data: less than 5 matches');
    }
    
    // Check for reasonable card ranges
    const totalCards = cardData.map(match => match.totalCards);
    const avgCards = totalCards.reduce((sum, cards) => sum + cards, 0) / totalCards.length;
    
    if (avgCards < 1 || avgCards > 10) {
      issues.push(`Unusual average cards: ${avgCards.toFixed(1)}`);
    }
    
    // Check for outliers
    const outliers = totalCards.filter(cards => cards > 12 || cards < 0);
    if (outliers.length > cardData.length * 0.1) {
      issues.push(`Too many outliers: ${outliers.length} matches`);
    }
    
    // Check for missing card data
    const missingData = cardData.filter(match => 
      match.totalCards === 0 && match.totalYellowCards === 0 && match.totalRedCards === 0
    );
    if (missingData.length > cardData.length * 0.2) {
      issues.push(`Too many matches with no card data: ${missingData.length}`);
    }
    
    let quality: 'low' | 'medium' | 'high' = 'high';
    if (issues.length > 2 || cardData.length < 10) {
      quality = 'low';
    } else if (issues.length > 0 || cardData.length < 20) {
      quality = 'medium';
    }
    
    return {
      isValid: issues.length === 0 || (issues.length === 1 && cardData.length >= 10),
      quality,
      issues
    };
  }
}
