import { fetchData } from './apiFootball';
import { TransferPlayer, TransferTeam, TransferInfo } from '../models/Transfer';

// Define the transfer response structure from the API
export interface TransferResponse {
    player: TransferPlayer;
    update: string;
    transfers: TransferInfo[];
}

// Define allowed query parameters based on documentation
interface FetchTransfersParams {
    player?: number; // Player ID
    team?: number;   // Team ID
}

export async function fetchTransfers(params: FetchTransfersParams): Promise<TransferResponse[]> {
    // Validate parameters
    if (!params.player && !params.team) {
        throw new Error("At least one parameter (player or team) is required.");
    }
    
    return fetchData<TransferResponse[]>('/transfers', params);
}
