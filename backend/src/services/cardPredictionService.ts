/**
 * Card Prediction Service
 * 
 * Generates card predictions using Poisson distribution
 * Focuses on Total Cards Over/Under markets only
 */

import { 
  CardPrediction, 
  CardMarketPrediction, 
  TeamCardDiscipline,
  LeagueCardParameters,
  CardPredictionUtils
} from '../models/CardPrediction';
import { TeamDisciplineService } from './teamDisciplineService';
import { CardDataService } from './cardDataService';

export class CardPredictionService {
  private static readonly MODEL_VERSION = '1.0.0';
  private static readonly MIN_CONFIDENCE_THRESHOLD = 0.3;
  
  /**
   * Generate card prediction for a specific fixture
   */
  static async generateCardPrediction(
    homeTeamId: number,
    awayTeamId: number,
    leagueId: number,
    fixtureDate: Date
  ): Promise<CardPrediction | null> {
    try {
      // Check if league supports card predictions
      const hasSupport = await CardDataService.checkLeagueCardSupport(leagueId);
      if (!hasSupport) {
        console.log(`League ${leagueId} does not support card predictions`);
        return null;
      }
      
      // Get team card discipline
      const teamDiscipline = await TeamDisciplineService.getTeamCardDiscipline(
        leagueId,
        fixtureDate
      );
      
      const homeTeamDiscipline = teamDiscipline.get(homeTeamId);
      const awayTeamDiscipline = teamDiscipline.get(awayTeamId);
      
      if (!homeTeamDiscipline || !awayTeamDiscipline) {
        console.warn(`Insufficient card data for teams ${homeTeamId} vs ${awayTeamId} in league ${leagueId}`);
        return this.generateLeagueAveragePrediction(leagueId);
      }
      
      // Check confidence threshold
      const minConfidence = Math.min(homeTeamDiscipline.confidence, awayTeamDiscipline.confidence);
      if (minConfidence < this.MIN_CONFIDENCE_THRESHOLD) {
        console.warn(`Low confidence for card prediction: ${minConfidence.toFixed(2)}`);
        return this.generateLeagueAveragePrediction(leagueId);
      }
      
      // Calculate expected cards
      const { homeExpected, awayExpected, totalExpected, yellowExpected, redExpected } = 
        this.calculateExpectedCards(homeTeamDiscipline, awayTeamDiscipline, leagueId);
      
      // Generate market predictions
      const markets = this.generateMarketPredictions(totalExpected, minConfidence);
      
      // Generate card distribution
      const distribution = this.generateCardDistribution(totalExpected);
      
      // Calculate overall confidence
      const overallConfidence = this.calculateOverallConfidence(
        homeTeamDiscipline,
        awayTeamDiscipline,
        totalExpected
      );
      
      return {
        expectedTotalCards: totalExpected,
        expectedYellowCards: yellowExpected,
        expectedRedCards: redExpected,
        homeExpectedCards: homeExpected,
        awayExpectedCards: awayExpected,
        confidence: overallConfidence,
        markets,
        distribution,
        algorithm: 'poisson',
        dataSource: 'historical',
        modelVersion: this.MODEL_VERSION,
        calculatedAt: new Date()
      };
      
    } catch (error) {
      console.error(`Error generating card prediction for ${homeTeamId} vs ${awayTeamId}:`, error);
      return null;
    }
  }
  
  /**
   * Calculate expected cards for both teams
   */
  private static calculateExpectedCards(
    homeTeamDiscipline: TeamCardDiscipline,
    awayTeamDiscipline: TeamCardDiscipline,
    leagueId: number
  ): { 
    homeExpected: number; 
    awayExpected: number; 
    totalExpected: number;
    yellowExpected: number;
    redExpected: number;
  } {
    
    // Get league average cards (fallback to global average if not available)
    const leagueAverage = 4.2; // TODO: Get from league parameters
    const baseCardRate = leagueAverage / 2; // Average per team
    
    // Calculate home team expected cards
    // Home discipline * Away aggression * Home penalty factor
    const homeExpected = baseCardRate * 
      homeTeamDiscipline.cardAttack * 
      awayTeamDiscipline.cardDefense * 
      homeTeamDiscipline.homeCardPenalty;
    
    // Calculate away team expected cards
    // Away discipline * Home aggression * Away penalty factor
    const awayExpected = baseCardRate * 
      awayTeamDiscipline.cardAttack * 
      homeTeamDiscipline.cardDefense * 
      awayTeamDiscipline.awayCardPenalty;
    
    const totalExpected = homeExpected + awayExpected;
    
    // Estimate yellow and red card breakdown
    const yellowRatio = 0.9; // Typically 90% of cards are yellow
    const redRatio = 0.1;    // Typically 10% of cards are red
    
    const yellowExpected = totalExpected * yellowRatio;
    const redExpected = totalExpected * redRatio;
    
    // Ensure reasonable bounds
    const boundedHomeExpected = Math.max(0.5, Math.min(8, homeExpected));
    const boundedAwayExpected = Math.max(0.5, Math.min(8, awayExpected));
    const boundedTotalExpected = boundedHomeExpected + boundedAwayExpected;
    const boundedYellowExpected = Math.max(0.5, Math.min(10, yellowExpected));
    const boundedRedExpected = Math.max(0.05, Math.min(2, redExpected));
    
    return {
      homeExpected: boundedHomeExpected,
      awayExpected: boundedAwayExpected,
      totalExpected: boundedTotalExpected,
      yellowExpected: boundedYellowExpected,
      redExpected: boundedRedExpected
    };
  }
  
  /**
   * Generate market predictions for Total Cards Over/Under
   */
  private static generateMarketPredictions(
    totalExpected: number,
    confidence: number
  ): CardPrediction['markets'] {
    
    const thresholds = [2.5, 3.5, 4.5, 5.5];
    const markets: any = {};
    
    for (const threshold of thresholds) {
      const overProbability = CardPredictionUtils.calculateOverProbability(
        totalExpected,
        threshold
      );
      
      const marketPrediction: CardMarketPrediction = {
        threshold,
        overProbability,
        underProbability: 1 - overProbability,
        value: CardPredictionUtils.calculateValueRating(overProbability), // No market odds available yet
        confidence: this.getMarketConfidence(confidence, overProbability)
      };
      
      const marketKey = `over${threshold.toString().replace('.', '_')}`;
      markets[marketKey] = marketPrediction;
    }
    
    return markets;
  }
  
  /**
   * Generate card distribution (probability for each card count)
   */
  private static generateCardDistribution(
    totalExpected: number,
    maxCards: number = 12
  ): Array<{ cards: number; probability: number }> {
    
    const distribution = [];
    
    for (let cards = 0; cards <= maxCards; cards++) {
      const probability = CardPredictionUtils.poissonPMF(totalExpected, cards);
      
      if (probability > 0.001) { // Only include probabilities > 0.1%
        distribution.push({
          cards,
          probability
        });
      }
    }
    
    return distribution;
  }
  
  /**
   * Calculate overall prediction confidence
   */
  private static calculateOverallConfidence(
    homeTeamDiscipline: TeamCardDiscipline,
    awayTeamDiscipline: TeamCardDiscipline,
    totalExpected: number
  ): number {
    
    // Base confidence from team data quality
    const teamConfidence = (homeTeamDiscipline.confidence + awayTeamDiscipline.confidence) / 2;
    
    // Match count factor
    const minMatches = Math.min(homeTeamDiscipline.matchCount, awayTeamDiscipline.matchCount);
    const matchFactor = Math.min(minMatches / 15, 1); // Max confidence at 15+ matches
    
    // Expected value reasonableness factor
    const reasonablenessFactor = totalExpected >= 2 && totalExpected <= 8 ? 1 : 0.8;
    
    // Combine factors
    const overallConfidence = teamConfidence * matchFactor * reasonablenessFactor;
    
    return Math.round(overallConfidence * 100); // Convert to percentage
  }
  
  /**
   * Get market-specific confidence level
   */
  private static getMarketConfidence(
    overallConfidence: number,
    probability: number
  ): 'low' | 'medium' | 'high' {
    
    // Higher confidence for probabilities closer to 0.5 (more certain outcomes)
    const probabilityFactor = 1 - Math.abs(probability - 0.5) * 2;
    const adjustedConfidence = overallConfidence * probabilityFactor;
    
    if (adjustedConfidence > 0.7) return 'high';
    if (adjustedConfidence > 0.4) return 'medium';
    return 'low';
  }
  
  /**
   * Generate league average prediction when team data is insufficient
   */
  private static async generateLeagueAveragePrediction(
    leagueId: number
  ): Promise<CardPrediction | null> {
    try {
      // Use global averages as fallback
      const totalExpected = 4.2;   // Global average cards per match
      const yellowExpected = 3.8;  // Global average yellow cards
      const redExpected = 0.4;     // Global average red cards
      const homeExpected = 2.0;    // Slightly lower for home team
      const awayExpected = 2.2;    // Slightly higher for away team
      
      const markets = this.generateMarketPredictions(totalExpected, 0.3); // Low confidence
      const distribution = this.generateCardDistribution(totalExpected);
      
      return {
        expectedTotalCards: totalExpected,
        expectedYellowCards: yellowExpected,
        expectedRedCards: redExpected,
        homeExpectedCards: homeExpected,
        awayExpectedCards: awayExpected,
        confidence: 30, // Low confidence for league average
        markets,
        distribution,
        algorithm: 'poisson',
        dataSource: 'league_average',
        modelVersion: this.MODEL_VERSION,
        calculatedAt: new Date()
      };
      
    } catch (error) {
      console.error(`Error generating league average card prediction for league ${leagueId}:`, error);
      return null;
    }
  }
  
  /**
   * Validate card prediction
   */
  static validateCardPrediction(prediction: CardPrediction): boolean {
    try {
      // Check expected values are reasonable
      if (prediction.expectedTotalCards < 1 || prediction.expectedTotalCards > 12) {
        return false;
      }
      
      if (prediction.homeExpectedCards < 0.2 || prediction.homeExpectedCards > 8) {
        return false;
      }
      
      if (prediction.awayExpectedCards < 0.2 || prediction.awayExpectedCards > 8) {
        return false;
      }
      
      if (prediction.expectedYellowCards < 0.5 || prediction.expectedYellowCards > 10) {
        return false;
      }
      
      if (prediction.expectedRedCards < 0.01 || prediction.expectedRedCards > 3) {
        return false;
      }
      
      // Check confidence is reasonable
      if (prediction.confidence < 0 || prediction.confidence > 100) {
        return false;
      }
      
      // Check market probabilities sum correctly
      for (const market of Object.values(prediction.markets)) {
        const total = market.overProbability + market.underProbability;
        if (Math.abs(total - 1) > 0.01) { // Allow small rounding errors
          return false;
        }
      }
      
      // Check distribution probabilities
      const totalProbability = prediction.distribution.reduce(
        (sum, item) => sum + item.probability, 
        0
      );
      if (Math.abs(totalProbability - 1) > 0.1) { // Allow for truncation
        return false;
      }
      
      return true;
      
    } catch (error) {
      console.error('Error validating card prediction:', error);
      return false;
    }
  }
}
