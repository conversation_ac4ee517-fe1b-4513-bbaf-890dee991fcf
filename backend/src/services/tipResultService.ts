import { getTipsCollection, TipType, TipStatus, Tip, updateTipResult } from '../models/Tip';
import { recordTipResult } from '../models/TipResult';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { ObjectId } from 'mongodb';

/**
 * Service to calculate tip results when matches finish
 * This service analyzes match data and determines if tips were won, lost, or void
 */
export class TipResultService {
  
  /**
   * Process all pending tips for a finished fixture
   */
  static async processFixtureTips(fixtureId: number): Promise<void> {
    try {
      console.log(`Processing tips for fixture ${fixtureId}`);
      
      // Get the finished fixture
      const fixturesCollection = getFixturesCollection();
      const fixture = await fixturesCollection.findOne({ _id: fixtureId });
      
      if (!fixture) {
        console.error(`Fixture ${fixtureId} not found`);
        return;
      }

      // Only process finished matches
      const isFinished = ['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short);
      if (!isFinished) {
        console.log(`Fixture ${fixtureId} is not finished yet (${fixture.fixture.status.short})`);
        return;
      }

      // Get all pending tips for this fixture
      const tipsCollection = getTipsCollection();
      const pendingTips = await tipsCollection.find({
        fixtureId,
        status: TipStatus.PENDING
      }).toArray();

      console.log(`Found ${pendingTips.length} pending tips for fixture ${fixtureId}`);

      // Process each tip
      for (const tip of pendingTips) {
        await this.processSingleTip(tip, fixture);
      }

      console.log(`Completed processing tips for fixture ${fixtureId}`);
    } catch (error) {
      console.error(`Error processing tips for fixture ${fixtureId}:`, error);
    }
  }

  /**
   * Process a single tip and determine its result
   */
  private static async processSingleTip(tip: Tip, fixture: Fixture): Promise<void> {
    try {
      const result = this.calculateTipResult(tip, fixture);
      
      // Update the tip status
      await updateTipResult(tip._id!.toString(), result.status, result.actualValue);
      
      // Record the result for statistics
      await recordTipResult(
        tip._id!.toString(),
        tip.userId.toString(),
        tip.fixtureId,
        tip.tipType,
        tip.odds,
        tip.stake || 0,
        result.status,
        result.actualValue,
        tip.createdAt
      );

      console.log(`Processed tip ${tip._id}: ${tip.tipType} - ${result.status}`);
    } catch (error) {
      console.error(`Error processing tip ${tip._id}:`, error);
    }
  }

  /**
   * Calculate the result of a tip based on match data
   */
  private static calculateTipResult(tip: Tip, fixture: Fixture): { status: TipStatus; actualValue?: any } {
    const homeGoals = fixture.goals.home || 0;
    const awayGoals = fixture.goals.away || 0;
    const totalGoals = homeGoals + awayGoals;

    switch (tip.tipType) {
      case TipType.BTTS:
        return this.calculateBTTSResult(tip, homeGoals, awayGoals);
      
      case TipType.OVER_UNDER_GOALS:
        return this.calculateOverUnderGoalsResult(tip, totalGoals);
      
      case TipType.MATCH_RESULT:
        return this.calculateMatchResultResult(tip, homeGoals, awayGoals);
      
      case TipType.OVER_UNDER_CORNERS:
        return this.calculateOverUnderCornersResult(tip, fixture);
      
      case TipType.OVER_UNDER_CARDS:
        return this.calculateOverUnderCardsResult(tip, fixture);
      
      case TipType.DOUBLE_CHANCE:
        return this.calculateDoubleChanceResult(tip, homeGoals, awayGoals);
      
      case TipType.FIRST_GOAL:
        return this.calculateFirstGoalResult(tip, fixture);
      
      case TipType.HANDICAP:
        return this.calculateHandicapResult(tip, homeGoals, awayGoals);
      
      case TipType.CORRECT_SCORE:
        return this.calculateCorrectScoreResult(tip, homeGoals, awayGoals);
      
      case TipType.HALF_TIME_RESULT:
        return this.calculateHalfTimeResult(tip, fixture);

      default:
        console.warn(`Unknown tip type: ${tip.tipType}`);
        return { status: TipStatus.VOID };
    }
  }

  private static calculateBTTSResult(tip: Tip, homeGoals: number, awayGoals: number): { status: TipStatus; actualValue: boolean } {
    const bothTeamsScored = homeGoals > 0 && awayGoals > 0;
    const tipPrediction = tip.details.bttsValue === 'yes';
    
    return {
      status: bothTeamsScored === tipPrediction ? TipStatus.WON : TipStatus.LOST,
      actualValue: bothTeamsScored
    };
  }

  private static calculateOverUnderGoalsResult(tip: Tip, totalGoals: number): { status: TipStatus; actualValue: number } {
    const line = tip.details.line!;
    const isOver = tip.details.overUnder === 'over';
    
    if (totalGoals === line) {
      return { status: TipStatus.PUSH, actualValue: totalGoals };
    }
    
    const overWins = totalGoals > line;
    const tipWins = isOver ? overWins : !overWins;
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: totalGoals
    };
  }

  private static calculateMatchResultResult(tip: Tip, homeGoals: number, awayGoals: number): { status: TipStatus; actualValue: string } {
    let actualResult: string;
    if (homeGoals > awayGoals) {
      actualResult = 'home';
    } else if (awayGoals > homeGoals) {
      actualResult = 'away';
    } else {
      actualResult = 'draw';
    }
    
    return {
      status: actualResult === tip.details.result ? TipStatus.WON : TipStatus.LOST,
      actualValue: actualResult
    };
  }

  private static calculateOverUnderCornersResult(tip: Tip, fixture: Fixture): { status: TipStatus; actualValue?: number } {
    // Extract corner count from match statistics
    const homeCorners = this.extractStatistic(fixture, 'home', 'Corner Kicks');
    const awayCorners = this.extractStatistic(fixture, 'away', 'Corner Kicks');
    
    if (homeCorners === null || awayCorners === null) {
      return { status: TipStatus.VOID, actualValue: undefined };
    }
    
    const totalCorners = homeCorners + awayCorners;
    const line = tip.details.line!;
    const isOver = tip.details.overUnder === 'over';
    
    if (totalCorners === line) {
      return { status: TipStatus.PUSH, actualValue: totalCorners };
    }
    
    const overWins = totalCorners > line;
    const tipWins = isOver ? overWins : !overWins;
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: totalCorners
    };
  }

  private static calculateOverUnderCardsResult(tip: Tip, fixture: Fixture): { status: TipStatus; actualValue?: number } {
    // Count yellow and red cards from events
    let totalCards = 0;
    
    if (fixture.events) {
      totalCards = fixture.events.filter(event => 
        event.type === 'Card' && 
        (event.detail === 'Yellow Card' || event.detail === 'Red Card')
      ).length;
    }
    
    const line = tip.details.line!;
    const isOver = tip.details.overUnder === 'over';
    
    if (totalCards === line) {
      return { status: TipStatus.PUSH, actualValue: totalCards };
    }
    
    const overWins = totalCards > line;
    const tipWins = isOver ? overWins : !overWins;
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: totalCards
    };
  }

  private static calculateDoubleChanceResult(tip: Tip, homeGoals: number, awayGoals: number): { status: TipStatus; actualValue: string } {
    const doubleChance = tip.details.doubleChance!;
    let actualResult: string;
    
    if (homeGoals > awayGoals) {
      actualResult = 'home';
    } else if (awayGoals > homeGoals) {
      actualResult = 'away';
    } else {
      actualResult = 'draw';
    }
    
    let tipWins = false;
    switch (doubleChance) {
      case '1X': // Home or Draw
        tipWins = actualResult === 'home' || actualResult === 'draw';
        break;
      case 'X2': // Draw or Away
        tipWins = actualResult === 'draw' || actualResult === 'away';
        break;
      case '12': // Home or Away
        tipWins = actualResult === 'home' || actualResult === 'away';
        break;
    }
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: actualResult
    };
  }

  private static calculateFirstGoalResult(tip: Tip, fixture: Fixture): { status: TipStatus; actualValue?: string } {
    if (!fixture.events || fixture.events.length === 0) {
      return { status: TipStatus.VOID, actualValue: undefined };
    }
    
    // Find the first goal
    const firstGoal = fixture.events
      .filter(event => event.type === 'Goal' && event.detail !== 'Own Goal')
      .sort((a, b) => a.time.elapsed - b.time.elapsed)[0];
    
    if (!firstGoal) {
      return { status: TipStatus.LOST, actualValue: 'no_goal' };
    }
    
    const firstGoalTeam = firstGoal.team.id === fixture.teams.home.id ? 'home' : 'away';
    const tipWins = firstGoalTeam === tip.details.firstGoalTeam;
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: firstGoalTeam
    };
  }

  private static calculateHandicapResult(tip: Tip, homeGoals: number, awayGoals: number): { status: TipStatus; actualValue: number } {
    const handicapValue = tip.details.handicapValue!;
    const handicapTeam = tip.details.handicapTeam!;
    
    let adjustedGoals: number;
    if (handicapTeam === 'home') {
      adjustedGoals = homeGoals + handicapValue - awayGoals;
    } else {
      adjustedGoals = awayGoals + handicapValue - homeGoals;
    }
    
    if (adjustedGoals === 0) {
      return { status: TipStatus.PUSH, actualValue: adjustedGoals };
    }
    
    return {
      status: adjustedGoals > 0 ? TipStatus.WON : TipStatus.LOST,
      actualValue: adjustedGoals
    };
  }

  private static calculateCorrectScoreResult(tip: Tip, homeGoals: number, awayGoals: number): { status: TipStatus; actualValue: string } {
    const predictedHome = tip.details.homeScore!;
    const predictedAway = tip.details.awayScore!;
    const actualScore = `${homeGoals}-${awayGoals}`;
    
    const tipWins = homeGoals === predictedHome && awayGoals === predictedAway;
    
    return {
      status: tipWins ? TipStatus.WON : TipStatus.LOST,
      actualValue: actualScore
    };
  }

  private static calculateHalfTimeResult(tip: Tip, fixture: Fixture): { status: TipStatus; actualValue?: string } {
    // This would require half-time score data
    // For now, return void as we might not have this data readily available
    return { status: TipStatus.VOID, actualValue: undefined };
  }



  /**
   * Helper method to extract statistics from fixture data
   */
  private static extractStatistic(fixture: Fixture, team: 'home' | 'away', statType: string): number | null {
    if (!fixture.statistics) {
      return null;
    }
    
    const teamStats = fixture.statistics.find(stat => 
      stat.team.id === (team === 'home' ? fixture.teams.home.id : fixture.teams.away.id)
    );
    
    if (!teamStats) {
      return null;
    }
    
    const stat = teamStats.statistics.find(s => s.type === statType);
    if (!stat || stat.value === null) {
      return null;
    }
    
    return typeof stat.value === 'number' ? stat.value : parseInt(stat.value.toString());
  }
}
