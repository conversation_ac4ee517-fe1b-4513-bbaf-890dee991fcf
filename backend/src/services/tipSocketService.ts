import { Server as SocketIOServer } from 'socket.io';
import jwt from 'jsonwebtoken';
import { findUserById } from '../models/User';
import { Tip, TipWithDetails } from '../models/Tip';
import { TipsterRanking, TipResult } from '../models/TipResult';

// Define the structure of a socket with tip subscription data
interface TipSubscription {
  userId?: string;
  fixtureIds: Set<number>;
  isSubscribedToRankings: boolean;
}

// Map to track socket subscriptions
const socketSubscriptions = new Map<string, TipSubscription>();

/**
 * Setup tip socket service
 * This service handles real-time tip updates, new tips, and ranking changes
 */
export function setupTipSocketService(io: SocketIOServer): void {
  // Create a namespace for tip updates
  const tipNamespace = io.of('/tips');

  // Optional authentication middleware (allows both authenticated and guest users)
  tipNamespace.use(async (socket, next) => {
    const token = socket.handshake.auth.token;

    if (token) {
      try {
        // Verify the JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret_key') as { id: string };

        // Find the user in the database
        const user = await findUserById(decoded.id);
        if (user) {
          // Attach user data to the socket
          (socket as any).userId = decoded.id;
          (socket as any).userName = user.name || 'Anonymous';
        }
      } catch (error) {
        console.warn('Invalid token provided to tip socket, continuing as guest');
      }
    }

    next();
  });

  // Handle socket connections
  tipNamespace.on('connection', (socket) => {
    const userId = (socket as any).userId;
    console.log(`Client connected to tip updates: ${socket.id}${userId ? ` (User: ${userId})` : ' (Guest)'}`);

    // Initialize subscription data for this socket
    socketSubscriptions.set(socket.id, {
      userId,
      fixtureIds: new Set<number>(),
      isSubscribedToRankings: false
    });

    // Handle subscribing to tips for a specific fixture
    socket.on('subscribe-fixture-tips', (fixtureId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.fixtureIds.add(fixtureId);
          socket.join(`fixture-tips:${fixtureId}`);
          console.log(`Client ${socket.id} subscribed to tips for fixture: ${fixtureId}`);
          socket.emit('subscription-success', { type: 'fixture-tips', id: fixtureId });
        }
      } catch (error) {
        console.error('Error subscribing to fixture tips:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to fixture tips' });
      }
    });

    // Handle unsubscribing from fixture tips
    socket.on('unsubscribe-fixture-tips', (fixtureId: number) => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.fixtureIds.delete(fixtureId);
          socket.leave(`fixture-tips:${fixtureId}`);
          console.log(`Client ${socket.id} unsubscribed from tips for fixture: ${fixtureId}`);
          socket.emit('unsubscription-success', { type: 'fixture-tips', id: fixtureId });
        }
      } catch (error) {
        console.error('Error unsubscribing from fixture tips:', error);
        socket.emit('unsubscription-error', { message: 'Failed to unsubscribe from fixture tips' });
      }
    });

    // Handle subscribing to tipster rankings
    socket.on('subscribe-rankings', () => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.isSubscribedToRankings = true;
          socket.join('tipster-rankings');
          console.log(`Client ${socket.id} subscribed to tipster rankings`);
          socket.emit('subscription-success', { type: 'rankings' });
        }
      } catch (error) {
        console.error('Error subscribing to rankings:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to rankings' });
      }
    });

    // Handle unsubscribing from tipster rankings
    socket.on('unsubscribe-rankings', () => {
      try {
        const subscription = socketSubscriptions.get(socket.id);
        if (subscription) {
          subscription.isSubscribedToRankings = false;
          socket.leave('tipster-rankings');
          console.log(`Client ${socket.id} unsubscribed from tipster rankings`);
          socket.emit('unsubscription-success', { type: 'rankings' });
        }
      } catch (error) {
        console.error('Error unsubscribing from rankings:', error);
        socket.emit('unsubscription-error', { message: 'Failed to unsubscribe from rankings' });
      }
    });

    // Handle subscribing to user's own tip results (authenticated users only)
    socket.on('subscribe-my-tips', () => {
      try {
        const userId = (socket as any).userId;
        if (!userId) {
          socket.emit('subscription-error', { message: 'Authentication required for personal tip updates' });
          return;
        }

        socket.join(`user-tips:${userId}`);
        console.log(`Client ${socket.id} subscribed to personal tips`);
        socket.emit('subscription-success', { type: 'my-tips' });
      } catch (error) {
        console.error('Error subscribing to personal tips:', error);
        socket.emit('subscription-error', { message: 'Failed to subscribe to personal tips' });
      }
    });

    // Handle disconnection
    socket.on('disconnect', () => {
      console.log(`Client disconnected from tip updates: ${socket.id}`);
      socketSubscriptions.delete(socket.id);
    });
  });

  console.log('Tip socket service initialized');
}

/**
 * Broadcast a new tip to all clients subscribed to the fixture
 */
export function broadcastNewTip(io: SocketIOServer, tip: TipWithDetails): void {
  try {
    io.of('/tips').to(`fixture-tips:${tip.fixtureId}`).emit('new-tip', {
      tip,
      timestamp: new Date()
    });

    console.log(`Broadcasted new tip for fixture ${tip.fixtureId}`);
  } catch (error) {
    console.error('Error broadcasting new tip:', error);
  }
}

/**
 * Broadcast tip result updates when matches finish
 */
export function broadcastTipResults(io: SocketIOServer, fixtureId: number, results: TipResult[]): void {
  try {
    io.of('/tips').to(`fixture-tips:${fixtureId}`).emit('tip-results', {
      fixtureId,
      results,
      timestamp: new Date()
    });

    // Also notify individual users about their tip results
    results.forEach(result => {
      io.of('/tips').to(`user-tips:${result.userId.toString()}`).emit('my-tip-result', {
        result,
        timestamp: new Date()
      });
    });

    console.log(`Broadcasted tip results for fixture ${fixtureId} (${results.length} tips)`);
  } catch (error) {
    console.error('Error broadcasting tip results:', error);
  }
}

/**
 * Broadcast updated tipster rankings
 */
export function broadcastRankingUpdate(io: SocketIOServer, rankings: TipsterRanking[]): void {
  try {
    io.of('/tips').to('tipster-rankings').emit('ranking-update', {
      rankings,
      timestamp: new Date()
    });

    console.log(`Broadcasted ranking update (${rankings.length} tipsters)`);
  } catch (error) {
    console.error('Error broadcasting ranking update:', error);
  }
}

/**
 * Broadcast tip like/unlike updates
 */
export function broadcastTipLikeUpdate(io: SocketIOServer, tipId: string, fixtureId: number, newLikeCount: number): void {
  try {
    io.of('/tips').to(`fixture-tips:${fixtureId}`).emit('tip-like-update', {
      tipId,
      fixtureId,
      likes: newLikeCount,
      timestamp: new Date()
    });

    console.log(`Broadcasted tip like update for tip ${tipId}`);
  } catch (error) {
    console.error('Error broadcasting tip like update:', error);
  }
}

/**
 * Broadcast user's personal tip statistics update
 */
export function broadcastUserStatsUpdate(io: SocketIOServer, userId: string, stats: any): void {
  try {
    io.of('/tips').to(`user-tips:${userId}`).emit('my-stats-update', {
      stats,
      timestamp: new Date()
    });

    console.log(`Broadcasted stats update for user ${userId}`);
  } catch (error) {
    console.error('Error broadcasting user stats update:', error);
  }
}

/**
 * Broadcast tip deletion
 */
export function broadcastTipDeletion(io: SocketIOServer, tipId: string, fixtureId: number): void {
  try {
    io.of('/tips').to(`fixture-tips:${fixtureId}`).emit('tip-deleted', {
      tipId,
      fixtureId,
      timestamp: new Date()
    });

    console.log(`Broadcasted tip deletion for tip ${tipId}`);
  } catch (error) {
    console.error('Error broadcasting tip deletion:', error);
  }
}

/**
 * Get the number of connected clients for a fixture
 */
export function getFixtureTipSubscriberCount(io: SocketIOServer, fixtureId: number): number {
  try {
    const room = io.of('/tips').adapter.rooms.get(`fixture-tips:${fixtureId}`);
    return room ? room.size : 0;
  } catch (error) {
    console.error('Error getting subscriber count:', error);
    return 0;
  }
}

/**
 * Get the number of connected clients for rankings
 */
export function getRankingSubscriberCount(io: SocketIOServer): number {
  try {
    const room = io.of('/tips').adapter.rooms.get('tipster-rankings');
    return room ? room.size : 0;
  } catch (error) {
    console.error('Error getting ranking subscriber count:', error);
    return 0;
  }
}

/**
 * Broadcast general tip statistics (total tips, active tipsters, etc.)
 */
export function broadcastTipStatistics(io: SocketIOServer, stats: {
  totalTips: number;
  activeTipsters: number;
  totalProfit: number;
  averageHitRate: number;
}): void {
  try {
    io.of('/tips').emit('tip-statistics', {
      stats,
      timestamp: new Date()
    });

    console.log('Broadcasted general tip statistics');
  } catch (error) {
    console.error('Error broadcasting tip statistics:', error);
  }
}
