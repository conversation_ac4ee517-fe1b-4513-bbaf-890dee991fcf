/**
 * Team Strength Service
 * 
 * Calculates Dixon-Coles parameters (attack/defense strengths) from historical match data
 * Uses maximum likelihood estimation with time decay weighting
 */

import { DixonColesParams } from './dixonColesService';
import { getEloStrength } from './eloService';
import { EloEnhancedStrength } from '../models/EloRating';
import { LeagueGroupingService } from './leagueGroupingService';
import { SeasonalAdjustmentService } from './seasonalAdjustmentService';

export interface MatchData {
  homeTeamId: number;
  awayTeamId: number;
  homeGoals: number;
  awayGoals: number;
  date: Date;
  leagueId: number;
}

export interface TeamStrength {
  teamId: number;
  attack: number;
  defense: number;
  matchesPlayed: number;
  lastUpdated: Date;
}

export interface LeagueParameters {
  leagueId?: number;
  homeAdvantage: number;
  averageGoalsPerGame: number;
  rho: number;
  matchesAnalyzed: number;
  lastUpdated?: Date;
}

export class TeamStrengthService {
  private static TIME_DECAY_XI = 0.0025; // Time decay parameter (now mutable for optimization)
  private static readonly MIN_MATCHES_FOR_STRENGTH = 5; // Minimum matches to calculate reliable strength
  private static readonly MAX_ITERATIONS = 100; // Maximum iterations for convergence
  private static readonly CONVERGENCE_THRESHOLD = 0.001; // Convergence threshold
  private static USE_GROUP_SPECIFIC_XI = true; // Enable group-specific xi optimization

  /**
   * Get the appropriate xi value for a league (group-specific or default)
   */
  private static getXiForLeague(leagueId: number): number {
    if (this.USE_GROUP_SPECIFIC_XI) {
      return LeagueGroupingService.getOptimizedXi(leagueId);
    }
    return this.TIME_DECAY_XI;
  }

  /**
   * Set the global xi parameter (for testing/optimization)
   */
  public static setTimeDecayXi(xi: number): void {
    this.TIME_DECAY_XI = xi;
  }

  /**
   * Get the current global xi parameter
   */
  public static getTimeDecayXi(): number {
    return this.TIME_DECAY_XI;
  }

  /**
   * Enable or disable group-specific xi optimization
   */
  public static setUseGroupSpecificXi(enabled: boolean): void {
    this.USE_GROUP_SPECIFIC_XI = enabled;
  }

  /**
   * Calculate time decay weight for a match
   */
  private static timeDecayWeight(matchDate: Date, currentDate: Date = new Date(), leagueId?: number): number {
    const daysDiff = (currentDate.getTime() - matchDate.getTime()) / (1000 * 60 * 60 * 24);
    const xi = leagueId ? this.getXiForLeague(leagueId) : this.TIME_DECAY_XI;
    return Math.exp(-xi * daysDiff);
  }

  /**
   * Calculate league-wide statistics
   */
  private static calculateLeagueStats(matches: MatchData[]): {
    averageGoalsPerGame: number;
    homeAdvantage: number;
    totalMatches: number;
  } {
    if (matches.length === 0) {
      return { averageGoalsPerGame: 2.5, homeAdvantage: 1.3, totalMatches: 0 };
    }

    let totalGoals = 0;
    let homeWins = 0;
    let totalMatches = matches.length;

    for (const match of matches) {
      totalGoals += match.homeGoals + match.awayGoals;
      if (match.homeGoals > match.awayGoals) {
        homeWins++;
      }
    }

    const averageGoalsPerGame = totalGoals / totalMatches;
    const homeWinPercentage = homeWins / totalMatches;
    
    // Convert home win percentage to home advantage factor
    // Typical home advantage is around 1.2-1.4
    let homeAdvantage = 1.0 + (homeWinPercentage - 0.33) * 2;

    // Ensure minimum home advantage, especially for limited data
    if (totalMatches < 20) {
      // For limited data, use a reasonable default home advantage
      homeAdvantage = Math.max(homeAdvantage, 1.25);
    }

    return {
      averageGoalsPerGame: Math.max(averageGoalsPerGame, 1.0),
      homeAdvantage: Math.max(Math.min(homeAdvantage, 2.0), 1.2), // Minimum 1.2 instead of 1.0
      totalMatches
    };
  }

  /**
   * Initialize team strengths with league averages
   */
  private static initializeTeamStrengths(
    teamIds: number[],
    leagueAverage: number
  ): Map<number, { attack: number; defense: number }> {
    const strengths = new Map<number, { attack: number; defense: number }>();
    
    // Initialize all teams with league average (log scale)
    const logAverage = Math.log(leagueAverage / 2); // Divide by 2 as average per team
    
    for (const teamId of teamIds) {
      strengths.set(teamId, {
        attack: logAverage,
        defense: logAverage
      });
    }

    return strengths;
  }

  /**
   * Apply attack strength constraint to prevent overparameterization
   * Ensures average attack strength = 0 in log space (equivalent to average = 1 in normal space)
   */
  private static applyAttackStrengthConstraint(
    strengths: Map<number, { attack: number; defense: number }>
  ): void {
    if (strengths.size === 0) return;

    // Calculate average attack strength
    const totalAttack = Array.from(strengths.values())
      .reduce((sum, strength) => sum + strength.attack, 0);
    const avgAttack = totalAttack / strengths.size;

    // Adjust all attack strengths so average = 0 (in log space)
    for (const [teamId, strength] of strengths) {
      strength.attack -= avgAttack;
    }

    console.log(`Applied attack strength constraint - Average adjustment: ${avgAttack.toFixed(4)}`);
  }

  /**
   * Calculate team strengths using iterative maximum likelihood estimation with MLE-based home advantage
   */
  private static calculateTeamStrengths(
    matches: MatchData[],
    leagueStats: { averageGoalsPerGame: number; homeAdvantage: number }
  ): {
    teamStrengths: Map<number, { attack: number; defense: number }>;
    optimizedHomeAdvantage: number;
  } {
    if (matches.length === 0) {
      return {
        teamStrengths: new Map(),
        optimizedHomeAdvantage: leagueStats.homeAdvantage
      };
    }

    // Get unique team IDs
    const teamIds = Array.from(new Set([
      ...matches.map(m => m.homeTeamId),
      ...matches.map(m => m.awayTeamId)
    ]));

    // Initialize team strengths
    let strengths = this.initializeTeamStrengths(teamIds, leagueStats.averageGoalsPerGame);

    // Initialize home advantage with the provided value (will be optimized via MLE)
    let homeAdvantage = Math.log(leagueStats.homeAdvantage);

    console.log(`Starting MLE with initial home advantage: ${Math.exp(homeAdvantage).toFixed(3)}`);

    // Iterative estimation with joint optimization of team strengths and home advantage
    for (let iteration = 0; iteration < this.MAX_ITERATIONS; iteration++) {
      const newStrengths = new Map(strengths);
      let maxChange = 0;

      // Update each team's attack and defense
      for (const teamId of teamIds) {
        const teamMatches = matches.filter(m =>
          m.homeTeamId === teamId || m.awayTeamId === teamId
        );

        if (teamMatches.length < this.MIN_MATCHES_FOR_STRENGTH) {
          continue;
        }

        // Calculate weighted attack strength
        let attackNumerator = 0;
        let attackDenominator = 0;

        // Calculate weighted defense strength
        let defenseNumerator = 0;
        let defenseDenominator = 0;

        for (const match of teamMatches) {
          const weight = this.timeDecayWeight(match.date, new Date(), match.leagueId);
          const homeStrength = strengths.get(match.homeTeamId);
          const awayStrength = strengths.get(match.awayTeamId);

          if (!homeStrength || !awayStrength) continue;

          if (match.homeTeamId === teamId) {
            // Team playing at home
            attackNumerator += weight * match.homeGoals;
            attackDenominator += weight * Math.exp(awayStrength.defense + homeAdvantage);

            defenseNumerator += weight * match.awayGoals;
            defenseDenominator += weight * Math.exp(awayStrength.attack);
          } else {
            // Team playing away
            attackNumerator += weight * match.awayGoals;
            attackDenominator += weight * Math.exp(homeStrength.defense);

            defenseNumerator += weight * match.homeGoals;
            defenseDenominator += weight * Math.exp(homeStrength.attack + homeAdvantage);
          }
        }

        // Update strengths (in log space)
        if (attackDenominator > 0) {
          const newAttack = Math.log(attackNumerator / attackDenominator);
          const change = Math.abs(newAttack - strengths.get(teamId)!.attack);
          maxChange = Math.max(maxChange, change);
          newStrengths.get(teamId)!.attack = newAttack;
        }

        if (defenseDenominator > 0) {
          const newDefense = Math.log(defenseNumerator / defenseDenominator);
          const change = Math.abs(newDefense - strengths.get(teamId)!.defense);
          maxChange = Math.max(maxChange, change);
          newStrengths.get(teamId)!.defense = newDefense;
        }
      }

      // Apply attack strength constraint after each iteration
      this.applyAttackStrengthConstraint(newStrengths);

      // Update home advantage using MLE
      const newHomeAdvantage = this.estimateHomeAdvantageMLE(matches, newStrengths);
      const homeAdvantageChange = Math.abs(newHomeAdvantage - homeAdvantage);
      maxChange = Math.max(maxChange, homeAdvantageChange);

      strengths = newStrengths;
      homeAdvantage = newHomeAdvantage;

      // Log progress every 10 iterations
      if (iteration % 10 === 0 || iteration < 5) {
        console.log(`Iteration ${iteration + 1}: Home advantage = ${Math.exp(homeAdvantage).toFixed(4)}, Max change = ${maxChange.toFixed(6)}`);
      }

      // Check for convergence
      if (maxChange < this.CONVERGENCE_THRESHOLD) {
        console.log(`MLE converged after ${iteration + 1} iterations`);
        console.log(`Final home advantage: ${Math.exp(homeAdvantage).toFixed(4)} (was ${Math.exp(Math.log(leagueStats.homeAdvantage)).toFixed(4)})`);
        break;
      }
    }

    return {
      teamStrengths: strengths,
      optimizedHomeAdvantage: Math.exp(homeAdvantage)
    };
  }

  /**
   * Estimate home advantage using maximum likelihood estimation
   * This calculates the optimal home advantage parameter that maximizes the likelihood
   * of observing the actual match results given the current team strengths
   */
  private static estimateHomeAdvantageMLE(
    matches: MatchData[],
    teamStrengths: Map<number, { attack: number; defense: number }>
  ): number {
    if (matches.length < 10) {
      console.log('Insufficient data for home advantage MLE, using default value');
      return Math.log(1.3); // Default 30% home advantage
    }

    let homeGoalsNumerator = 0;
    let homeGoalsDenominator = 0;
    let totalWeight = 0;

    // Calculate weighted sums for MLE estimation
    for (const match of matches) {
      const homeStrength = teamStrengths.get(match.homeTeamId);
      const awayStrength = teamStrengths.get(match.awayTeamId);

      if (!homeStrength || !awayStrength) continue;

      const weight = this.timeDecayWeight(match.date, new Date(), match.leagueId);

      // For home advantage estimation, we look at the ratio of home goals
      // to what would be expected without home advantage
      const expectedHomeGoalsWithoutHA = Math.exp(homeStrength.attack + awayStrength.defense);

      // Only include matches with reasonable expected goals to avoid extreme values
      if (expectedHomeGoalsWithoutHA > 0.1 && expectedHomeGoalsWithoutHA < 8.0) {
        homeGoalsNumerator += weight * match.homeGoals;
        homeGoalsDenominator += weight * expectedHomeGoalsWithoutHA;
        totalWeight += weight;
      }
    }

    // Calculate MLE estimate of home advantage
    let homeAdvantage: number;

    if (homeGoalsDenominator > 0 && totalWeight > 0) {
      // MLE estimate: log(observed_home_goals / expected_home_goals_without_HA)
      const rawEstimate = homeGoalsNumerator / homeGoalsDenominator;

      // Apply smoothing to prevent extreme estimates
      // Use a weighted average with a reasonable prior (1.3)
      const priorWeight = Math.min(totalWeight * 0.1, 10); // Prior gets less weight as we have more data
      const smoothedEstimate = (rawEstimate * totalWeight + 1.3 * priorWeight) / (totalWeight + priorWeight);

      homeAdvantage = Math.log(smoothedEstimate);

      // Apply reasonable bounds to prevent extreme values
      const minHomeAdvantage = Math.log(1.05); // Minimum 5% advantage
      const maxHomeAdvantage = Math.log(1.8);  // Maximum 80% advantage (more conservative)

      homeAdvantage = Math.max(minHomeAdvantage, Math.min(maxHomeAdvantage, homeAdvantage));

    } else {
      // Fallback: use a simple goal ratio approach
      const totalHomeGoals = matches.reduce((sum, m) => sum + m.homeGoals, 0);
      const totalAwayGoals = matches.reduce((sum, m) => sum + m.awayGoals, 0);

      if (totalAwayGoals > 0) {
        const goalRatio = totalHomeGoals / totalAwayGoals;
        // Smooth the goal ratio towards 1.3
        const smoothedRatio = (goalRatio + 1.3) / 2;
        homeAdvantage = Math.log(Math.max(1.05, Math.min(1.8, smoothedRatio)));
      } else {
        homeAdvantage = Math.log(1.3);
      }

      console.log(`Using fallback home advantage estimation: ${Math.exp(homeAdvantage).toFixed(4)}`);
    }

    return homeAdvantage;
  }

  /**
   * Estimate rho parameter using continuous maximum likelihood estimation
   */
  private static estimateRho(
    matches: MatchData[],
    teamStrengths: Map<number, { attack: number; defense: number }>,
    homeAdvantage: number
  ): number {
    if (matches.length < 20) {
      console.log('Insufficient data for rho estimation, using default value');
      return -0.13;
    }

    // Count actual frequencies of low-scoring games for reporting
    const actualCounts = { '0-0': 0, '1-0': 0, '0-1': 0, '1-1': 0, total: 0 };

    for (const match of matches) {
      const key = `${match.homeGoals}-${match.awayGoals}`;
      if (key in actualCounts) {
        actualCounts[key as keyof typeof actualCounts]++;
      }
      actualCounts.total++;
    }

    // Use continuous optimization to find optimal rho
    const result = this.optimizeRhoContinuous(matches, teamStrengths, homeAdvantage);

    console.log(`Estimated rho parameter: ${result.optimalRho.toFixed(4)} (log-likelihood: ${result.logLikelihood.toFixed(2)})`);
    console.log(`Optimization: ${result.iterations} iterations, improvement: ${result.improvement.toFixed(4)}`);
    console.log(`Low-scoring game frequencies: 0-0: ${actualCounts['0-0']}, 1-0: ${actualCounts['1-0']}, 0-1: ${actualCounts['0-1']}, 1-1: ${actualCounts['1-1']}`);

    return result.optimalRho;
  }

  /**
   * Continuous optimization of rho parameter using golden section search
   */
  private static optimizeRhoContinuous(
    matches: MatchData[],
    teamStrengths: Map<number, { attack: number; defense: number }>,
    homeAdvantage: number
  ): {
    optimalRho: number;
    logLikelihood: number;
    iterations: number;
    improvement: number;
  } {
    // Golden section search parameters
    const PHI = (1 + Math.sqrt(5)) / 2; // Golden ratio
    const RESPHI = 2 - PHI; // 1/PHI
    const TOLERANCE = 1e-5;
    const MAX_ITERATIONS = 100;

    // Search bounds for rho (based on literature)
    let a = -0.3;  // Lower bound
    let b = 0.1;   // Upper bound

    // Initial points
    let c = a + RESPHI * (b - a);
    let d = a + (1 - RESPHI) * (b - a);

    // Calculate initial log-likelihoods
    let fc = this.calculateLogLikelihoodForRho(matches, teamStrengths, homeAdvantage, c);
    let fd = this.calculateLogLikelihoodForRho(matches, teamStrengths, homeAdvantage, d);

    // Baseline log-likelihood with default rho
    const baselineLogLikelihood = this.calculateLogLikelihoodForRho(matches, teamStrengths, homeAdvantage, -0.13);

    let iterations = 0;

    // Golden section search loop
    while (Math.abs(b - a) > TOLERANCE && iterations < MAX_ITERATIONS) {
      iterations++;

      if (fc > fd) {
        // Maximum is in [a, d]
        b = d;
        d = c;
        fd = fc;
        c = a + RESPHI * (b - a);
        fc = this.calculateLogLikelihoodForRho(matches, teamStrengths, homeAdvantage, c);
      } else {
        // Maximum is in [c, b]
        a = c;
        c = d;
        fc = fd;
        d = a + (1 - RESPHI) * (b - a);
        fd = this.calculateLogLikelihoodForRho(matches, teamStrengths, homeAdvantage, d);
      }
    }

    // Return the point with higher log-likelihood
    const optimalRho = fc > fd ? c : d;
    const optimalLogLikelihood = Math.max(fc, fd);
    const improvement = optimalLogLikelihood - baselineLogLikelihood;

    return {
      optimalRho,
      logLikelihood: optimalLogLikelihood,
      iterations,
      improvement
    };
  }

  /**
   * Calculate log-likelihood for a specific rho value
   */
  private static calculateLogLikelihoodForRho(
    matches: MatchData[],
    teamStrengths: Map<number, { attack: number; defense: number }>,
    homeAdvantage: number,
    rho: number
  ): number {
    let logLikelihood = 0;
    let validMatches = 0;

    for (const match of matches) {
      const homeStrength = teamStrengths.get(match.homeTeamId);
      const awayStrength = teamStrengths.get(match.awayTeamId);

      if (!homeStrength || !awayStrength) continue;

      // Calculate expected goals
      const lambda = Math.exp(homeStrength.attack + awayStrength.defense + homeAdvantage);
      const mu = Math.exp(awayStrength.attack + homeStrength.defense);

      // Validate expected goals are reasonable
      if (lambda <= 0 || mu <= 0 || lambda > 10 || mu > 10) continue;

      // Calculate probability for this match result
      const poissonProb = this.poissonProbability(match.homeGoals, lambda) *
                         this.poissonProbability(match.awayGoals, mu);

      const rhoCorr = this.calculateRhoCorrection(match.homeGoals, match.awayGoals, lambda, mu, rho);
      const probability = poissonProb * rhoCorr;

      // Only include valid probabilities
      if (probability > 0 && isFinite(probability)) {
        logLikelihood += Math.log(probability);
        validMatches++;
      }
    }

    // Return average log-likelihood to normalize for different dataset sizes
    return validMatches > 0 ? logLikelihood / validMatches : -Infinity;
  }

  /**
   * Calculate Poisson probability for rho estimation
   */
  private static poissonProbability(k: number, lambda: number): number {
    if (lambda <= 0) return k === 0 ? 1 : 0;

    // Use log space to avoid overflow
    const logProb = k * Math.log(lambda) - lambda - this.logFactorial(k);
    return Math.exp(logProb);
  }

  /**
   * Calculate log factorial for Poisson probability
   */
  private static logFactorial(n: number): number {
    if (n <= 1) return 0;
    let result = 0;
    for (let i = 2; i <= n; i++) {
      result += Math.log(i);
    }
    return result;
  }

  /**
   * Calculate rho correction for Dixon-Coles model
   */
  private static calculateRhoCorrection(
    homeGoals: number,
    awayGoals: number,
    lambda: number,
    mu: number,
    rho: number
  ): number {
    if (homeGoals === 0 && awayGoals === 0) {
      return 1 - lambda * mu * rho;
    }
    if (homeGoals === 0 && awayGoals === 1) {
      return 1 + lambda * rho;
    }
    if (homeGoals === 1 && awayGoals === 0) {
      return 1 + mu * rho;
    }
    if (homeGoals === 1 && awayGoals === 1) {
      return 1 - rho;
    }
    return 1; // No correction for other scores
  }

  /**
   * Calculate team strengths and league parameters from match data
   * This is the main public interface for the improved Dixon-Coles calculation
   */
  public static async calculateFromMatches(
    matches: MatchData[],
    fixtureDate?: Date
  ): Promise<{
    teamStrengths: Map<number, TeamStrength>;
    leagueParams: LeagueParameters;
  }> {
    if (matches.length === 0) {
      throw new Error('No matches provided for calculation');
    }

    // Calculate league statistics
    const leagueStats = this.calculateLeagueStats(matches);

    // Apply seasonal adjustments if fixture date is provided
    let adjustedLeagueStats = leagueStats;
    let adjustedRho = -0.13; // Default rho

    if (fixtureDate && matches.length > 0) {
      const leagueId = matches[0].leagueId;
      const seasonalContext = SeasonalAdjustmentService.getSeasonalContext(fixtureDate, leagueId);

      if (SeasonalAdjustmentService.shouldApplySeasonalAdjustments(leagueId)) {
        adjustedLeagueStats = {
          ...leagueStats,
          homeAdvantage: SeasonalAdjustmentService.adjustHomeAdvantage(leagueStats.homeAdvantage, seasonalContext)
        };

        console.log(`🗓️ Applying seasonal adjustments for ${seasonalContext.period} season:`);
        console.log(`   Home advantage: ${leagueStats.homeAdvantage.toFixed(3)} → ${adjustedLeagueStats.homeAdvantage.toFixed(3)}`);
      }
    }

    // Calculate team strengths with MLE-optimized home advantage
    const strengthsResult = this.calculateTeamStrengths(matches, adjustedLeagueStats);
    const strengthsMap = strengthsResult.teamStrengths;
    const optimizedHomeAdvantage = strengthsResult.optimizedHomeAdvantage;

    // Use the MLE-optimized home advantage for rho estimation
    const baseRho = this.estimateRho(matches, strengthsMap, Math.log(optimizedHomeAdvantage));

    // Apply seasonal adjustment to rho if applicable
    if (fixtureDate && matches.length > 0) {
      const leagueId = matches[0].leagueId;
      const seasonalContext = SeasonalAdjustmentService.getSeasonalContext(fixtureDate, leagueId);

      if (SeasonalAdjustmentService.shouldApplySeasonalAdjustments(leagueId)) {
        adjustedRho = SeasonalAdjustmentService.adjustRhoParameter(baseRho, seasonalContext);
        console.log(`   Rho parameter: ${baseRho.toFixed(3)} → ${adjustedRho.toFixed(3)}`);
      } else {
        adjustedRho = baseRho;
      }
    } else {
      adjustedRho = baseRho;
    }

    // Convert to TeamStrength format
    const teamStrengths = new Map<number, TeamStrength>();
    for (const [teamId, strength] of strengthsMap) {
      const teamMatches = matches.filter(m =>
        m.homeTeamId === teamId || m.awayTeamId === teamId
      );

      teamStrengths.set(teamId, {
        teamId,
        attack: strength.attack,
        defense: strength.defense,
        matchesPlayed: teamMatches.length,
        lastUpdated: new Date()
      });
    }

    // Create league parameters using MLE-optimized home advantage
    const leagueParams: LeagueParameters = {
      homeAdvantage: optimizedHomeAdvantage, // Use MLE-optimized value
      averageGoalsPerGame: adjustedLeagueStats.averageGoalsPerGame,
      rho: adjustedRho,
      matchesAnalyzed: matches.length
    };

    console.log(`🏠 Home advantage optimization: ${adjustedLeagueStats.homeAdvantage.toFixed(4)} → ${optimizedHomeAdvantage.toFixed(4)} (${((optimizedHomeAdvantage/adjustedLeagueStats.homeAdvantage - 1) * 100).toFixed(1)}% change)`);

    return { teamStrengths, leagueParams };
  }

  /**
   * Calculate Dixon-Coles parameters for a specific match
   */
  public static calculateMatchParams(
    homeTeamId: number,
    awayTeamId: number,
    leagueId: number,
    teamStrengths: Map<number, TeamStrength>,
    leagueParams: LeagueParameters
  ): DixonColesParams | null {
    const homeStrength = teamStrengths.get(homeTeamId);
    const awayStrength = teamStrengths.get(awayTeamId);

    if (!homeStrength || !awayStrength) {
      return null;
    }

    return {
      homeAttack: homeStrength.attack,
      homeDefense: homeStrength.defense,
      awayAttack: awayStrength.attack,
      awayDefense: awayStrength.defense,
      homeAdvantage: leagueParams.homeAdvantage,
      rho: leagueParams.rho
    };
  }

  /**
   * Process historical matches to calculate team strengths and league parameters
   */
  public static processHistoricalData(
    matches: MatchData[],
    leagueId: number
  ): {
    teamStrengths: Map<number, TeamStrength>;
    leagueParams: LeagueParameters;
  } {
    if (matches.length === 0) {
      return {
        teamStrengths: new Map(),
        leagueParams: {
          leagueId,
          homeAdvantage: 1.3,
          averageGoalsPerGame: 2.5,
          rho: -0.13,
          matchesAnalyzed: 0,
          lastUpdated: new Date()
        }
      };
    }

    // Calculate league statistics
    const leagueStats = this.calculateLeagueStats(matches);

    // Calculate team strengths with MLE-optimized home advantage
    const strengthsResult = this.calculateTeamStrengths(matches, leagueStats);
    const strengthsMap = strengthsResult.teamStrengths;
    const optimizedHomeAdvantage = strengthsResult.optimizedHomeAdvantage;

    // Use the MLE-optimized home advantage for rho estimation
    const rho = this.estimateRho(matches, strengthsMap, Math.log(optimizedHomeAdvantage));

    // Convert to TeamStrength format
    const teamStrengths = new Map<number, TeamStrength>();
    for (const [teamId, strength] of strengthsMap) {
      const teamMatches = matches.filter(m => 
        m.homeTeamId === teamId || m.awayTeamId === teamId
      );

      teamStrengths.set(teamId, {
        teamId,
        attack: strength.attack,
        defense: strength.defense,
        matchesPlayed: teamMatches.length,
        lastUpdated: new Date()
      });
    }

    const leagueParams: LeagueParameters = {
      leagueId,
      homeAdvantage: optimizedHomeAdvantage, // Use MLE-optimized value
      averageGoalsPerGame: leagueStats.averageGoalsPerGame,
      rho,
      matchesAnalyzed: matches.length,
      lastUpdated: new Date()
    };

    console.log(`🏠 Home advantage optimization for league ${leagueId}: ${leagueStats.homeAdvantage.toFixed(4)} → ${optimizedHomeAdvantage.toFixed(4)} (${((optimizedHomeAdvantage/leagueStats.homeAdvantage - 1) * 100).toFixed(1)}% change)`);

    return { teamStrengths, leagueParams };
  }

  /**
   * Get team strength with fallback to league average
   */
  public static getTeamStrengthWithFallback(
    teamId: number,
    teamStrengths: Map<number, TeamStrength>,
    leagueParams: LeagueParameters
  ): { attack: number; defense: number } {
    const strength = teamStrengths.get(teamId);

    if (strength && strength.matchesPlayed >= this.MIN_MATCHES_FOR_STRENGTH) {
      return { attack: strength.attack, defense: strength.defense };
    }

    // Fallback to league average
    const logAverage = Math.log(leagueParams.averageGoalsPerGame / 2);
    return { attack: logAverage, defense: logAverage };
  }

  /**
   * Get ELO-enhanced team strength with intelligent fallback
   * Uses ELO data when historical data is limited
   */
  public static async getEloEnhancedTeamStrength(
    teamId: number,
    teamStrengths: Map<number, TeamStrength>,
    leagueParams: LeagueParameters
  ): Promise<{ attack: number; defense: number; eloEnhanced: boolean }> {
    const strength = teamStrengths.get(teamId);

    // If we have sufficient historical data, use it primarily
    if (strength && strength.matchesPlayed >= this.MIN_MATCHES_FOR_STRENGTH) {
      return {
        attack: strength.attack,
        defense: strength.defense,
        eloEnhanced: false
      };
    }

    // Try to get ELO data for better fallback
    try {
      const eloStrength = await getEloStrength(teamId);

      if (eloStrength) {
        // Convert ELO rating to attack/defense strengths
        const logAverage = Math.log(leagueParams.averageGoalsPerGame / 2);
        const eloAdjustment = (eloStrength.relativeStrength - 1.0) * 0.5; // Scale ELO influence

        return {
          attack: logAverage + eloAdjustment,
          defense: logAverage - eloAdjustment * 0.7, // Defense is slightly less influenced
          eloEnhanced: true
        };
      }
    } catch (error) {
      console.warn(`Could not fetch ELO strength for team ${teamId}:`, error);
    }

    // Final fallback to league average
    const logAverage = Math.log(leagueParams.averageGoalsPerGame / 2);
    return {
      attack: logAverage,
      defense: logAverage,
      eloEnhanced: false
    };
  }
}
