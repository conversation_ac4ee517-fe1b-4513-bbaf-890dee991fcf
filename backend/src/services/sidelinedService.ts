import { fetchData } from './apiFootball';
import { SidelinedPlayer, SidelinedTeam } from '../models/Sidelined';

// Define the sidelined response structure from the API
// For single player requests
export interface SidelinedResponse {
    player?: SidelinedPlayer;
    team?: SidelinedTeam;
    type?: string;
    reason?: string | null;
    start?: string;
    end?: string | null;
    // For batch player requests
    id?: number;
    sidelined?: Array<{
        type: string;
        start: string;
        end: string | null;
    }>;
}

// Define allowed query parameters based on documentation
interface FetchSidelinedParams {
    player?: number; // Player ID
    coach?: number;  // Coach ID
    team?: number;   // Team ID
    season?: number; // Season year (required with league)
    league?: number; // League ID (requires season)
    players?: string; // Hyphen-separated player IDs (max 20)
}

export async function fetchSidelined(params: FetchSidelinedParams): Promise<SidelinedResponse[]> {
    // Validate parameters
    if (!params.player && !params.coach && !params.team && !params.league && !params.players) {
        throw new Error("At least one parameter (player, coach, team, league, or players) is required.");
    }

    // Season is required if league is provided
    if (params.league && !params.season) {
        throw new Error("Parameter 'season' is required when using 'league'.");
    }

    return fetchData<SidelinedResponse[]>('/sidelined', params);
}
