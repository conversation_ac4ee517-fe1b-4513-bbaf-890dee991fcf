/**
 * Team Rating Service
 * 
 * Calculates team ratings based on individual player ratings from fixture data.
 * Includes both starting XI and substitute players who have valid ratings.
 */

import { getRedisClient } from '../config/redis';

// Player rating information
export interface PlayerRatingInfo {
  playerId: number;
  name: string;
  rating: number;
  substitute: boolean;
  minutes: number | null;
  position: string;
}

// Team rating data with detailed information
export interface TeamRatingData {
  rating: number;
  playersCount: number;
  ratedPlayersCount: number;
  playerRatings: PlayerRatingInfo[];
}

// Complete team ratings for both teams
export interface TeamRatings {
  home: TeamRatingData;
  away: TeamRatingData;
}

// Player data structure from API-Football
interface PlayerStatistics {
  games: {
    minutes: number | null;
    number: number;
    position: string;
    rating: string | null;
    captain: boolean;
    substitute: boolean;
  };
}

interface Player {
  player: {
    id: number;
    name: string;
    photo: string;
  };
  statistics: PlayerStatistics[];
}

interface TeamPlayers {
  team: {
    id: number;
    name: string;
    logo: string;
  };
  players: Player[];
}

export class TeamRatingService {
  private static readonly CACHE_PREFIX = 'team_rating:';
  private static readonly LIVE_MATCH_TTL = 120; // 2 minutes for live matches
  private static readonly FINISHED_MATCH_TTL = 86400; // 24 hours for finished matches

  /**
   * Calculate team rating from player data
   */
  private static calculateTeamRating(teamPlayers: TeamPlayers): TeamRatingData {
    const playerRatings: PlayerRatingInfo[] = [];
    let totalRating = 0;
    let ratedPlayersCount = 0;

    // Process all players (starting XI + substitutes)
    for (const player of teamPlayers.players) {
      const stats = player.statistics[0]; // First statistics entry
      
      if (!stats || !stats.games) continue;

      const rating = stats.games.rating;
      
      // Only include players with valid ratings
      if (rating !== null && rating !== undefined && rating !== '') {
        const numericRating = parseFloat(rating);
        
        // Validate the numeric rating
        if (!isNaN(numericRating) && numericRating > 0 && numericRating <= 10) {
          playerRatings.push({
            playerId: player.player.id,
            name: player.player.name,
            rating: numericRating,
            substitute: stats.games.substitute,
            minutes: stats.games.minutes,
            position: stats.games.position
          });
          
          totalRating += numericRating;
          ratedPlayersCount++;
        }
      }
    }

    // Calculate average rating
    const averageRating = ratedPlayersCount > 0 ? totalRating / ratedPlayersCount : 0;

    return {
      rating: Math.round(averageRating * 100) / 100, // Round to 2 decimal places
      playersCount: teamPlayers.players.length,
      ratedPlayersCount,
      playerRatings: playerRatings.sort((a, b) => {
        // Sort by substitute status (starting XI first), then by rating (highest first)
        if (a.substitute !== b.substitute) {
          return a.substitute ? 1 : -1;
        }
        return b.rating - a.rating;
      })
    };
  }

  /**
   * Calculate team ratings for both teams from fixture player data
   */
  public static calculateTeamRatings(playersData: TeamPlayers[]): TeamRatings {
    if (!playersData || playersData.length < 2) {
      throw new Error('Invalid players data: Expected data for both teams');
    }

    const homeTeam = playersData[0];
    const awayTeam = playersData[1];

    return {
      home: this.calculateTeamRating(homeTeam),
      away: this.calculateTeamRating(awayTeam)
    };
  }

  /**
   * Get cached team ratings or calculate if not cached
   */
  public static async getTeamRatings(
    fixtureId: number, 
    playersData: TeamPlayers[], 
    isLive: boolean = false
  ): Promise<TeamRatings> {
    const cacheKey = `${this.CACHE_PREFIX}${fixtureId}`;

    const redisClient = getRedisClient();

    try {
      // Try to get from cache first
      const cachedData = await redisClient.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
    } catch (error) {
      console.warn(`Failed to get team ratings from cache for fixture ${fixtureId}:`, error);
    }

    // Calculate team ratings
    const teamRatings = this.calculateTeamRatings(playersData);

    // Cache the results
    try {
      const ttl = isLive ? this.LIVE_MATCH_TTL : this.FINISHED_MATCH_TTL;
      await redisClient.setex(cacheKey, ttl, JSON.stringify(teamRatings));
    } catch (error) {
      console.warn(`Failed to cache team ratings for fixture ${fixtureId}:`, error);
    }

    return teamRatings;
  }

  /**
   * Invalidate cached team ratings for a fixture
   */
  public static async invalidateCache(fixtureId: number): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${fixtureId}`;
    const redisClient = getRedisClient();

    try {
      await redisClient.del(cacheKey);
    } catch (error) {
      console.warn(`Failed to invalidate team ratings cache for fixture ${fixtureId}:`, error);
    }
  }

  /**
   * Get team rating summary (just the numbers, no detailed player info)
   */
  public static getTeamRatingSummary(teamRatings: TeamRatings): {
    home: { rating: number; ratedPlayersCount: number };
    away: { rating: number; ratedPlayersCount: number };
  } {
    return {
      home: {
        rating: teamRatings.home.rating,
        ratedPlayersCount: teamRatings.home.ratedPlayersCount
      },
      away: {
        rating: teamRatings.away.rating,
        ratedPlayersCount: teamRatings.away.ratedPlayersCount
      }
    };
  }

  /**
   * Validate if team ratings can be calculated from the provided data
   */
  public static canCalculateRatings(playersData: TeamPlayers[]): boolean {
    if (!playersData || playersData.length < 2) {
      return false;
    }

    // Check if at least one team has players with ratings
    return playersData.some(teamData => 
      teamData.players.some(player => 
        player.statistics?.[0]?.games?.rating !== null &&
        player.statistics?.[0]?.games?.rating !== undefined &&
        player.statistics?.[0]?.games?.rating !== ''
      )
    );
  }
}
