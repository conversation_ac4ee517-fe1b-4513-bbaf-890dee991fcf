import { getUsersCollection } from '../models/User';
import { getAnonymousDeviceTokensCollection } from '../models/AnonymousDeviceToken';
import { getFixturesCollection, EventInfo } from '../models/Fixture';
import { sendToAuthenticatedUsers, sendToAnonymousUsers } from './notificationHelpers';
import { ObjectId } from 'mongodb';
import apn from 'node-apn';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config();

// --- APNs Provider Configuration ---
let apnProvider: apn.Provider | null = null;

// Initialize the APNs provider
function initializeAPNsProvider(): apn.Provider | null {
  if (apnProvider) return apnProvider;

  const keyPath = process.env.APNS_KEY_PATH || path.join(__dirname, '../../certs/AuthKey_F8D572C22G.p8');
  console.log(`Initializing APNs provider with key at: ${keyPath}`);

  try {
    // Check if the key file exists
    const fs = require('fs');
    if (!fs.existsSync(keyPath)) {
      console.error(`APNs key file not found at: ${keyPath}`);
      return null;
    }

    const options: apn.ProviderOptions = {
      token: {
        key: keyPath,
        keyId: process.env.APNS_KEY_ID || 'F8D572C22G',
        teamId: process.env.APNS_TEAM_ID || '5NEAGT64RQ',
      },
      production: false // Always use development APNs server for now
    };

    apnProvider = new apn.Provider(options);
    console.log('APNs provider initialized successfully');
    return apnProvider;
  } catch (error) {
    console.error('Failed to initialize APNs provider:', error);
    return null;
  }
}

// Send push notification via APNs
export async function sendPushNotification(token: string, title: string, body: string, data?: any, userId?: string): Promise<boolean> {
  // If we're in development mode without proper certificates, just log
  if (process.env.MOCK_NOTIFICATIONS === 'true') {
    console.log(`[PUSH SENT - Mock Mode] To: ${token}, Title: "${title}", Body: "${body}"${data ? `, Data: ${JSON.stringify(data)}` : ''}`);
    return true;
  }

  try {
    const provider = initializeAPNsProvider();
    if (!provider) {
      console.error(`[PUSH FAILED - No Provider] To: ${token}, Title: "${title}", Body: "${body}"`);
      console.error(`Check that the APNs certificate file exists at ${process.env.APNS_KEY_PATH || './certs/AuthKey_F8D572C22G.p8'}`);
      return false;
    }

    // Create notification
    const notification = new apn.Notification();
    notification.expiry = Math.floor(Date.now() / 1000) + 3600; // Expires in 1 hour
    notification.badge = 1;
    notification.sound = 'ping.aiff';
    notification.alert = {
      title: title,
      body: body
    };

    // Make sure the bundle ID is set correctly
    const bundleId = process.env.APNS_BUNDLE_ID || 'com.kickoffscore.kickoffscore';
    notification.topic = bundleId;
    console.log(`Using bundle ID: ${bundleId} for notification`);

    // Add custom data if provided
    if (data) {
      notification.payload = { ...data };
      console.log(`Adding custom data to notification: ${JSON.stringify(data)}`);
    }

    // Send notification
    console.log(`Sending notification to token: ${token}`);
    const result = await provider.send(notification, token);

    // Check for failures
    if (result.failed.length > 0) {
      const failure = result.failed[0];
      console.error(`[PUSH FAILED] To: ${token}, Reason: ${failure.response?.reason}`);

      // Handle invalid tokens
      if (failure.response?.reason === 'BadDeviceToken' ||
          failure.response?.reason === 'Unregistered' ||
          failure.response?.reason === 'DeviceTokenNotForTopic') {
        if (userId) {
          console.log(`Removing invalid token ${token} for user ${userId}`);
          const usersCollection = getUsersCollection();
          await usersCollection.updateOne(
            { _id: new ObjectId(userId) },
            { $pull: { deviceTokens: token } }
          );
        } else {
          // Handle anonymous token removal
          console.log(`Removing invalid anonymous token ${token}`);
          const anonymousTokensCollection = getAnonymousDeviceTokensCollection();
          await anonymousTokensCollection.deleteOne({ token });
        }
      }
      return false;
    }

    console.log(`[PUSH SENT] To: ${token}, Title: "${title}", Body: "${body}"`);
    return true;
  } catch (error) {
    console.error(`[PUSH ERROR] To: ${token}:`, error);
    return false;
  }
}

// --- Notification Types ---

// Send goal notification
export async function sendGoalNotification(fixtureId: number, event: EventInfo): Promise<void> {
  try {
    const fixturesCollection = getFixturesCollection();
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });

    if (!fixture) {
      console.error(`Cannot send goal notification: Fixture ${fixtureId} not found`);
      return;
    }

    const homeTeam = fixture.teams.home.name;
    const awayTeam = fixture.teams.away.name;
    const scorerName = event.player.name || 'Unknown player';
    const minute = `${event.time.elapsed}'${event.time.extra ? `+${event.time.extra}` : ''}`;
    const isHomeTeamGoal = event.team.id === fixture.teams.home.id;
    const homeScore = fixture.goals.home;
    const awayScore = fixture.goals.away;

    // Format: 3 lines with title and body separated
    const title = `⚽ GOAL!`;
    const body = `${scorerName} (${minute})\n${homeTeam} ${isHomeTeamGoal ? `[${homeScore}]` : homeScore} - ${isHomeTeamGoal ? awayScore : `[${awayScore}]`} ${awayTeam}`;

    // Prepare notification data
    const notificationData = {
      type: 'goal',
      fixtureId,
      eventTime: event.time,
      score: {
        home: homeScore,
        away: awayScore
      }
    };

    // Send to authenticated users
    await sendToAuthenticatedUsers(fixtureId, title, body, notificationData, 'goals');

    // Send to anonymous users
    await sendToAnonymousUsers(fixtureId, title, body, notificationData, 'goals');
  } catch (error) {
    console.error('Error sending goal notification:', error);
  }
}

// Send goal disallowed notification
export async function sendGoalDisallowedNotification(fixtureId: number, event: EventInfo): Promise<void> {
  try {
    const fixturesCollection = getFixturesCollection();
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });

    if (!fixture) {
      console.error(`Cannot send goal disallowed notification: Fixture ${fixtureId} not found`);
      return;
    }

    const homeTeam = fixture.teams.home.name;
    const awayTeam = fixture.teams.away.name;
    const playerName = event.player.name || 'Unknown player';
    const minute = `${event.time.elapsed}'${event.time.extra ? `+${event.time.extra}` : ''}`;
    const homeScore = fixture.goals.home;
    const awayScore = fixture.goals.away;

    // Extract reason from detail or comments
    let reason = '';
    if (event.detail && event.detail.toLowerCase().includes('disallowed')) {
      // If detail contains "- reason", extract the reason
      const detailParts = event.detail.split('-');
      if (detailParts.length > 1) {
        reason = detailParts[1].trim();
      }
    }

    // If no reason found in detail, check comments
    if (!reason && event.comments) {
      reason = event.comments;
    }

    // Format reason text if available
    const reasonText = reason ? ` (${reason})` : '';

    // Format: 3 lines with title and body separated
    const title = `❌ Goal disallowed`;
    const body = `${playerName} (${minute})${reasonText}\n${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;

    // Prepare notification data
    const notificationData = {
      type: 'goalDisallowed',
      fixtureId,
      eventTime: event.time,
      reason: reason,
      score: {
        home: homeScore,
        away: awayScore
      },
      player: {
        id: event.player.id,
        name: playerName
      }
    };

    // Send to authenticated users
    await sendToAuthenticatedUsers(fixtureId, title, body, notificationData, 'goals');

    // Send to anonymous users
    await sendToAnonymousUsers(fixtureId, title, body, notificationData, 'goals');
  } catch (error) {
    console.error('Error sending goal disallowed notification:', error);
  }
}

// Send red card notification
export async function sendRedCardNotification(fixtureId: number, event: EventInfo): Promise<void> {
  try {
    const fixturesCollection = getFixturesCollection();
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });

    if (!fixture) {
      console.error(`Cannot send red card notification: Fixture ${fixtureId} not found`);
      return;
    }

    const homeTeam = fixture.teams.home.name;
    const awayTeam = fixture.teams.away.name;
    const playerName = event.player.name || 'Unknown player';
    const minute = `${event.time.elapsed}'${event.time.extra ? `+${event.time.extra}` : ''}`;
    const homeScore = fixture.goals.home;
    const awayScore = fixture.goals.away;

    // Format: 3 lines with title and body separated
    const title = `🟥 RED CARD`;
    const body = `${playerName} (${minute})\n${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;

    // Prepare notification data
    const notificationData = {
      type: 'redCard',
      fixtureId,
      eventTime: event.time,
      player: {
        name: playerName,
        team: event.team.name
      }
    };

    // Send to authenticated users
    await sendToAuthenticatedUsers(fixtureId, title, body, notificationData, 'redCards');

    // Send to anonymous users
    await sendToAnonymousUsers(fixtureId, title, body, notificationData, 'redCards');
  } catch (error) {
    console.error('Error sending red card notification:', error);
  }
}

// Send match status notification
export async function sendMatchStatusNotification(fixtureId: number, status: string): Promise<void> {
  try {
    const fixturesCollection = getFixturesCollection();
    const fixture = await fixturesCollection.findOne({ _id: fixtureId });

    if (!fixture) {
      console.error(`Cannot send match status notification: Fixture ${fixtureId} not found`);
      return;
    }

    const homeTeam = fixture.teams.home.name;
    const awayTeam = fixture.teams.away.name;
    const homeScore = fixture.goals.home;
    const awayScore = fixture.goals.away;
    const leagueName = fixture.league?.name || '';
    let title = '';
    let body = '';

    switch (status) {
      case '1H':
        // Format: 3 lines with title and body separated
        title = `🏆 KICKOFF!`;
        body = `${homeTeam} vs ${awayTeam}\n${leagueName}`;
        break;
      case '2H':
        // Format: 3 lines with title and body separated
        title = `▶️ Second Half Starts`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        break;
      case 'HT':
        // Format: 3 lines with title and body separated
        title = `⏱️ Half Time`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        break;
      case 'FT':
        // Format: 3 lines with title and body separated
        title = `🏁 Full Time`;
        // Add trophy icon next to the winning team
        const homeWins = homeScore > awayScore;
        const awayWins = awayScore > homeScore;
        const homeTeamDisplay = homeWins ? `🏆 ${homeTeam}` : homeTeam;
        const awayTeamDisplay = awayWins ? `${awayTeam} 🏆` : awayTeam;
        body = `${homeTeamDisplay} ${homeScore} - ${awayScore} ${awayTeamDisplay}`;
        break;
      // Extra Time notifications
      case 'ET':
        // Extra Time begins
        title = `⏱️ Extra Time Begins`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        console.log(`Sending 'Extra Time Begins' notification for fixture ${fixtureId}`);
        break;
      case 'BT':
        // Break Time (Half-time of Extra Time)
        title = `⏱️ Extra Time Half-Time`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        console.log(`Sending 'Extra Time Half-Time' notification for fixture ${fixtureId}`);
        break;
      // Penalty Shootout notifications
      case 'P':
        // Penalty Shootout begins
        title = `🎯 Penalty Shootout Begins`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        console.log(`Sending 'Penalty Shootout Begins' notification for fixture ${fixtureId}`);
        break;
      case 'AET':
        // After Extra Time
        title = `⏱️ Extra Time Finished`;
        body = `${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
        console.log(`Sending 'After Extra Time' notification for fixture ${fixtureId}`);
        break;
      case 'PEN':
        // Penalty Shootout concluded
        title = `🎯 Penalty Shootout Concluded`;

        // Get penalty scores if available
        let homePenaltyScore = 0;
        let awayPenaltyScore = 0;

        // Check if penalty scores are available in the fixture data
        if (fixture.score && fixture.score.penalty) {
          homePenaltyScore = fixture.score.penalty.home || 0;
          awayPenaltyScore = fixture.score.penalty.away || 0;
        }

        // Add trophy icon next to the winning team based on penalty scores
        const homePenWins = homePenaltyScore > awayPenaltyScore;
        const awayPenWins = awayPenaltyScore > homePenaltyScore;
        const homePenTeamDisplay = homePenWins ? `🏆 ${homeTeam}` : homeTeam;
        const awayPenTeamDisplay = awayPenWins ? `${awayTeam} 🏆` : awayTeam;

        // Include both regular time score and penalty score
        body = `${homePenTeamDisplay} ${homeScore} - ${awayScore} ${awayPenTeamDisplay}\nPenalties: ${homePenaltyScore} - ${awayPenaltyScore}`;
        console.log(`Sending 'Penalty Shootout Concluded' notification for fixture ${fixtureId} with penalty scores: ${homePenaltyScore}-${awayPenaltyScore}`);
        break;
      default:
        // Format: 3 lines with title and body separated
        title = `⚽ Match Update`;
        body = `${status}\n${homeTeam} ${homeScore} - ${awayScore} ${awayTeam}`;
    }

    // Prepare notification data
    const notificationData = {
      type: 'matchStatus',
      fixtureId,
      status,
      score: {
        home: homeScore,
        away: awayScore
      }
    };

    // Send to authenticated users
    await sendToAuthenticatedUsers(fixtureId, title, body, notificationData, 'matchStatus');

    // Send to anonymous users
    await sendToAnonymousUsers(fixtureId, title, body, notificationData, 'matchStatus');
  } catch (error) {
    console.error('Error sending match status notification:', error);
  }
}
