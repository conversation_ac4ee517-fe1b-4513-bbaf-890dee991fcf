import { getRedisClient } from '../config/redis';
import { getDb } from '../config/database';

// Interface for live match state
export interface LiveMatchState {
  fixtureId: number;
  leagueId: number;
  season: number;
  homeTeamId: number;
  awayTeamId: number;
  originalScore: { home: number; away: number };
  currentScore: { home: number; away: number };
  status: string;
  minute: number;
  startTime: Date;
  lastUpdated: Date;
  isLive: boolean;
  isFinished: boolean;
}

// Interface for match state change
export interface MatchStateChange {
  fixtureId: number;
  previousScore: { home: number; away: number };
  currentScore: { home: number; away: number };
  previousStatus: string;
  currentStatus: string;
  minute: number;
  timestamp: Date;
}

/**
 * Live Match State Service
 * Tracks the state of live matches for standings calculations
 */
export class LiveMatchStateService {
  private redisClient = getRedisClient();
  private readonly CACHE_PREFIX = 'live_match_state:';
  private readonly CACHE_TTL = 60 * 60 * 6; // 6 hours

  /**
   * Get cache key for a match state
   */
  private getCacheKey(fixtureId: number): string {
    return `${this.CACHE_PREFIX}${fixtureId}`;
  }

  /**
   * Initialize or update live match state
   */
  async initializeMatchState(
    fixtureId: number,
    leagueId: number,
    season: number,
    homeTeamId: number,
    awayTeamId: number,
    currentScore: { home: number; away: number },
    status: string,
    minute: number = 0
  ): Promise<LiveMatchState> {
    try {
      const cacheKey = this.getCacheKey(fixtureId);
      
      // Try to get existing state
      let existingState = await this.getMatchState(fixtureId);
      
      if (existingState) {
        // Update existing state
        const previousScore = { ...existingState.currentScore };
        const previousStatus = existingState.status;
        
        existingState.currentScore = { ...currentScore };
        existingState.status = status;
        existingState.minute = minute;
        existingState.lastUpdated = new Date();
        existingState.isLive = this.isLiveStatus(status);
        existingState.isFinished = this.isFinishedStatus(status);
        
        // Store updated state
        await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(existingState));
        
        // Log state change if significant
        if (previousScore.home !== currentScore.home || 
            previousScore.away !== currentScore.away || 
            previousStatus !== status) {
          console.log(`Match state updated for fixture ${fixtureId}: ${previousScore.home}-${previousScore.away} -> ${currentScore.home}-${currentScore.away}, ${previousStatus} -> ${status}`);
        }
        
        return existingState;
      } else {
        // Create new state
        const newState: LiveMatchState = {
          fixtureId,
          leagueId,
          season,
          homeTeamId,
          awayTeamId,
          originalScore: { ...currentScore }, // Assume current score is original when first seen
          currentScore: { ...currentScore },
          status,
          minute,
          startTime: new Date(),
          lastUpdated: new Date(),
          isLive: this.isLiveStatus(status),
          isFinished: this.isFinishedStatus(status)
        };
        
        // Store new state
        await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(newState));
        
        console.log(`New match state initialized for fixture ${fixtureId}: ${homeTeamId} vs ${awayTeamId}`);
        
        return newState;
      }
    } catch (error) {
      console.error(`Error initializing match state for fixture ${fixtureId}:`, error);
      throw error;
    }
  }

  /**
   * Get match state for a fixture
   */
  async getMatchState(fixtureId: number): Promise<LiveMatchState | null> {
    try {
      const cacheKey = this.getCacheKey(fixtureId);
      const stateData = await this.redisClient.get(cacheKey);
      
      if (stateData) {
        const state = JSON.parse(stateData) as LiveMatchState;
        // Convert date strings back to Date objects
        state.startTime = new Date(state.startTime);
        state.lastUpdated = new Date(state.lastUpdated);
        return state;
      }
      
      return null;
    } catch (error) {
      console.error(`Error getting match state for fixture ${fixtureId}:`, error);
      return null;
    }
  }

  /**
   * Update match score
   */
  async updateMatchScore(
    fixtureId: number,
    newScore: { home: number; away: number },
    minute: number = 0
  ): Promise<MatchStateChange | null> {
    try {
      const existingState = await this.getMatchState(fixtureId);
      
      if (!existingState) {
        console.warn(`No existing state found for fixture ${fixtureId} when updating score`);
        return null;
      }
      
      const previousScore = { ...existingState.currentScore };
      
      // Update the state
      existingState.currentScore = { ...newScore };
      existingState.minute = minute;
      existingState.lastUpdated = new Date();
      
      // Store updated state
      const cacheKey = this.getCacheKey(fixtureId);
      await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(existingState));
      
      // Return change information
      const change: MatchStateChange = {
        fixtureId,
        previousScore,
        currentScore: newScore,
        previousStatus: existingState.status,
        currentStatus: existingState.status,
        minute,
        timestamp: new Date()
      };
      
      console.log(`Score updated for fixture ${fixtureId}: ${previousScore.home}-${previousScore.away} -> ${newScore.home}-${newScore.away}`);
      
      return change;
    } catch (error) {
      console.error(`Error updating match score for fixture ${fixtureId}:`, error);
      return null;
    }
  }

  /**
   * Update match status
   */
  async updateMatchStatus(
    fixtureId: number,
    newStatus: string,
    minute: number = 0
  ): Promise<MatchStateChange | null> {
    try {
      const existingState = await this.getMatchState(fixtureId);
      
      if (!existingState) {
        console.warn(`No existing state found for fixture ${fixtureId} when updating status`);
        return null;
      }
      
      const previousStatus = existingState.status;
      
      // Update the state
      existingState.status = newStatus;
      existingState.minute = minute;
      existingState.lastUpdated = new Date();
      existingState.isLive = this.isLiveStatus(newStatus);
      existingState.isFinished = this.isFinishedStatus(newStatus);
      
      // Store updated state
      const cacheKey = this.getCacheKey(fixtureId);
      await this.redisClient.setex(cacheKey, this.CACHE_TTL, JSON.stringify(existingState));
      
      // Return change information
      const change: MatchStateChange = {
        fixtureId,
        previousScore: existingState.currentScore,
        currentScore: existingState.currentScore,
        previousStatus,
        currentStatus: newStatus,
        minute,
        timestamp: new Date()
      };
      
      console.log(`Status updated for fixture ${fixtureId}: ${previousStatus} -> ${newStatus}`);
      
      return change;
    } catch (error) {
      console.error(`Error updating match status for fixture ${fixtureId}:`, error);
      return null;
    }
  }

  /**
   * Get all live matches for a league
   */
  async getLiveMatchesForLeague(leagueId: number, season: number): Promise<LiveMatchState[]> {
    try {
      // This is a simplified implementation. In production, you might want to
      // maintain a separate index of live matches by league for better performance
      const db = getDb();
      const fixturesCollection = db.collection('fixtures');
      
      // Get all live fixtures for the league
      const liveFixtures = await fixturesCollection.find({
        'league.id': leagueId,
        'league.season': season,
        'fixture.status.short': { $in: ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'] }
      }, {
        projection: { _id: 1 }
      }).toArray();
      
      // Get match states for all live fixtures
      const liveStates: LiveMatchState[] = [];
      for (const fixture of liveFixtures) {
        const state = await this.getMatchState(Number(fixture._id));
        if (state && state.isLive) {
          liveStates.push(state);
        }
      }
      
      return liveStates;
    } catch (error) {
      console.error(`Error getting live matches for league ${leagueId}:`, error);
      return [];
    }
  }

  /**
   * Clean up finished matches (remove from cache after some time)
   */
  async cleanupFinishedMatches(): Promise<void> {
    try {
      // This is a placeholder for cleanup logic
      // In production, you might want to implement a more sophisticated cleanup
      console.log('Cleanup finished matches - placeholder implementation');
    } catch (error) {
      console.error('Error cleaning up finished matches:', error);
    }
  }

  /**
   * Check if status indicates a live match
   */
  private isLiveStatus(status: string): boolean {
    return ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status);
  }

  /**
   * Check if status indicates a finished match
   */
  private isFinishedStatus(status: string): boolean {
    return ['FT', 'AET', 'PEN', 'AWD', 'WO'].includes(status);
  }

  /**
   * Remove match state (useful for testing or cleanup)
   */
  async removeMatchState(fixtureId: number): Promise<void> {
    try {
      const cacheKey = this.getCacheKey(fixtureId);
      await this.redisClient.del(cacheKey);
      console.log(`Match state removed for fixture ${fixtureId}`);
    } catch (error) {
      console.error(`Error removing match state for fixture ${fixtureId}:`, error);
    }
  }
}

// Export singleton instance
let liveMatchStateServiceInstance: LiveMatchStateService | null = null;

export function initializeLiveMatchStateService(): LiveMatchStateService {
  if (!liveMatchStateServiceInstance) {
    liveMatchStateServiceInstance = new LiveMatchStateService();
  }
  return liveMatchStateServiceInstance;
}

export function getLiveMatchStateService(): LiveMatchStateService | null {
  return liveMatchStateServiceInstance;
}
