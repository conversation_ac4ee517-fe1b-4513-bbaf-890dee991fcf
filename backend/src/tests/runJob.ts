import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchAndUpdatePlayerProfiles } from '../jobs/playerJobs';
import { fetchAndUpdateCoaches } from '../jobs/coachJobs';
import { fetchAndUpdateTeamTransfers } from '../jobs/transferJobs';
import { fetchAndUpdateTeamSidelined, fetchAndUpdateLeagueSidelined } from '../jobs/sidelinedJobs';

// Load environment variables
dotenv.config();

// Job to run (change this to run different jobs)
type JobType = 'players' | 'coaches' | 'transfers' | 'sidelined';
const JOB_TO_RUN: JobType = 'sidelined'; // Options: 'players', 'coaches', 'transfers', 'sidelined'

// Main function
async function runJob() {
  try {
    console.log('Connecting to database...');
    await connectDB();
    connectRedis();

    console.log(`Running job: ${JOB_TO_RUN}`);

    switch (JOB_TO_RUN) {
      case 'players':
        await fetchAndUpdatePlayerProfiles(1); // Fetch just 1 page for testing
        break;
      case 'coaches':
        await fetchAndUpdateCoaches();
        break;
      case 'transfers':
        await fetchAndUpdateTeamTransfers();
        break;
      case 'sidelined':
        await fetchAndUpdateLeagueSidelined();
        break;
      default:
        console.error(`Unknown job: ${JOB_TO_RUN}`);
    }

    console.log('Job completed successfully');
    process.exit(0);

  } catch (error) {
    console.error('Error running job:', error);
    process.exit(1);
  }
}

// Run the job
runJob();
