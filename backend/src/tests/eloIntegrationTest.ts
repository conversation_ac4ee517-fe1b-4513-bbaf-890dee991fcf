/**
 * ELO Integration Test
 * 
 * Test the enhanced prediction system with ELO integration
 * Specifically tests cross-league scenarios like Champions League
 */

import { EnhancedPredictionService } from '../services/enhancedPredictionService';
import { syncEloRatings } from '../services/eloService';
import connectDB from '../config/database';

async function testEloIntegration() {
  console.log('🧪 Starting ELO Integration Test...\n');

  try {
    // Connect to database
    await connectDB();
    console.log('✅ Connected to database');

    // Sync ELO ratings first (optional - comment out if already synced)
    console.log('🔄 Syncing ELO ratings...');
    // await syncEloRatings();
    console.log('✅ ELO ratings synced');

    // Test case: Champions League match (Real Madrid vs Basel scenario)
    // Using actual team IDs from your database
    const testCases = [
      {
        name: 'Champions League - Strong vs Weak Team',
        fixtureId: 999999, // Mock fixture ID
        homeTeamId: 541, // Real Madrid (if available in your DB)
        awayTeamId: 1396, // Basel (if available in your DB) 
        leagueId: 2, // Champions League
        description: 'Tests cross-league comparison with ELO enhancement'
      },
      {
        name: 'Premier League - Domestic Match',
        fixtureId: 999998,
        homeTeamId: 33, // Manchester United (example)
        awayTeamId: 40, // Liverpool (example)
        leagueId: 39, // Premier League
        description: 'Tests domestic league with sufficient historical data'
      }
    ];

    for (const testCase of testCases) {
      console.log(`\n🎯 Testing: ${testCase.name}`);
      console.log(`   ${testCase.description}`);
      
      try {
        const prediction = await EnhancedPredictionService.generatePrediction(
          testCase.fixtureId,
          testCase.homeTeamId,
          testCase.awayTeamId,
          testCase.leagueId,
          new Date()
        );

        if (prediction) {
          console.log('✅ Prediction generated successfully');
          console.log(`   Algorithm: ${prediction.metadata.algorithm}`);
          console.log(`   Model Version: ${prediction.metadata.modelVersion}`);
          console.log(`   Processing Time: ${prediction.metadata.processingTime}ms`);
          console.log(`   Overall Confidence: ${prediction.metadata.confidence.toFixed(1)}%`);
          
          // Check if ELO data was used
          if (prediction.metadata.eloData) {
            console.log('🎯 ELO Enhancement Applied:');
            console.log(`   Home ELO: ${prediction.metadata.eloData.homeEloRating.toFixed(0)}`);
            console.log(`   Away ELO: ${prediction.metadata.eloData.awayEloRating.toFixed(0)}`);
            console.log(`   ELO Ratio: ${prediction.metadata.eloData.eloRatio.toFixed(3)}`);
            console.log(`   ELO Influence: ${(prediction.metadata.eloData.eloInfluence * 100).toFixed(1)}%`);
          } else {
            console.log('ℹ️  No ELO enhancement (using historical data only)');
          }

          // Show prediction results
          console.log('📊 Prediction Results:');
          console.log(`   Home Win: ${(prediction.predictions.matchOutcome.homeWin * 100).toFixed(1)}%`);
          console.log(`   Draw: ${(prediction.predictions.matchOutcome.draw * 100).toFixed(1)}%`);
          console.log(`   Away Win: ${(prediction.predictions.matchOutcome.awayWin * 100).toFixed(1)}%`);
          console.log(`   Expected Goals: ${prediction.predictions.expectedGoals.home.toFixed(2)} - ${prediction.predictions.expectedGoals.away.toFixed(2)}`);
          console.log(`   Most Likely Score: ${prediction.predictions.correctScore.mostLikely.home}-${prediction.predictions.correctScore.mostLikely.away} (${(prediction.predictions.correctScore.mostLikely.probability * 100).toFixed(1)}%)`);
          console.log(`   BTTS: ${prediction.predictions.bothTeamsToScore.prediction ? 'Yes' : 'No'} (${(prediction.predictions.bothTeamsToScore.probability * 100).toFixed(1)}%)`);

        } else {
          console.log('❌ Failed to generate prediction');
        }

      } catch (error) {
        console.error(`❌ Error in test case "${testCase.name}":`, error);
      }
    }

    console.log('\n✅ ELO Integration Test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  testEloIntegration()
    .then(() => {
      console.log('\n🎉 Test execution finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Test execution failed:', error);
      process.exit(1);
    });
}

export { testEloIntegration };
