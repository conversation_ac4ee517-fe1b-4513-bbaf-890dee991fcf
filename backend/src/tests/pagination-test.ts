/**
 * COMPREHENSIVE PAGINATION TEST SCRIPT
 * This script tests the new pagination feature for the fixtures endpoint
 * by directly testing against the MongoDB database.
 */

import { MongoClient } from 'mongodb';
import { Fixture, getFixturesCollection } from '../models/Fixture';
import dayjs from 'dayjs';

// Mock MongoDB fixtures for testing
const SAMPLE_SIZE = 51; // Create enough fixtures to test pagination
const TODAY = dayjs().format('YYYY-MM-DD');
const YESTERDAY = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
const TOMORROW = dayjs().add(1, 'day').format('YYYY-MM-DD');

// Create our own test fixtures handler based on the real implementation
async function testFixturesPagination() {
    console.log('🧪 STARTING COMPREHENSIVE PAGINATION TESTS 🧪');
    
    try {
        // Connect to MongoDB
        console.log('\n📊 Connecting to MongoDB and setting up test data...');
        const client = await MongoClient.connect('mongodb://localhost:27017');
        const db = client.db('kickoffscore_test');
        const collection = db.collection<Fixture>('fixtures');
        
        // Clear any existing test data
        await collection.deleteMany({});
        
        // Generate test data
        const today = dayjs().unix();
        const fixtures: Partial<Fixture>[] = [];
        
        // Create fixtures for yesterday, today, and tomorrow
        for (let i = 1; i <= SAMPLE_SIZE; i++) {
            const dayOffset = i % 3 === 0 ? -1 : (i % 3 === 1 ? 0 : 1); // Distribute across days
            const timestamp = dayjs().add(dayOffset, 'day').unix();
            
            fixtures.push({
                _id: 1000 + i,
                fixture: {
                    id: 1000 + i,
                    timestamp,
                    date: dayjs.unix(timestamp).format('YYYY-MM-DD'),
                    referee: null,
                    timezone: 'UTC',
                    status: { short: 'NS', long: 'Not Started', elapsed: null },
                    periods: { first: null, second: null },
                    venue: { id: i, name: `Test Venue ${i}`, city: 'Test City' }
                },
                league: { 
                    id: Math.ceil(i/10), 
                    name: `Test League ${Math.ceil(i/10)}`, 
                    season: 2025,
                    country: 'Test Country',
                    logo: 'https://example.com/logo.png',
                    flag: 'https://example.com/flag.png',
                    round: 'Regular Season - 1'
                },
                teams: {
                    home: { id: i*2, name: `Home Team ${i}`, logo: 'https://example.com/team.png', winner: null },
                    away: { id: i*2+1, name: `Away Team ${i}`, logo: 'https://example.com/team.png', winner: null }
                },
                goals: { home: 0, away: 0 },
                score: { 
                    halftime: { home: 0, away: 0 }, 
                    fulltime: { home: 0, away: 0 },
                    extratime: { home: null, away: null },
                    penalty: { home: null, away: null }
                }
            });
        }
        
        // Insert fixtures
        await collection.insertMany(fixtures as any[]);
        console.log(`✅ Created ${fixtures.length} test fixtures in the database`);
        
        // =========== TEST SCENARIOS ===========
        
        // SCENARIO 1: Basic pagination with dates
        console.log('\n🔍 SCENARIO 1: Basic pagination with date filtering');
        await testPaginationScenario(collection, { 'fixture.timestamp': { $gte: dayjs(TODAY).startOf('day').unix(), $lte: dayjs(TODAY).endOf('day').unix() } }, 'date=' + TODAY);
        
        // SCENARIO 2: Pagination with league filter
        console.log('\n🔍 SCENARIO 2: Pagination with league filtering');
        await testPaginationScenario(collection, { 'league.id': 1 }, 'league=1&season=2025');
        
        // SCENARIO 3: Pagination with team filter
        console.log('\n🔍 SCENARIO 3: Pagination with team filtering');
        await testPaginationScenario(collection, { $or: [{ 'teams.home.id': 10 }, { 'teams.away.id': 10 }] }, 'team=10');
        
        // SCENARIO 4: Testing edge cases
        console.log('\n🔍 SCENARIO 4: Edge cases');
        
        // 4.1: Request a page beyond what's available
        console.log('  • Testing pagination beyond available pages');
        const pageSize = 15;
        const totalFixtures = await collection.countDocuments({ 'league.id': 1 });
        const totalPages = Math.ceil(totalFixtures / pageSize);
        
        console.log(`    Total fixtures: ${totalFixtures}, Pages at ${pageSize} per page: ${totalPages}`);
        console.log(`    Testing request for page ${totalPages + 1}...`);
        
        const result = await simulatePaginatedRequest(collection, { 'league.id': 1 }, totalPages + 1, pageSize);
        if (result.error) {
            console.log(`    ✅ Correctly received error: ${result.error}`);
        } else {
            console.log(`    ❌ Should have received an error for invalid page but got ${result.data.length} results instead`);
        }
        
        // SCENARIO 5: Test that pagination is not applied with other limiting parameters
        console.log('\n🔍 SCENARIO 5: Testing backward compatibility');
        
        // 5.1: Single fixture by ID
        console.log('  • Testing fixture by ID (should not be paginated)');
        const fixtureById = await simulateNonPaginatedRequest(collection, { _id: 1001 });
        if (fixtureById.isPaginated) {
            console.log('    ❌ ID query was incorrectly paginated');
        } else {
            console.log('    ✅ ID query correctly returned without pagination');
        }
        
        // 5.2: Last N fixtures
        console.log('  • Testing last parameter (should not be paginated)');
        const lastFixtures = await simulateLastNRequest(collection, { 'league.id': 1 }, 3);
        if (lastFixtures.isPaginated) {
            console.log('    ❌ Last N query was incorrectly paginated');
        } else {
            console.log('    ✅ Last N query correctly returned without pagination');
            if (lastFixtures.count <= 3) {
                console.log(`    ✅ Correctly limited to ${lastFixtures.count} fixtures`);
            } else {
                console.log(`    ❌ Should have limited to 3 fixtures but got ${lastFixtures.count}`);
            }
        }
        
        // Clean up
        await collection.deleteMany({});
        await client.close();
        console.log('\n🧹 Cleaned up test data and closed database connection');
        console.log('\n✅ ALL TESTS COMPLETED ✅');
        
    } catch (error) {
        console.error('❌ Test failed with error:', error);
    }
}

// Simulate a paginated request using the same logic as our handler
async function testPaginationScenario(collection, filter, filterDescription) {
    // Test first page
    const page1 = await simulatePaginatedRequest(collection, filter, 1, 5);
    console.log(`  • Page 1 with ${filterDescription}:`);
    console.log(`    Total: ${page1.totalCount}, Results: ${page1.data.length}`);
    console.log(`    First few IDs: [${page1.data.slice(0, 3).map(f => f._id).join(', ')}...]`);
    
    // Test second page
    const page2 = await simulatePaginatedRequest(collection, filter, 2, 5);
    console.log(`  • Page 2 with ${filterDescription}:`);
    console.log(`    Results: ${page2.data.length}`);
    console.log(`    First few IDs: [${page2.data.slice(0, 3).map(f => f._id).join(', ')}...]`);
    
    // Check for duplicates between pages
    const page1Ids = page1.data.map(f => f._id);
    const page2Ids = page2.data.map(f => f._id);
    const duplicates = page1Ids.filter(id => page2Ids.includes(id));
    
    if (duplicates.length > 0) {
        console.log(`    ❌ Found ${duplicates.length} duplicate fixtures between pages: ${duplicates}`);
    } else {
        console.log('    ✅ No duplicates found between pages');
    }
    
    return { page1, page2, hasDuplicates: duplicates.length > 0 };
}

// Simulates our real handler's pagination logic
async function simulatePaginatedRequest(collection, filter, page, pageSize) {
    const currentPage = page || 1;
    const itemsPerPage = pageSize || 15;
    
    const totalCount = await collection.countDocuments(filter);
    const totalPages = Math.ceil(totalCount / itemsPerPage);
    
    // Check for invalid page
    if (currentPage > totalPages && totalPages > 0) {
        return {
            error: `Requested page ${currentPage} exceeds available pages (${totalPages})`,
            pagination: {
                page: currentPage,
                pageSize: itemsPerPage,
                totalPages,
                totalCount,
                hasNextPage: false,
                hasPrevPage: totalPages > 0
            },
            data: []
        };
    }
    
    const skip = (currentPage - 1) * itemsPerPage;
    const data = await collection.find(filter)
        .sort({ 'fixture.timestamp': 1, '_id': 1 }) // Add secondary sort by _id to ensure consistent ordering
        .skip(skip)
        .limit(itemsPerPage)
        .toArray();
    
    return {
        data,
        totalCount,
        totalPages,
        pagination: {
            page: currentPage,
            pageSize: itemsPerPage,
            totalPages,
            totalCount,
            hasNextPage: currentPage < totalPages,
            hasPrevPage: currentPage > 1
        }
    };
}

// Simulates a non-paginated request (e.g., by ID)
async function simulateNonPaginatedRequest(collection, filter) {
    const fixtures = await collection.find(filter).toArray();
    return {
        data: fixtures,
        count: fixtures.length,
        isPaginated: false
    };
}

// Simulates a last N request
async function simulateLastNRequest(collection, filter, limit) {
    const nowTimestamp = dayjs().unix();
    const queryFilter = { ...filter, 'fixture.timestamp': { $lt: nowTimestamp } };
    const fixtures = await collection.find(queryFilter)
        .sort({ 'fixture.timestamp': -1 })
        .limit(limit)
        .toArray();
    
    return {
        data: fixtures,
        count: fixtures.length,
        isPaginated: false
    };
}

// Run the test
testFixturesPagination().catch(error => {
    console.error('Unhandled error in test script:', error);
});

