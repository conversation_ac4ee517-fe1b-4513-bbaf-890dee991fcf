import axios from 'axios';
import dotenv from 'dotenv';
import { writeFileSync } from 'fs';
import path from 'path';

// Load environment variables
dotenv.config();

// Base URL for the API
const BASE_URL = `http://localhost:${process.env.PORT || 3000}/api`;

// Fixture ID to test
const FIXTURE_ID = 1361163; // Valid fixture ID from today's fixtures

// Helper function to check if a fixture has comprehensive data
function hasComprehensiveData(fixture: any): boolean {
  // Check if the fixture has all the required fields
  return (
    fixture &&
    fixture.fixture &&
    fixture.league &&
    fixture.teams &&
    fixture.goals &&
    fixture.score &&
    (fixture.events || []).length > 0 &&
    (fixture.lineups || []).length > 0 &&
    (fixture.statistics || []).length > 0
  );
}

// Main test function
async function testFixtureDetail() {
  try {
    console.log(`Testing fixture detail for ID: ${FIXTURE_ID}`);

    // Make the request
    const response = await axios.get(`${BASE_URL}/fixtures`, {
      params: { id: FIXTURE_ID }
    });

    // Check if we got a response
    if (!response.data) {
      console.error('No fixture data returned');
      return;
    }

    // Handle both array and single object responses
    const fixture = Array.isArray(response.data) ? response.data[0] : response.data;

    // Save the fixture data for inspection
    const filePath = path.join(__dirname, `../../fixture-${FIXTURE_ID}.json`);
    writeFileSync(filePath, JSON.stringify(fixture, null, 2));
    console.log(`Fixture data saved to ${filePath}`);

    // Check if the fixture has comprehensive data
    const isComprehensive = hasComprehensiveData(fixture);

    console.log('\nFixture Detail Test Summary:');
    console.log(`Fixture ID: ${FIXTURE_ID}`);
    console.log(`Has comprehensive data: ${isComprehensive ? 'YES' : 'NO'}`);

    // Print what data is available
    console.log('\nAvailable Data:');
    console.log(`- Basic Info: ${fixture.fixture ? 'YES' : 'NO'}`);
    console.log(`- League Info: ${fixture.league ? 'YES' : 'NO'}`);
    console.log(`- Teams Info: ${fixture.teams ? 'YES' : 'NO'}`);
    console.log(`- Goals Info: ${fixture.goals ? 'YES' : 'NO'}`);
    console.log(`- Score Info: ${fixture.score ? 'YES' : 'NO'}`);
    console.log(`- Events: ${fixture.events ? `YES (${fixture.events.length} events)` : 'NO'}`);
    console.log(`- Lineups: ${fixture.lineups ? `YES (${fixture.lineups.length} teams)` : 'NO'}`);
    console.log(`- Statistics: ${fixture.statistics ? `YES (${fixture.statistics.length} teams)` : 'NO'}`);
    console.log(`- Players: ${fixture.players ? `YES (${fixture.players.length} teams)` : 'NO'}`);

  } catch (error: any) {
    console.error('Error testing fixture detail:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
console.log('Starting fixture detail test...');
testFixtureDetail()
  .then(() => console.log('Test completed.'))
  .catch(error => console.error('Error running test:', error));
