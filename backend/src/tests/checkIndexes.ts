import dotenv from 'dotenv';
import connectDB from '../config/database';
import { getDb } from '../config/database';

// Load environment variables
dotenv.config();

// Main function
async function checkIndexes() {
  try {
    console.log('Connecting to database...');
    await connectDB();
    
    const db = getDb();
    
    // Get collections
    const fixturesCollection = db.collection('fixtures');
    const leaguesCollection = db.collection('leagues');
    
    // Get indexes
    console.log('Fixtures collection indexes:');
    const fixtureIndexes = await fixturesCollection.listIndexes().toArray();
    console.log(JSON.stringify(fixtureIndexes, null, 2));
    
    console.log('\nLeagues collection indexes:');
    const leagueIndexes = await leaguesCollection.listIndexes().toArray();
    console.log(JSON.stringify(leagueIndexes, null, 2));
    
    process.exit(0);
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run the function
checkIndexes();
