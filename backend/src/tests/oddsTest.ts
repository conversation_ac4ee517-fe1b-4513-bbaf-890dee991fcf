import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { 
    fetchAndUpdateBookmakers, 
    fetchAndUpdateBets, 
    fetchAndUpdateOdds, 
    fetchAndUpdateLiveOdds 
} from '../jobs/oddsJobs';

// Load environment variables
dotenv.config();

// Main function
async function testOddsJobs() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        connectRedis();
        
        // Test bookmakers job
        console.log('Testing fetchAndUpdateBookmakers job...');
        await fetchAndUpdateBookmakers();
        
        // Test bets job
        console.log('Testing fetchAndUpdateBets job...');
        await fetchAndUpdateBets();
        
        // Test odds job
        console.log('Testing fetchAndUpdateOdds job...');
        await fetchAndUpdateOdds();
        
        // Test live odds job
        console.log('Testing fetchAndUpdateLiveOdds job...');
        await fetchAndUpdateLiveOdds();
        
        console.log('All odds jobs completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error:', error);
        process.exit(1);
    }
}

// Run the function
testOddsJobs();
