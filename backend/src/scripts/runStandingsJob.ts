import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateStandings } from '../jobs/standingJobs';

// Load environment variables
dotenv.config();

// Main function
async function runStandingsJob() {
    try {
        console.log('=== TESTING NEW FIXTURE-DAY BASED STANDINGS UPDATE ===');
        console.log('Connecting to database...');
        await connectDB();

        console.log('Running fetchAndUpdateStandings job with new logic...');
        console.log('Expected behavior:');
        console.log('- Leagues with fixtures today: Update every hour');
        console.log('- Leagues without fixtures today: Update every 6 hours (fallback)');
        console.log('');

        await fetchAndUpdateStandings();

        console.log('Job completed successfully');
        process.exit(0);

    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runStandingsJob();
