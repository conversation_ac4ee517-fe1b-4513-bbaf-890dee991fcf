import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

async function fetchFixtureData() {
    const fixtureId = parseInt(process.argv[2]);
    
    if (!fixtureId) {
        console.error('Please provide a fixture ID');
        process.exit(1);
    }

    try {
        console.log('Connecting to database...');
        await connectDB();
        console.log('MongoDB connected successfully.');

        console.log('Connecting to Redis...');
        await connectRedis();
        console.log('Redis connected successfully.');

        console.log(`Fetching detailed data for fixture ID: ${fixtureId}`);
        await fetchAndUpdateFixturesById([fixtureId]);
        console.log('Fixture data fetched successfully');

        process.exit(0);
    } catch (error) {
        console.error('Error fetching fixture data:', error);
        process.exit(1);
    }
}

fetchFixtureData();
