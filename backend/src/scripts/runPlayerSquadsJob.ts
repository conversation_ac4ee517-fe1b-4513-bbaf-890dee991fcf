import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdatePlayerSquads } from '../jobs/playerJobs';

// Load environment variables
dotenv.config();

// Main function
async function runPlayerSquadsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdatePlayerSquads job...');
        await fetchAndUpdatePlayerSquads();
        
        console.log('Player squads job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runPlayerSquadsJob();
