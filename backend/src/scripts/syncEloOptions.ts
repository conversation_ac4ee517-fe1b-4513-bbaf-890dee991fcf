/**
 * ELO Sync Options Script
 * 
 * Provides different ELO sync options:
 * - incremental: Default daily sync (preserves mappings)
 * - full: Full remapping (destructive, loses manual mappings)
 * - mappings-only: Only update mappings without fetching new ELO data
 * - stats: Show current mapping statistics
 */

import connectDB from '../config/database';
import { 
    syncEloRatings, 
    syncEloRatingsWithFullRemapping,
    updateTeamMappingsIncremental,
    generateEloEnhancedStrength
} from '../services/eloService';
import { 
    getEloRatingsCollection,
    getTeamNameMappingsCollection,
    getEloEnhancedStrengthCollection
} from '../models/EloRating';

async function showStats(): Promise<void> {
    console.log('📊 ELO System Statistics\n');
    
    const eloCollection = getEloRatingsCollection();
    const mappingsCollection = getTeamNameMappingsCollection();
    const strengthCollection = getEloEnhancedStrengthCollection();
    
    const [
        totalEloTeams,
        rankedEloTeams,
        totalMappings,
        verifiedMappings,
        highConfidenceMappings,
        strengthEntries,
        lastEloUpdate,
        lastMappingUpdate
    ] = await Promise.all([
        eloCollection.countDocuments(),
        eloCollection.countDocuments({ rank: { $ne: null } }),
        mappingsCollection.countDocuments(),
        mappingsCollection.countDocuments({ verified: true }),
        mappingsCollection.countDocuments({ confidence: { $gte: 0.8 } }),
        strengthCollection.countDocuments(),
        eloCollection.findOne({}, { sort: { lastUpdated: -1 } }),
        mappingsCollection.findOne({}, { sort: { lastUpdated: -1 } })
    ]);
    
    console.log(`📈 ELO Teams: ${totalEloTeams} (${rankedEloTeams} ranked)`);
    console.log(`🔗 Mappings: ${totalMappings} total, ${verifiedMappings} verified, ${highConfidenceMappings} high-confidence`);
    console.log(`💪 Enhanced Strength: ${strengthEntries} teams`);
    console.log(`📅 Last ELO Update: ${lastEloUpdate?.lastUpdated || 'Never'}`);
    console.log(`📅 Last Mapping Update: ${lastMappingUpdate?.lastUpdated || 'Never'}`);
    console.log(`📊 Mapping Coverage: ${((totalMappings / totalEloTeams) * 100).toFixed(1)}%`);
    console.log(`🏆 Top 100 Coverage: ${totalMappings >= rankedEloTeams ? '100%' : ((totalMappings / rankedEloTeams) * 100).toFixed(1) + '%'}`);
}

async function syncIncremental(): Promise<void> {
    console.log('🔄 Running incremental ELO sync (preserves existing mappings)...\n');
    await syncEloRatings();
    console.log('\n✅ Incremental sync completed!');
}

async function syncFull(): Promise<void> {
    console.log('⚠️  Running FULL ELO sync (will lose manual mappings)...\n');
    
    // Confirm destructive action
    console.log('This will DELETE all existing mappings and recreate them.');
    console.log('Manual/verified mappings will be LOST.');
    
    await syncEloRatingsWithFullRemapping();
    console.log('\n✅ Full sync completed!');
}

async function mappingsOnly(): Promise<void> {
    console.log('🔗 Updating mappings only (no new ELO data fetch)...\n');
    
    await updateTeamMappingsIncremental();
    await generateEloEnhancedStrength();
    
    console.log('\n✅ Mappings update completed!');
}

async function main(): Promise<void> {
    const command = process.argv[2];
    
    if (!command) {
        console.log('ELO Sync Options');
        console.log('================');
        console.log('');
        console.log('Usage: npm run sync-elo-options <command>');
        console.log('');
        console.log('Commands:');
        console.log('  incremental   - Default daily sync (preserves mappings) [RECOMMENDED]');
        console.log('  full          - Full remapping (destructive, loses manual mappings)');
        console.log('  mappings-only - Only update mappings without fetching new ELO data');
        console.log('  stats         - Show current ELO system statistics');
        console.log('');
        console.log('Examples:');
        console.log('  npm run sync-elo-options incremental');
        console.log('  npm run sync-elo-options stats');
        console.log('');
        process.exit(0);
    }
    
    try {
        console.log('🔌 Connecting to database...');
        await connectDB();
        console.log('✅ Database connected\n');
        
        switch (command) {
            case 'incremental':
                await syncIncremental();
                break;
            case 'full':
                await syncFull();
                break;
            case 'mappings-only':
                await mappingsOnly();
                break;
            case 'stats':
                await showStats();
                break;
            default:
                console.log(`❌ Unknown command: ${command}`);
                console.log('Run without arguments to see available commands.');
                process.exit(1);
        }
        
        // Show final stats
        if (command !== 'stats') {
            console.log('\n' + '='.repeat(50));
            await showStats();
        }
        
    } catch (error) {
        console.error('❌ Error:', error);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

// Run the script
main();
