import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';
import { fetchData } from '../services/apiFootball';

// Load environment variables
dotenv.config();

interface PlayerResponse {
    player: {
        id: number;
        name: string;
        firstname: string;
        lastname: string;
        age: number;
        nationality: string;
        height: string;
        weight: string;
        injured: boolean;
        photo: string;
    };
    statistics: Array<{
        team: {
            id: number;
            name: string;
            logo: string;
        };
        league: {
            id: number;
            name: string;
            country: string;
            logo: string;
            flag: string;
            season: number;
        };
        // other statistics fields...
    }>;
}

// Main function
async function fetchSidelinedWithTeam() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const playerId = 276; // <PERSON>
        
        // First, get the player's current team
        console.log(`Fetching current team for Player ID: ${playerId}`);
        const playerData = await fetchData<PlayerResponse[]>('/players', { 
            id: playerId,
            season: 2023 // Use current season
        });
        
        let currentTeam = { id: 0, name: 'Unknown', logo: null };
        
        if (playerData && playerData.length > 0 && playerData[0].statistics && playerData[0].statistics.length > 0) {
            currentTeam = {
                id: playerData[0].statistics[0].team.id,
                name: playerData[0].statistics[0].team.name,
                logo: playerData[0].statistics[0].team.logo
            };
            console.log(`Current team found: ${currentTeam.name} (ID: ${currentTeam.id})`);
        } else {
            console.log('No current team found for player');
        }
        
        console.log(`Fetching sidelined history for Player ID: ${playerId}`);
        const sidelinedPlayers = await fetchSidelined({ player: playerId });
        
        if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
            console.log(`No sidelined history received for Player ${playerId}.`);
            return;
        }
        
        console.log(`Received ${sidelinedPlayers.length} sidelined record(s) for Player ${playerId}:`);
        console.log(JSON.stringify(sidelinedPlayers.slice(0, 2), null, 2)); // Show first 2 sidelined records
        
        // Store in database
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();
        
        for (const sidelined of sidelinedPlayers) {
            const type = sidelined.type || 'Unknown';
            const start = sidelined.start || new Date().toISOString();
            const sidelinedId = createSidelinedId(playerId, type, start);
            
            // Create sidelined document
            const sidelinedDoc: Sidelined = {
                _id: sidelinedId,
                player: {
                    id: playerId,
                    name: 'Lionel Messi',
                    photo: null
                },
                team: currentTeam, // Use the current team
                type: type,
                reason: sidelined.reason || null,
                start: start,
                end: sidelined.end || null,
                lastUpdated: now
            };
            
            const result = await sidelinedCollection.updateOne(
                { _id: sidelinedId },
                { $set: sidelinedDoc },
                { upsert: true }
            );
            
            console.log(`Sidelined ${sidelinedId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchSidelinedWithTeam();
