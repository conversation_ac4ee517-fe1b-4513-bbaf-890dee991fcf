import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateFixturesByDate } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runFixtureJobForDate() {
    try {
        // Get date from command line arguments
        const dateArg = process.argv[2];
        
        if (!dateArg) {
            console.error('Please provide a date in YYYY-MM-DD format as a command line argument');
            process.exit(1);
        }
        
        // Validate date format (simple validation)
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(dateArg)) {
            console.error('Invalid date format. Please use YYYY-MM-DD format');
            process.exit(1);
        }
        
        console.log('Connecting to database...');
        await connectDB();
        
        console.log(`Running fetchAndUpdateFixturesByDate job for date: ${dateArg}...`);
        await fetchAndUpdateFixturesByDate(dateArg);
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runFixtureJobForDate();
