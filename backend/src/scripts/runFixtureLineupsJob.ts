import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateUpcomingFixtureLineups } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runFixtureLineupsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateUpcomingFixtureLineups job...');
        await fetchAndUpdateUpcomingFixtureLineups(3); // Check for lineups of fixtures starting in the next 3 hours
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runFixtureLineupsJob();
