/**
 * Update Bet Types Script
 * 
 * This script updates the bet types in the database to match the new IDs in targetedBetTypes.ts.
 * It fetches all bet types from the API and updates the database with the correct IDs.
 */

import dotenv from 'dotenv';
import connectDB from '../config/database';
import { getBetsCollection, BetInfo } from '../models/Odds';
import { fetchBets } from '../services/oddsService';
import { targetedBetTypeMap, targetedBetTypeIds } from '../config/targetedBetTypes';
import { AnyBulkWriteOperation } from 'mongodb';

// Load environment variables
dotenv.config();

// Main function
async function updateBetTypes() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        // Fetch all bet types from API
        console.log('Fetching bet types from API...');
        const bets = await fetchBets();
        
        console.log(`Received ${bets.length} bet types from API.`);
        
        // Filter to only include targeted bet types
        const targetedBets = bets.filter(bet => targetedBetTypeIds.includes(bet.id));
        console.log(`Filtered down to ${targetedBets.length} targeted bet types.`);
        
        // Check if all targeted bet types exist in the API response
        const missingBetTypes = targetedBetTypeIds.filter(id => 
            !bets.some(bet => bet.id === id)
        );
        
        if (missingBetTypes.length > 0) {
            console.warn('Warning: The following targeted bet types were not found in the API response:');
            missingBetTypes.forEach(id => {
                console.warn(`ID ${id} - ${targetedBetTypeMap[id]}`);
            });
        }
        
        // Get the bets collection
        const betsCollection = getBetsCollection();
        
        // Prepare bulk operations
        const bulkOps: AnyBulkWriteOperation<BetInfo>[] = [];
        const now = new Date();
        
        for (const bet of targetedBets) {
            const betDoc: BetInfo = {
                _id: bet.id,
                apiId: bet.id,
                name: bet.name,
                lastUpdated: now
            };
            
            bulkOps.push({
                updateOne: {
                    filter: { _id: bet.id },
                    update: { $set: betDoc },
                    upsert: true
                }
            });
        }
        
        if (bulkOps.length > 0) {
            const result = await betsCollection.bulkWrite(bulkOps);
            console.log(`Bet types updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        // List all bet types in the database
        console.log('\nCurrent bet types in database:');
        const dbBets = await betsCollection.find().toArray();
        dbBets.forEach(bet => {
            const isTargeted = targetedBetTypeIds.includes(bet._id) ? '(targeted)' : '';
            console.log(`ID ${bet._id} - ${bet.name} ${isTargeted}`);
        });
        
        // Check if there are any bet types in the database that are not in the targeted list
        const nonTargetedBets = dbBets.filter(bet => !targetedBetTypeIds.includes(bet._id));
        if (nonTargetedBets.length > 0) {
            console.log('\nThe following bet types in the database are not in the targeted list:');
            nonTargetedBets.forEach(bet => {
                console.log(`ID ${bet._id} - ${bet.name}`);
            });
            
            // Ask if these should be removed
            console.log('\nThese non-targeted bet types will still be available if specifically requested by ID.');
            console.log('If you want to remove them, run this script with the --remove-non-targeted flag.');
        }
        
        console.log('\nUpdate completed successfully.');
        
    } catch (error) {
        console.error('Error updating bet types:', error);
    } finally {
        process.exit(0);
    }
}

// Run the main function
updateBetTypes();
