import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateLeagues } from '../jobs/leagueJobs';

// Load environment variables
dotenv.config();

// Main function
async function runLeagueJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateLeagues job...');
        await fetchAndUpdateLeagues();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runLeagueJob();
