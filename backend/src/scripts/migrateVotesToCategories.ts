import { MongoClient } from 'mongodb';
import config from '../config';

/**
 * Migration script to add category field to existing fixture votes
 * This script updates all existing votes to have category: 'match_outcome'
 * for backward compatibility with the new multi-category voting system.
 */

async function migrateVotesToCategories() {
  const client = new MongoClient(config.database.mongoUri);
  
  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    
    const db = client.db();
    const collection = db.collection('fixtureVotes');
    
    console.log('Starting migration of existing votes...');
    
    // Count existing votes without category field
    const votesWithoutCategory = await collection.countDocuments({
      category: { $exists: false }
    });
    
    console.log(`Found ${votesWithoutCategory} votes without category field`);
    
    if (votesWithoutCategory === 0) {
      console.log('No votes need migration. All votes already have category field.');
      return;
    }
    
    // Update all votes without category to have category: 'match_outcome'
    const result = await collection.updateMany(
      { category: { $exists: false } },
      { $set: { category: 'match_outcome' } }
    );
    
    console.log(`Migration completed successfully!`);
    console.log(`Updated ${result.modifiedCount} votes with category: 'match_outcome'`);
    
    // Verify the migration
    const remainingVotesWithoutCategory = await collection.countDocuments({
      category: { $exists: false }
    });
    
    if (remainingVotesWithoutCategory === 0) {
      console.log('✅ Migration verification passed: All votes now have category field');
    } else {
      console.log(`⚠️  Warning: ${remainingVotesWithoutCategory} votes still missing category field`);
    }
    
    // Show category distribution
    const categoryStats = await collection.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]).toArray();
    
    console.log('\nCategory distribution after migration:');
    categoryStats.forEach(stat => {
      console.log(`  ${stat._id}: ${stat.count} votes`);
    });
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  } finally {
    await client.close();
    console.log('Database connection closed');
  }
}

// Run the migration if this script is executed directly
if (require.main === module) {
  migrateVotesToCategories()
    .then(() => {
      console.log('Migration script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Migration script failed:', error);
      process.exit(1);
    });
}

export { migrateVotesToCategories };
