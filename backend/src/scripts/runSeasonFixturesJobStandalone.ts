import dotenv from 'dotenv';

// Set standalone job flag BEFORE any other imports
process.env.STANDALONE_JOB = 'true';

// Load environment variables
dotenv.config();

import connectDB from '../config/database';
import { fetchAndUpdateSeasonFixtures } from '../jobs/fixtureJobs';

// Main function
async function runSeasonFixturesJobStandalone() {
    try {
        console.log('🔧 Running Season Fixtures job in standalone mode...');
        console.log('🔌 Connecting to database...');
        await connectDB();
        console.log('✅ Database connected successfully');
        
        console.log('🚀 Running fetchAndUpdateSeasonFixtures job...');
        console.log('⏰ Started at:', new Date().toISOString());
        
        await fetchAndUpdateSeasonFixtures();
        
        console.log('🎉 Job completed successfully!');
        console.log('⏰ Finished at:', new Date().toISOString());
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Error running job:', error);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run the job
runSeasonFixturesJobStandalone();
