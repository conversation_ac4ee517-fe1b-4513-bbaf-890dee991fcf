import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateVenuesForActiveTeams } from '../jobs/venueJobs';

// Load environment variables
dotenv.config();

// Main function
async function runVenueJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateVenuesForActiveTeams job...');
        await fetchAndUpdateVenuesForActiveTeams();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runVenueJob();
