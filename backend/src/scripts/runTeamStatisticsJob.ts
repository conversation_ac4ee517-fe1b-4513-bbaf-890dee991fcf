import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateTeamStatistics } from '../jobs/teamStatisticJobs';

// Load environment variables
dotenv.config();

// Main function
async function runTeamStatisticsJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateTeamStatistics job...');
        await fetchAndUpdateTeamStatistics();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runTeamStatisticsJob();
