import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchFixtures } from '../services/apiFootball';
import { getLeaguesCollection } from '../models/League';
import { targetedLeagues } from '../config/targetedLeagues';

// Load environment variables
dotenv.config();

// Test function to check season fixtures for specific leagues
async function testSeasonFixtures() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        // Test with a few major leagues
        const testLeagues = [
            39,  // Premier League
            140, // La Liga
            78,  // Bundesliga
            61,  // Ligue 1
            135  // Serie A
        ];
        
        console.log('Testing season fixtures for major leagues...');
        
        const leaguesCollection = getLeaguesCollection();
        
        for (const leagueId of testLeagues) {
            try {
                console.log(`\n🏆 Testing League ${leagueId}...`);
                
                // Get league info from database
                const leagueDoc = await leaguesCollection.findOne({ _id: leagueId });
                
                if (!leagueDoc) {
                    console.log(`  ❌ League ${leagueId} not found in database`);
                    continue;
                }
                
                const leagueName = leagueDoc.league.name;
                const activeSeasons = leagueDoc.seasons
                    .filter((season: any) => season.current)
                    .map((season: any) => season.year);
                
                console.log(`  📋 ${leagueName}`);
                console.log(`  📅 Active seasons: ${activeSeasons.join(', ')}`);
                
                for (const season of activeSeasons) {
                    console.log(`    🔍 Fetching fixtures for season ${season}...`);
                    
                    const fixtures = await fetchFixtures({
                        league: leagueId,
                        season: season
                    });
                    
                    if (fixtures && fixtures.length > 0) {
                        console.log(`    ✅ Found ${fixtures.length} fixtures`);
                        
                        // Show some sample fixtures
                        const upcomingFixtures = fixtures.filter(f => f.fixture.status.short === 'NS').slice(0, 3);
                        const finishedFixtures = fixtures.filter(f => f.fixture.status.short === 'FT').slice(0, 3);
                        
                        if (upcomingFixtures.length > 0) {
                            console.log(`    📅 Sample upcoming fixtures:`);
                            upcomingFixtures.forEach(f => {
                                const date = new Date(f.fixture.date).toLocaleDateString();
                                console.log(`      ${date}: ${f.teams.home.name} vs ${f.teams.away.name}`);
                            });
                        }
                        
                        if (finishedFixtures.length > 0) {
                            console.log(`    ✅ Sample finished fixtures:`);
                            finishedFixtures.forEach(f => {
                                const date = new Date(f.fixture.date).toLocaleDateString();
                                const score = `${f.goals.home}-${f.goals.away}`;
                                console.log(`      ${date}: ${f.teams.home.name} ${score} ${f.teams.away.name}`);
                            });
                        }
                    } else {
                        console.log(`    ⚠️  No fixtures found for season ${season}`);
                    }
                    
                    // Small delay between requests
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
            } catch (error) {
                console.error(`❌ Error testing league ${leagueId}:`, error);
            }
        }
        
        // Show league distribution for the weekly schedule
        console.log('\n📊 Weekly League Distribution:');
        const leaguesPerDay = Math.ceil(targetedLeagues.length / 7);
        
        for (let day = 0; day < 7; day++) {
            const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const startIndex = day * leaguesPerDay;
            const endIndex = Math.min(startIndex + leaguesPerDay, targetedLeagues.length);
            const count = endIndex - startIndex;
            
            console.log(`  ${dayNames[day]}: ${count} leagues (${startIndex + 1}-${endIndex})`);
        }
        
        console.log(`\n📈 Total targeted leagues: ${targetedLeagues.length}`);
        console.log(`📅 Leagues per day: ~${leaguesPerDay}`);
        console.log(`🔄 Full cycle: 7 days`);
        
        console.log('\nTest completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running test:', error);
        process.exit(1);
    }
}

// Run the test
testSeasonFixtures();
