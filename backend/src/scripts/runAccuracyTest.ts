#!/usr/bin/env ts-node

/**
 * Runner script for Enhanced Predictions Accuracy Test
 * 
 * Usage: npm run test:accuracy
 * Or: ts-node src/scripts/runAccuracyTest.ts
 */

import EnhancedPredictionAccuracyTest from './testEnhancedPredictionsAccuracy';

async function main() {
  console.log('🚀 Starting Enhanced Predictions Accuracy Test...\n');
  
  try {
    await EnhancedPredictionAccuracyTest.runAccuracyTest();
    console.log('\n✅ Test completed successfully!');
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

main();
