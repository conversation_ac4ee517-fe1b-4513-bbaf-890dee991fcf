import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';
import { invalidateFixtureCaches } from '../config/redis';

// Load environment variables
dotenv.config();

// Helper function to create bulk operations (copied from fixtureJobs.ts)
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        const filter = { _id: fixtureApi.fixture.id };
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        fixtureDate.setHours(0, 0, 0, 0);

        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate,
            lastUpdated: now,
        };

        const updateDoc: any = { ...baseUpdateDoc };
        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true,
            },
        });
    }

    return bulkOps;
}

// Main function
async function fetchFIFAClubWorldCupFixtures() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Connecting to Redis...');
        await connectRedis();

        const leagueId = 15; // FIFA Club World Cup
        const season = 2025;

        console.log(`Fetching all fixtures for FIFA Club World Cup (League ID ${leagueId}) for season ${season}...`);

        // Fetch fixtures from API-Football
        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`No fixtures received from API for League ID ${leagueId}, season ${season}.`);
            return;
        }

        console.log(`Received ${allFixturesFromApi.length} fixtures for FIFA Club World Cup, season ${season}.`);

        // Log some sample fixtures to see what rounds we have
        console.log('\nSample fixtures and rounds:');
        allFixturesFromApi.slice(0, 10).forEach((fixture, index) => {
            console.log(`${index + 1}. ${fixture.teams.home.name} vs ${fixture.teams.away.name} - Round: ${fixture.league.round} - Date: ${new Date(fixture.fixture.timestamp * 1000).toISOString()} - Status: ${fixture.fixture.status.long}`);
        });

        // Check for semi-final and final fixtures specifically
        const semiAndFinalFixtures = allFixturesFromApi.filter(fixture => 
            fixture.league.round && (
                fixture.league.round.toLowerCase().includes('semi') ||
                fixture.league.round.toLowerCase().includes('final')
            )
        );

        console.log(`\nFound ${semiAndFinalFixtures.length} semi-final and final fixtures:`);
        semiAndFinalFixtures.forEach((fixture, index) => {
            console.log(`${index + 1}. ${fixture.teams.home.name} vs ${fixture.teams.away.name} - Round: ${fixture.league.round} - Date: ${new Date(fixture.fixture.timestamp * 1000).toISOString()} - Status: ${fixture.fixture.status.long}`);
        });

        // Filter fixtures if needed
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

        if (fixturesFromApi.length < allFixturesFromApi.length) {
            console.log(`Filtered down to ${fixturesFromApi.length} fixtures based on targeted leagues.`);
        }

        // Create bulk operations for MongoDB
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`FIFA Club World Cup fixtures update finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Invalidate cache for updated fixtures
            if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                await invalidateFixtureCaches(fixtureIds);
                console.log(`Cache invalidated for ${fixtureIds.length} fixtures`);
            }
        } else {
            console.log('No fixtures to update.');
        }

        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchFIFAClubWorldCupFixtures();
