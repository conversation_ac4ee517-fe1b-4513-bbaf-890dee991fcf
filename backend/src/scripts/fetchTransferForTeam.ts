import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchTransfers } from '../services/transferService';
import { getTransfersCollection, Transfer, createTransferId } from '../models/Transfer';

// Load environment variables
dotenv.config();

// Main function
async function fetchTransferForTeam() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        const teamId = 33; // Manchester United
        
        console.log(`Fetching transfers for Team ID: ${teamId}`);
        const transfers = await fetchTransfers({ team: teamId });
        
        if (!transfers || transfers.length === 0) {
            console.log(`No transfer information received for Team ${teamId}.`);
            return;
        }
        
        console.log(`Received ${transfers.length} transfer record(s) for Team ${teamId}:`);
        console.log(JSON.stringify(transfers.slice(0, 2), null, 2)); // Show first 2 transfers
        
        // Store in database
        const transfersCollection = getTransfersCollection();
        const now = new Date();
        
        for (const transfer of transfers) {
            const playerId = transfer.player.id;
            const updateDate = transfer.update;
            const transferId = createTransferId(playerId, updateDate);
            
            // Create transfer document
            const transferDoc: Transfer = {
                _id: transferId,
                player: transfer.player,
                update: transfer.update,
                transfers: transfer.transfers,
                lastUpdated: now
            };
            
            const result = await transfersCollection.updateOne(
                { _id: transferId },
                { $set: transferDoc },
                { upsert: true }
            );
            
            console.log(`Transfer ${transferId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchTransferForTeam();
