/**
 * Update Fixture Odds Script
 * 
 * This script fetches and updates odds for a specific fixture.
 * Usage: npx ts-node src/scripts/updateFixtureOdds.ts <fixtureId>
 */

import dotenv from 'dotenv';
import connectDB from '../config/database';
import { getOddsCollection, Odds, createOddsId } from '../models/Odds';
import { fetchOdds } from '../services/oddsService';
import { targetedBetTypeIds } from '../config/targetedBetTypes';
import { getRedisClient } from '../config/redis';

// Load environment variables
dotenv.config();

// Main function
async function updateFixtureOdds() {
    try {
        // Get fixture ID from command line arguments
        const args = process.argv.slice(2);
        if (args.length === 0) {
            console.log('Usage: npx ts-node src/scripts/updateFixtureOdds.ts <fixtureId>');
            process.exit(1);
        }
        
        const fixtureId = parseInt(args[0]);
        if (isNaN(fixtureId)) {
            console.error('Invalid fixture ID. Must be a number.');
            process.exit(1);
        }
        
        console.log(`Updating odds for fixture ID: ${fixtureId}`);
        
        // Connect to database
        console.log('Connecting to database...');
        await connectDB();
        
        // Get Redis client
        const redisClient = getRedisClient();
        
        // Clear Redis cache for this fixture's odds
        console.log('Clearing Redis cache for fixture odds...');
        const keys = await redisClient.keys(`odds:fixture:${fixtureId}*`);
        if (keys.length > 0) {
            await redisClient.del(keys);
            console.log(`Cleared ${keys.length} Redis cache keys for fixture odds.`);
        } else {
            console.log('No Redis cache keys found for fixture odds.');
        }
        
        // Get the odds collection
        const oddsCollection = getOddsCollection();
        
        // Clear existing odds for this fixture
        console.log('Clearing existing odds for this fixture...');
        const deleteResult = await oddsCollection.deleteMany({ fixtureId });
        console.log(`Deleted ${deleteResult.deletedCount} existing odds documents.`);
        
        // Fetch odds from API
        console.log('Fetching odds from API...');
        const oddsFromApi = await fetchOdds({ fixture: fixtureId });
        
        if (!oddsFromApi || oddsFromApi.length === 0) {
            console.log(`No odds received for fixture ${fixtureId}.`);
            process.exit(0);
        }
        
        console.log(`Received odds for fixture ${fixtureId}.`);
        
        // Process each odds response
        const now = new Date();
        let totalOddsUpdated = 0;
        
        for (const oddsResponse of oddsFromApi) {
            const leagueId = oddsResponse.league.id;
            const update = oddsResponse.update;
            const bookmakers = oddsResponse.bookmakers;
            
            // Process each bookmaker
            for (const bookmaker of bookmakers) {
                const bookmakerId = bookmaker.id;
                const bookmakerName = bookmaker.name;
                
                // Filter to only include targeted bet types
                const targetedBets = bookmaker.bets.filter(bet => targetedBetTypeIds.includes(bet.id));
                
                // Process each targeted bet type
                for (const bet of targetedBets) {
                    const betId = bet.id;
                    const betName = bet.name;
                    const oddsId = createOddsId(fixtureId, bookmakerName, betName);
                    
                    // Create a modified bookmaker object with only this bet
                    const filteredBookmaker = {
                        ...bookmaker,
                        bets: [bet]
                    };
                    
                    // Create odds document
                    const oddsDoc: Odds = {
                        _id: oddsId,
                        apiId: oddsId,
                        fixtureId,
                        leagueId,
                        update,
                        bookmakers: [filteredBookmaker],
                        lastUpdated: now
                    };
                    
                    // Update or insert odds
                    await oddsCollection.updateOne(
                        { _id: oddsId },
                        { $set: oddsDoc },
                        { upsert: true }
                    );
                    
                    totalOddsUpdated++;
                }
            }
        }
        
        console.log(`Update completed. Total odds updated: ${totalOddsUpdated}`);
        
    } catch (error) {
        console.error('Error updating fixture odds:', error);
    } finally {
        process.exit(0);
    }
}

// Run the main function
updateFixtureOdds();
