import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { fetchAndUpdateFixturesById } from '../jobs/fixtureJobs';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';

// Load environment variables
dotenv.config();

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create bulk operations (copied from fixtureJobs.ts)
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        // Use fixture ID as the unique identifier (_id) for upsert
        const filter = { _id: fixtureApi.fixture.id };

        // Extract the date from the fixture timestamp for easier querying
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        // Reset hours, minutes, seconds, and milliseconds to get just the date
        fixtureDate.setHours(0, 0, 0, 0);

        // Create an update document with only the fields that are present in the API response
        // This ensures we don't overwrite existing detailed data with undefined values
        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate, // Add the extracted date field for easier querying
            lastUpdated: now,
        };

        // Only include detailed fields if they are present in the API response
        // This prevents overwriting existing data with undefined values
        const updateDoc: any = { ...baseUpdateDoc };

        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true, // Insert if not found, update if found
            },
        });
    }

    return bulkOps;
}

// Main function to fetch fixtures for a single league
async function fetchLeagueFixtures(leagueId: number, season: number) {
    try {
        console.log(`Fetching all fixtures for League ID ${leagueId} for season ${season}...`);

        // Fetch fixtures from API-Football
        const allFixturesFromApi = await fetchFixtures({
            league: leagueId,
            season: season
        });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`No fixtures received from API for League ID ${leagueId}, season ${season}.`);
            return 0;
        }

        console.log(`Received ${allFixturesFromApi.length} fixtures for League ID ${leagueId}, season ${season}.`);

        // Filter fixtures if needed
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

        if (fixturesFromApi.length < allFixturesFromApi.length) {
            console.log(`Filtered down to ${fixturesFromApi.length} fixtures based on targeted leagues.`);
        }

        // Create bulk operations for MongoDB
        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`League ${leagueId} fixtures update finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Find finished fixtures to fetch detailed data for them
            const finishedFixtures = fixturesFromApi.filter(fixture =>
                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
            );

            if (finishedFixtures.length > 0) {
                console.log(`Found ${finishedFixtures.length} finished fixtures. Fetching detailed data...`);
                const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);

                // Fetch detailed data for finished fixtures
                await fetchAndUpdateFixturesById(finishedFixtureIds);
                console.log(`Detailed data fetch completed for ${finishedFixtures.length} finished fixtures.`);
            }

            return finishedFixtures.length;
        } else {
            console.log(`No changes needed for League ${leagueId} fixtures.`);
            return 0;
        }

    } catch (error) {
        console.error(`Error fetching fixtures for League ${leagueId}:`, error);
        return 0;
    }
}

// Function to fetch fixtures for multiple leagues
async function fetchMultipleLeagueFixtures() {
    try {
        console.log('Connecting to database...');
        await connectDB();

        // Define the leagues and seasons to fetch
        // You can customize this list as needed
        const leaguesToFetch = [
            { id: 39, season: 2024 },  // Premier League
            { id: 140, season: 2024 }, // La Liga
            { id: 78, season: 2024 },  // Bundesliga
            { id: 135, season: 2024 }, // Serie A
            { id: 61, season: 2024 }   // Ligue 1
        ];

        let totalFinishedFixtures = 0;

        // Process each league
        for (const { id, season } of leaguesToFetch) {
            console.log(`\n--- Processing League ID ${id}, Season ${season} ---`);

            const finishedFixturesCount = await fetchLeagueFixtures(id, season);
            totalFinishedFixtures += finishedFixturesCount;

            // Add a delay between leagues to respect API rate limits
            console.log('Waiting 3 seconds before processing next league...');
            await delay(3000);
        }

        console.log(`\nAll leagues processed successfully. Total finished fixtures with detailed data: ${totalFinishedFixtures}`);

    } catch (error) {
        console.error('Error fetching multiple league fixtures:', error);
    }
}

// Function to run the script with command line arguments
async function run() {
    // Get command line arguments
    const args = process.argv.slice(2);

    // If arguments are provided, fetch specific leagues
    if (args.length >= 2) {
        const leagueId = parseInt(args[0]);
        const season = parseInt(args[1]);

        if (isNaN(leagueId) || isNaN(season)) {
            console.log('Error: League ID and season must be numbers');
            process.exit(1);
        }

        console.log('Connecting to database...');
        await connectDB();
        await fetchLeagueFixtures(leagueId, season);
    } else {
        // Otherwise, fetch all predefined leagues
        await fetchMultipleLeagueFixtures();
    }

    process.exit(0);
}

// Run the script
run();
