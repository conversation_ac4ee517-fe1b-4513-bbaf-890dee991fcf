import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateTeamSidelined, fetchAndUpdateLeagueSidelined } from '../jobs/sidelinedJobs';

// Load environment variables
dotenv.config();

// Main function
async function runSidelinedJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        // Choose which job to run
        const jobType = process.argv[2] || 'team'; // Default to team sidelined
        
        if (jobType === 'team') {
            console.log('Running fetchAndUpdateTeamSidelined job...');
            await fetchAndUpdateTeamSidelined();
            console.log('Team sidelined job completed successfully');
        } else if (jobType === 'league') {
            console.log('Running fetchAndUpdateLeagueSidelined job...');
            await fetchAndUpdateLeagueSidelined();
            console.log('League sidelined job completed successfully');
        } else {
            console.error('Invalid job type. Use "team" or "league"');
            process.exit(1);
        }
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runSidelinedJob();
