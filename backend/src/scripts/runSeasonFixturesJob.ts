import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateSeasonFixtures } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runSeasonFixturesJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateSeasonFixtures job...');
        await fetchAndUpdateSeasonFixtures();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runSeasonFixturesJob();
