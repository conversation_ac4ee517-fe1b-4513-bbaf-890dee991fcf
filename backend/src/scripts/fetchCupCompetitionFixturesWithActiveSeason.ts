import dotenv from 'dotenv';
import connectDB from '../config/database';
import connectRedis from '../config/redis';
import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { AnyBulkWriteOperation } from 'mongodb';
import { invalidateFixtureCaches } from '../config/redis';

// Load environment variables
dotenv.config();

// Helper function to create bulk operations (copied from fixtureJobs.ts)
function createFixtureBulkOps(fixturesFromApi: any[], now: Date): AnyBulkWriteOperation<Fixture>[] {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        const filter = { _id: fixtureApi.fixture.id };
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        fixtureDate.setHours(0, 0, 0, 0);

        const baseUpdateDoc = {
            apiId: fixtureApi.fixture.id,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate,
            lastUpdated: now,
        };

        const updateDoc: any = { ...baseUpdateDoc };
        if (fixtureApi.events) updateDoc.events = fixtureApi.events;
        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true,
            },
        });
    }

    return bulkOps;
}

// Main function
async function fetchCupCompetitionFixturesWithActiveSeason() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Connecting to Redis...');
        await connectRedis();

        // Define cup competition league IDs
        const cupCompetitionLeagues = [
            15,   // FIFA Club World Cup
            2,    // UEFA Champions League
            3,    // UEFA Europa League
            848,  // UEFA Europa Conference League
            1,    // World Cup
            8,    // World Cup
            4,    // European Championship (UEFA Euro)
            9,    // Copa America (CONMEBOL)
            6,    // Africa Cup of Nations
            7,    // AFC Asian Cup
            17,   // AFC Champions League Elite
            1132, // AFC Champions League Two
            13,   // Copa Libertadores (CONMEBOL)
            11,   // Copa Sudamericana (CONMEBOL)
            12,   // CAF Champions League
            22,   // CONCACAF Gold Cup
            856,  // CONCACAF Champions Cup
            45,   // FA Cup
            48,   // League Cup (Carabao Cup)
            66,   // Coupe de France
            81,   // DFB Pokal
            137,  // Coppa Italia
            143,  // Copa Del Rey
            96,   // Taça De Portugal
        ];

        const collection = getFixturesCollection();
        const leaguesCollection = getLeaguesCollection();
        let totalUpserted = 0;
        let totalModified = 0;

        console.log('Fetching active seasons for cup competitions from database...');

        // Get active seasons for cup competitions from the database
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: cupCompetitionLeagues },
                "seasons.current": true
            },
            { projection: { _id: 1, "league.name": 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        console.log(`Found ${activeLeagues.length} active cup competition leagues in database.`);

        // Create a map of league ID to active seasons
        const leagueSeasonMap = new Map<number, { name: string; seasons: number[] }>();
        for (const league of activeLeagues) {
            const activeSeasons = league.seasons
                .filter((season: any) => season.current)
                .map((season: any) => season.year);
            
            if (activeSeasons.length > 0) {
                leagueSeasonMap.set(league._id, {
                    name: league.league?.name || `League ${league._id}`,
                    seasons: activeSeasons
                });
            }
        }

        // Also include current and next year as fallback for leagues that might not be in our database yet
        const currentYear = new Date().getFullYear();
        const fallbackSeasons = [currentYear, currentYear + 1];

        console.log('\nProcessing cup competitions:');
        for (const leagueId of cupCompetitionLeagues) {
            const leagueInfo = leagueSeasonMap.get(leagueId);
            const seasonsToFetch = leagueInfo?.seasons || fallbackSeasons;
            const leagueName = leagueInfo?.name || `League ${leagueId}`;
            
            console.log(`\n📅 ${leagueName} (ID: ${leagueId})`);
            console.log(`   Seasons to fetch: ${seasonsToFetch.join(', ')}`);
            
            for (const season of seasonsToFetch) {
                try {
                    console.log(`   Fetching fixtures for season ${season}...`);

                    const allFixturesFromApi = await fetchFixtures({
                        league: leagueId,
                        season: season
                    });

                    if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
                        console.log(`     ⚠️  No fixtures found for season ${season}`);
                        continue;
                    }

                    console.log(`     ✅ Found ${allFixturesFromApi.length} fixtures`);

                    // Check for semi-final and final fixtures specifically
                    const semiAndFinalFixtures = allFixturesFromApi.filter(fixture => 
                        fixture.league.round && (
                            fixture.league.round.toLowerCase().includes('semi') ||
                            fixture.league.round.toLowerCase().includes('final')
                        )
                    );

                    if (semiAndFinalFixtures.length > 0) {
                        console.log(`     🏆 Found ${semiAndFinalFixtures.length} semi-final/final fixtures:`);
                        semiAndFinalFixtures.forEach((fixture, index) => {
                            console.log(`        ${index + 1}. ${fixture.teams.home.name} vs ${fixture.teams.away.name} - ${fixture.league.round} - ${new Date(fixture.fixture.timestamp * 1000).toISOString().split('T')[0]}`);
                        });
                    }

                    // Filter fixtures if needed
                    const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

                    // Create bulk operations for MongoDB
                    const now = new Date();
                    const bulkOps = createFixtureBulkOps(fixturesFromApi, now);

                    if (bulkOps.length > 0) {
                        const result = await collection.bulkWrite(bulkOps);
                        console.log(`     💾 Season ${season}: Upserted ${result.upsertedCount}, Modified ${result.modifiedCount}`);
                        
                        totalUpserted += result.upsertedCount;
                        totalModified += result.modifiedCount;

                        // Invalidate cache for updated fixtures
                        if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                            const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                            await invalidateFixtureCaches(fixtureIds);
                        }
                    }

                    // Add delay between API calls to respect rate limits
                    await new Promise(resolve => setTimeout(resolve, 1000));

                } catch (error) {
                    console.error(`     ❌ Error fetching fixtures for season ${season}:`, error);
                    continue;
                }
            }
        }

        console.log(`\n🎉 Job completed successfully!`);
        console.log(`   Total Upserted: ${totalUpserted}`);
        console.log(`   Total Modified: ${totalModified}`);
        
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
fetchCupCompetitionFixturesWithActiveSeason();
