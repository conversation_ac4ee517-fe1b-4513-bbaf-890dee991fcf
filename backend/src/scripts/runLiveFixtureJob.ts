import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateLiveFixtures } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runLiveFixtureJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateLiveFixtures job...');
        await fetchAndUpdateLiveFixtures();
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runLiveFixtureJob();
