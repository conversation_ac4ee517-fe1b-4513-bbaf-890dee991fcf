# Season Data Fetching Scripts

This document explains how to use the comprehensive season data fetching scripts for KickoffScore backend.

## 🚀 Quick Start

### For Complete Season Setup (Recommended for New Seasons)
```bash
# Fetch all data for season 2025 (all targeted leagues)
npm run fetch-complete-season 2025

# Fetch all data for season 2024 (specific leagues only)
npm run fetch-complete-season 2024 39,140,78,135,61
```

### For Fixtures Only (Faster, Less Comprehensive)
```bash
# Fetch only fixtures for season 2025 (all targeted leagues)
npm run fetch-season-fixtures 2025

# Fetch only fixtures for season 2024 (specific leagues only)
npm run fetch-season-fixtures 2024 39,140,78,135,61
```

### For Single League
```bash
# Fetch fixtures for Premier League 2025
npm run fetch-league-fixtures 39 2025
```

## 📋 Available Scripts

### 1. `fetch-complete-season` - Complete Season Data Setup

**What it does:**
- ✅ Updates league seasons and information
- ✅ Updates league coverage data
- ✅ Fetches teams (venues skipped - don't change frequently)
- ✅ Fetches all fixtures (with detailed data for finished matches)
- ✅ Updates standings and fixture rounds
- ✅ Fetches player profiles, statistics, and squads
- ✅ Updates top players (scorers, assists, cards)
- ✅ Fetches coaches and team statistics
- ✅ Updates transfers and injury data
- ❌ Skips odds and predictions (not relevant for historical/future seasons)

**Usage:**
```bash
npm run fetch-complete-season <season> [league1,league2,league3]
```

**Examples:**
```bash
# All targeted leagues for 2025
npm run fetch-complete-season 2025

# Top 5 European leagues for 2024
npm run fetch-complete-season 2024 39,140,78,135,61

# Historical season (2015) for Premier League and La Liga
npm run fetch-complete-season 2015 39,140
```

**Time:** ~20-40 minutes depending on number of leagues (faster without venues/odds)

### 2. `fetch-season-fixtures` - Fixtures Only

**What it does:**
- ✅ Fetches all fixtures for specified season/leagues
- ✅ Includes detailed data for finished matches (events, lineups, statistics)
- ❌ Does not fetch other data (teams, players, standings, etc.)

**Usage:**
```bash
npm run fetch-season-fixtures <season> [league1,league2,league3]
```

**Examples:**
```bash
# All targeted leagues for 2025
npm run fetch-season-fixtures 2025

# Specific leagues for 2024
npm run fetch-season-fixtures 2024 39,140,78
```

**Time:** ~10-20 minutes depending on number of leagues

### 3. `fetch-league-fixtures` - Single League

**What it does:**
- ✅ Fetches fixtures for one specific league and season
- ✅ Includes detailed data for finished matches

**Usage:**
```bash
npm run fetch-league-fixtures <leagueId> <season>
```

**Examples:**
```bash
# Premier League 2025
npm run fetch-league-fixtures 39 2025

# La Liga 2024
npm run fetch-league-fixtures 140 2024
```

**Time:** ~1-3 minutes per league

## 🏆 Common League IDs

| League | ID | Current Season |
|--------|----|----|
| Premier League | 39 | 2024 |
| La Liga | 140 | 2024 |
| Bundesliga | 78 | 2024 |
| Serie A | 135 | 2024 |
| Ligue 1 | 61 | 2024 |
| Champions League | 2 | 2024 |
| Europa League | 3 | 2024 |
| World Cup | 1 | 2022 |

*See `src/config/targetedLeagues.ts` for complete list*

## 📅 Season Transition Workflow

When a new season starts (e.g., 2024/2025 → 2025/2026):

### Step 1: Check Available Seasons
```bash
# Run league seasons job to get latest available seasons
npm run fetch-complete-season 2025 39
```

### Step 2: Full Season Setup (New Season)
```bash
# For major European leagues
npm run fetch-complete-season 2025 39,140,78,135,61

# For all targeted leagues (takes longer)
npm run fetch-complete-season 2025
```

### Step 3: Historical Data (Optional)
```bash
# Fetch historical seasons if needed
npm run fetch-complete-season 2023 39,140
npm run fetch-complete-season 2022 39,140
```

## ⚠️ Important Notes

### API Rate Limits
- Scripts include delays between requests (1-3 seconds)
- Complete season fetch can take 20-40 minutes (optimized - skips venues/odds)
- Monitor API usage to avoid hitting limits

### Season Availability
- Scripts automatically check if a league has data for the specified season
- Leagues without data for that season are skipped
- Check console output for skipped leagues

### Error Handling
- Scripts continue processing other leagues if one fails
- Detailed error logging for troubleshooting
- Safe to re-run scripts (upsert operations)

### Database Impact
- Scripts use upsert operations (safe to re-run)
- Large amounts of data will be inserted/updated
- Ensure sufficient database storage

## 🔧 Troubleshooting

### Script Fails with "Season not found"
```bash
# First update league seasons
npm run fetch-complete-season 2025 39
# Then check if season 2025 is available for league 39
```

### API Rate Limit Errors
- Wait a few minutes and re-run
- Consider processing fewer leagues at once
- Check your API-Football subscription limits

### Database Connection Issues
- Ensure MongoDB is running
- Check database connection string in `.env`
- Verify database permissions

### Memory Issues
- Process leagues in smaller batches
- Use `fetch-season-fixtures` instead of `fetch-complete-season`
- Monitor system resources

## 📊 Expected Data Volumes

### Complete Season (Major League)
- ~380 fixtures per season
- ~500-1000 players
- ~20 teams
- Standings, statistics, transfers, etc.

### All Targeted Leagues
- ~15,000+ fixtures per season
- ~50,000+ players
- ~1,000+ teams
- Complete ecosystem data

## 🎯 Best Practices

1. **Start Small**: Test with 1-2 leagues first
2. **Monitor Progress**: Watch console output for errors
3. **Check Results**: Verify data in database after completion
4. **Regular Updates**: Run during off-peak hours
5. **Backup First**: Backup database before major updates

## 📞 Support

If you encounter issues:
1. Check console output for specific errors
2. Verify API-Football subscription status
3. Ensure database connectivity
4. Review targeted leagues configuration
