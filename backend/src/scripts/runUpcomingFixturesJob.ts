import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchAndUpdateUpcomingFixtures } from '../jobs/fixtureJobs';

// Load environment variables
dotenv.config();

// Main function
async function runUpcomingFixturesJob() {
    try {
        console.log('Connecting to database...');
        await connectDB();
        
        console.log('Running fetchAndUpdateUpcomingFixtures job...');
        await fetchAndUpdateUpcomingFixtures(7); // Fetch fixtures for the next 7 days
        
        console.log('Job completed successfully');
        process.exit(0);
        
    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Run the job
runUpcomingFixturesJob();
