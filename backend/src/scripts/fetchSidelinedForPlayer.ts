import dotenv from 'dotenv';
import connectDB from '../config/database';
import { fetchSidelined } from '../services/sidelinedService';
import { getSidelinedCollection, Sidelined, createSidelinedId } from '../models/Sidelined';

// Load environment variables
dotenv.config();

// Main function
async function fetchSidelinedForPlayer(playerId: number = 276) {
    try {
        console.log('Connecting to database...');
        await connectDB();

        // Player ID is passed as a parameter

        console.log(`Fetching sidelined history for Player ID: ${playerId}`);
        const sidelinedPlayers = await fetchSidelined({ player: playerId });

        if (!sidelinedPlayers || sidelinedPlayers.length === 0) {
            console.log(`No sidelined history received for Player ${playerId}.`);
            return;
        }

        console.log(`Received ${sidelinedPlayers.length} sidelined record(s) for Player ${playerId}:`);
        console.log(JSON.stringify(sidelinedPlayers.slice(0, 2), null, 2)); // Show first 2 sidelined records

        // Store in database
        const sidelinedCollection = getSidelinedCollection();
        const now = new Date();

        for (const sidelined of sidelinedPlayers) {
            const type = sidelined.type || 'Unknown';
            const start = sidelined.start || new Date().toISOString();
            const sidelinedId = createSidelinedId(playerId, type, start);

            // Create sidelined document
            const sidelinedDoc: Sidelined = {
                _id: sidelinedId,
                player: {
                    id: playerId,
                    name: playerId === 276 ? 'Lionel Messi' : (playerId === 19545 ? 'Reece James' : 'Player ' + playerId),
                    photo: playerId === 19545 ? 'https://media.api-sports.io/football/players/19545.png' : null
                },
                team: playerId === 19545 ? { id: 49, name: 'Chelsea', logo: 'https://media.api-sports.io/football/teams/49.png' } : (sidelined.team || { id: 0, name: 'Unknown', logo: null }),
                type: type,
                reason: sidelined.reason || null,
                start: start,
                end: sidelined.end || null,
                lastUpdated: now
            };

            const result = await sidelinedCollection.updateOne(
                { _id: sidelinedId },
                { $set: sidelinedDoc },
                { upsert: true }
            );

            console.log(`Sidelined ${sidelinedId} updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        }

        process.exit(0);

    } catch (error) {
        console.error('Error running job:', error);
        process.exit(1);
    }
}

// Get player ID from command line arguments
const playerId = process.argv[2] ? parseInt(process.argv[2]) : 276;

// Run the job
fetchSidelinedForPlayer(playerId);
