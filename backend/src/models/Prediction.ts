import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /predictions

interface PredictionComparison {
    form?: { home: string | null; away: string | null };
    att?: { home: string | null; away: string | null }; // Attack strength
    def?: { home: string | null; away: string | null }; // Defense strength
    poisson_distribution?: { home: string | null; away: string | null };
    h2h?: { home: string | null; away: string | null };
    goals?: { home: string | null; away: string | null };
    total?: { home: string | null; away: string | null };
}

interface PredictionTeams {
    home: { id: number; name: string; logo: string | null; last_5: any; league: any }; // Define last_5/league more accurately if needed
    away: { id: number; name: string; logo: string | null; last_5: any; league: any };
}

// Main Prediction interface for MongoDB document
export interface Prediction {
    _id: number; // Use fixture ID as the MongoDB _id
    predictions: {
        winner: { id: number | null; name: string | null; comment: string | null };
        win_or_draw: boolean | null;
        under_over: string | null; // e.g., "-2.5", "****" or null
        goals: { home: string | null; away: string | null }; // e.g., "-1.5", "****"
        advice: string | null;
        percent: { home: string | null; draw: string | null; away: string | null }; // Percentage strings e.g., "45%"
    };
    league: { id: number; name: string; country: string; logo: string | null; flag: string | null; season: number };
    teams: PredictionTeams;
    comparison: PredictionComparison;
    h2h?: any[]; // Array of previous H2H fixtures (structure similar to Fixture, simplify if needed)
    lastUpdated: Date; // Our internal update timestamp
}

// Function to get the predictions collection
export function getPredictionsCollection(): Collection<Prediction> {
    const db = getDb();
    return db.collection<Prediction>('predictions');
}

// Optional: Add helper functions for common operations
