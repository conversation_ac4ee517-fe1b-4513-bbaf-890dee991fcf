import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interface matching the data structure from fetchCountries in apiFootball.ts
export interface Country {
    _id?: string; // Optional: MongoDB will add this
    name: string;
    code: string | null;
    flag: string | null;
    // Add any other relevant fields if needed from the API response
    lastUpdated: Date; // Track when the record was last updated
}

// Function to get the countries collection
export function getCountriesCollection(): Collection<Country> {
    const db = getDb();
    return db.collection<Country>('countries');
}

// Optional: Add helper functions for common operations if desired
// e.g., findCountryByName, upsertCountry, etc.
