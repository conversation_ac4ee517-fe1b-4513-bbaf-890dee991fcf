import { Collection } from 'mongodb';
import { getDb } from '../config/database';

/**
 * Interface for tracking when standings were last updated for each league
 */
export interface StandingUpdate {
  _id: string; // League ID + Season
  leagueId: number;
  season: number;
  lastUpdated: Date;
  lastFixtureDate: string; // The date of the last fixture that triggered an update
  lastFixtureId: number; // The ID of the last fixture that triggered an update
}

/**
 * Create a unique ID for a standing update record
 */
export function createStandingUpdateId(leagueId: number, season: number): string {
  return `${leagueId}-${season}`;
}

/**
 * Get the standing updates collection
 */
export function getStandingUpdatesCollection(): Collection<StandingUpdate> {
  const db = getDb();
  return db.collection<StandingUpdate>('standingUpdates');
}

/**
 * Create indexes for the standing updates collection
 */
export async function createStandingUpdatesIndexes(): Promise<void> {
  const collection = getStandingUpdatesCollection();
  
  // Create indexes
  await collection.createIndex({ lastUpdated: 1 });
  await collection.createIndex({ leagueId: 1, season: 1 });
  
  console.log('Standing updates indexes created');
}

/**
 * Check if standings need to be updated for a league (LEGACY - kept for backward compatibility)
 * @param leagueId The league ID
 * @param season The season year
 * @param fixtureDate The date of the fixture that might trigger an update
 * @param fixtureId The ID of the fixture that might trigger an update
 * @param fixtureStatus The status of the fixture
 * @deprecated Use time-based logic in standingJobs.ts instead
 */
export async function shouldUpdateStandings(
  leagueId: number,
  season: number,
  fixtureDate: string,
  fixtureId: number,
  fixtureStatus: string
): Promise<boolean> {
  // If the fixture is not finished, don't update standings
  const finishedStatuses = ['FT', 'AET', 'PEN', 'AWD', 'WO'];
  if (!finishedStatuses.includes(fixtureStatus)) {
    return false;
  }

  const collection = getStandingUpdatesCollection();
  const updateId = createStandingUpdateId(leagueId, season);

  // Get the last update record for this league and season
  const lastUpdate = await collection.findOne({ _id: updateId });

  // If there's no previous update, we should update
  if (!lastUpdate) {
    return true;
  }

  // If this fixture has already triggered an update, don't update again
  if (lastUpdate.lastFixtureId === fixtureId) {
    return false;
  }

  // If the fixture date is after the last update date, update the standings
  if (fixtureDate > lastUpdate.lastFixtureDate) {
    return true;
  }

  // If the fixture date is the same as the last update date but the fixture ID is different,
  // it means there's another fixture on the same day that has completed, so update
  if (fixtureDate === lastUpdate.lastFixtureDate && fixtureId !== lastUpdate.lastFixtureId) {
    return true;
  }

  // Otherwise, don't update
  return false;
}

/**
 * Check if standings need to be updated based on time and fixture activity
 * @param leagueId The league ID
 * @param season The season year
 * @param hasFixturesToday Whether the league has fixtures today
 * @returns Promise<boolean> True if standings should be updated
 */
export async function shouldUpdateStandingsTimeBasedNew(
  leagueId: number,
  season: number,
  hasFixturesToday: boolean = false
): Promise<boolean> {
  const collection = getStandingUpdatesCollection();
  const updateId = createStandingUpdateId(leagueId, season);

  // Get the last update record for this league and season
  const lastUpdate = await collection.findOne({ _id: updateId });

  // If there's no previous update, we should update
  if (!lastUpdate || !lastUpdate.lastUpdated) {
    return true;
  }

  const hoursSinceUpdate = (Date.now() - lastUpdate.lastUpdated.getTime()) / (1000 * 60 * 60);

  // If league has fixtures today, update every hour
  if (hasFixturesToday && hoursSinceUpdate >= 1) {
    return true;
  }

  // Otherwise, update every 6 hours (fallback for leagues without fixtures)
  return hoursSinceUpdate >= 6;
}

/**
 * Record that standings were updated for a league
 */
export async function recordStandingsUpdate(
  leagueId: number,
  season: number,
  fixtureDate: string,
  fixtureId: number | null
): Promise<void> {
  const collection = getStandingUpdatesCollection();
  const updateId = createStandingUpdateId(leagueId, season);

  await collection.updateOne(
    { _id: updateId },
    {
      $set: {
        leagueId,
        season,
        lastUpdated: new Date(),
        lastFixtureDate: fixtureDate,
        lastFixtureId: fixtureId || 0 // Use 0 for time-based updates
      }
    },
    { upsert: true }
  );
}
