import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interface for the document storing the list of rounds for a league/season
export interface FixtureRoundList {
    _id: string; // Composite key: leagueId_seasonYear
    leagueId: number;
    season: number;
    rounds: string[]; // Array of round names (e.g., "Regular Season - 1", "Final")
    // Optional: Store round dates if fetched using 'dates=true' parameter
    // roundDates?: { round: string; start: string; end: string }[];
    lastUpdated: Date;
}

// Function to get the fixtureRounds collection
export function getFixtureRoundsCollection(): Collection<FixtureRoundList> {
    const db = getDb();
    return db.collection<FixtureRoundList>('fixtureRounds');
}

// Helper to create the composite ID
export function createFixtureRoundId(leagueId: number, season: number): string {
    return `${leagueId}_${season}`;
}
