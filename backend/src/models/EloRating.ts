import { Collection } from 'mongodb';
import { getDb } from '../config/database';

/**
 * ELO Rating interface based on ClubElo API data structure
 */
export interface EloRating {
    _id: string; // Use club name as primary key for now
    club: string;
    country: string;
    level: number; // Division level (1 = top division)
    elo: number;
    rank: number | null; // Top 100 get ranks, others are null
    from: string; // Date range start (YYYY-MM-DD)
    to: string; // Date range end (YYYY-MM-DD)
    lastUpdated: Date;
    // Team mapping fields
    apiFootballTeamId?: number; // Mapped API-Football team ID
    mappingConfidence?: number; // 0-1 confidence score for mapping
    alternativeNames?: string[]; // Alternative team names for matching
}

/**
 * Team name mapping for ELO to API-Football team matching
 */
export interface TeamNameMapping {
    _id: string; // Composite key: eloName_apiTeamId
    eloName: string; // Name from ClubElo
    apiTeamId: number; // API-Football team ID
    apiTeamName: string; // API-Football team name
    confidence: number; // Mapping confidence (0-1)
    country: string; // Country for additional validation
    verified: boolean; // Manual verification flag
    createdAt: Date;
    lastUpdated: Date;
}

/**
 * ELO-enhanced team strength for predictions
 */
export interface EloEnhancedStrength {
    teamId: number; // API-Football team ID
    eloRating: number;
    eloRank: number | null;
    relativeStrength: number; // ELO rating normalized (0-2 scale)
    globalPercentile: number; // Percentile rank globally (0-100)
    lastUpdated: Date;
}

// Collection getters
export function getEloRatingsCollection(): Collection<EloRating> {
    const db = getDb();
    return db.collection<EloRating>('eloRatings');
}

export function getTeamNameMappingsCollection(): Collection<TeamNameMapping> {
    const db = getDb();
    return db.collection<TeamNameMapping>('teamNameMappings');
}

export function getEloEnhancedStrengthCollection(): Collection<EloEnhancedStrength> {
    const db = getDb();
    return db.collection<EloEnhancedStrength>('eloEnhancedStrength');
}

/**
 * Helper function to create mapping ID
 */
export function createMappingId(eloName: string, apiTeamId: number): string {
    return `${eloName.toLowerCase().replace(/\s+/g, '_')}_${apiTeamId}`;
}

/**
 * Helper function to normalize team names for matching
 */
export function normalizeTeamName(name: string | null | undefined): string {
    if (!name) return '';
    return name
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .replace(/[^\w\s]/g, '')
        .trim();
}
