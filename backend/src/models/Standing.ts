import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /standings

interface StandingTeamInfo {
    id: number;
    name: string;
    logo: string;
}

interface StandingStats {
    played: number;
    win: number;
    draw: number;
    lose: number; // Note API spelling 'lose'
    points: number;
    goalsDiff: number;
    goalsFor: number; // Extracted from goals object in API response
    goalsAgainst: number; // Extracted from goals object in API response
}

interface TeamStanding {
    rank: number;
    team: StandingTeamInfo;
    points: number;
    goalsDiff: number;
    group: string; // e.g., "Group A", "Premier League"
    form: string | null; // e.g., "WWLDW"
    status: string; // e.g., "same", "up", "down"
    description: string | null; // e.g., "Promotion - Champions League (Group Stage)"
    all: StandingStats;
    home: StandingStats;
    away: StandingStats;
    update: string; // Date string of last update from API
}

// Main Standing interface for MongoDB document
// Represents the standings for ONE league and ONE season
export interface Standing {
    _id: string; // Composite key: leagueId_seasonYear
    league: {
        apiId: number; // Same as leagueId but named to match the database schema
        season: number; // Season year
    };
    leagueId: number; // For backward compatibility
    season: number; // For backward compatibility
    standings: TeamStanding[][]; // Array of groups/tables, each containing an array of team standings
    lastUpdated: Date; // Our internal update timestamp
}

// Function to get the standings collection
export function getStandingsCollection(): Collection<Standing> {
    const db = getDb();
    return db.collection<Standing>('standings');
}

// Helper to create the composite ID
export function createStandingId(leagueId: number, season: number): string {
    return `${leagueId}_${season}`;
}
