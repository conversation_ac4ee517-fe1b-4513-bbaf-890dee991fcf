import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /teams

interface ApiVenue { // Renamed to avoid conflict if a full Venue model is created later
    id: number | null;
    name: string | null;
    address: string | null;
    city: string | null;
    country: string | null;
    capacity: number | null;
    surface: string | null;
    image: string | null;
}

interface TeamInfo {
    id: number; // Use API ID as the primary identifier
    name: string;
    code: string | null; // e.g., MUN, LIV
    country: string | null;
    founded: number | null;
    national: boolean;
    logo: string | null;
}

// League association interface to track which leagues a team is part of
interface LeagueAssociation {
    apiId: number;     // League ID
    name: string;      // League name
    country: string;   // League country
    season: number;    // Season year
}

// Main Team interface for MongoDB document
export interface Team {
    _id: number; // Use the team ID from the API as the MongoDB _id
    apiId: number; // Duplicate of _id to match existing database index
    team: TeamInfo;
    venue: ApiVenue;
    // Add league associations to track which leagues/seasons a team is part of
    leagues?: LeagueAssociation[];
    lastUpdated: Date;
}

// Function to get the teams collection
export function getTeamsCollection(): Collection<Team> {
    const db = getDb();
    return db.collection<Team>('teams');
}

/**
 * Helper function to add a league association to a team
 * @param teamId The team ID
 * @param leagueId The league ID
 * @param season The season year
 * @returns A MongoDB update operation that can be used in updateOne or bulkWrite
 */
export function addLeagueAssociation(teamId: number, leagueId: number, season: number) {
    return {
        updateOne: {
            filter: { _id: teamId },
            update: {
                $addToSet: {
                    leagues: {
                        apiId: leagueId,
                        name: '', // This will be updated when we have the league name
                        country: '', // This will be updated when we have the country
                        season: season
                    }
                }
            }
        }
    };
}
