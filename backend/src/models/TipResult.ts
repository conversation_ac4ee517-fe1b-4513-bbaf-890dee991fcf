import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';
import { TipStatus } from './Tip';

// Interface for user tipster statistics
export interface TipsterStats {
  _id?: ObjectId;
  userId: ObjectId;
  
  // Overall statistics
  totalTips: number;
  wonTips: number;
  lostTips: number;
  voidTips: number;
  pushTips: number;
  
  // Financial statistics
  totalStake: number; // Total amount staked
  totalReturn: number; // Total amount returned (stake + profit)
  netProfit: number; // Total profit/loss
  
  // Performance metrics
  hitRate: number; // Percentage of won tips (won / (won + lost))
  yield: number; // Percentage return on investment ((totalReturn - totalStake) / totalStake * 100)
  averageOdds: number; // Average odds of all tips
  
  // Streak tracking
  currentStreak: number; // Current winning/losing streak (positive = winning, negative = losing)
  longestWinningStreak: number;
  longestLosingStreak: number;
  
  // Time-based statistics
  last30DaysTips: number;
  last30DaysProfit: number;
  last30DaysHitRate: number;
  
  // Category breakdown (optional - can be expanded)
  categoryStats?: {
    [tipType: string]: {
      totalTips: number;
      wonTips: number;
      hitRate: number;
      profit: number;
    };
  };
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  lastTipAt?: Date; // When the user last placed a tip
}

// Interface for individual tip result tracking
export interface TipResult {
  _id?: ObjectId;
  tipId: ObjectId; // Reference to the original tip
  userId: ObjectId; // Reference to user (for faster queries)
  fixtureId: number; // Reference to fixture (for faster queries)
  
  // Tip details (copied for historical tracking)
  tipType: string;
  odds: number;
  stake: number;
  
  // Result details
  status: TipStatus;
  actualResult?: any; // What actually happened in the match
  profit: number; // Actual profit/loss (negative for losses)
  
  // Timestamps
  tipCreatedAt: Date; // When the original tip was created
  resultCalculatedAt: Date; // When the result was calculated
  createdAt: Date;
}

// Interface for leaderboard/ranking display
export interface TipsterRanking {
  userId: ObjectId;
  userName?: string;
  username?: string; // URL-friendly username
  profileImage?: string;
  rank: number;
  
  // Key metrics for ranking
  netProfit: number;
  hitRate: number;
  yield: number;
  totalTips: number;
  averageOdds: number;
  
  // Recent performance
  last30DaysProfit: number;
  last30DaysHitRate: number;
  currentStreak: number;
}

// Function to get the tipster stats collection
export function getTipsterStatsCollection(): Collection<TipsterStats> {
  return getDb().collection<TipsterStats>('tipsterStats');
}

// Function to get the tip results collection
export function getTipResultsCollection(): Collection<TipResult> {
  return getDb().collection<TipResult>('tipResults');
}

// Create or update tipster stats for a user
export async function createOrUpdateTipsterStats(userId: string): Promise<TipsterStats> {
  const collection = getTipsterStatsCollection();
  const now = new Date();

  // Check if stats already exist
  const existingStats = await collection.findOne({ userId: new ObjectId(userId) });

  if (existingStats) {
    return existingStats;
  }

  // Create new stats
  const newStats: TipsterStats = {
    userId: new ObjectId(userId),
    totalTips: 0,
    wonTips: 0,
    lostTips: 0,
    voidTips: 0,
    pushTips: 0,
    totalStake: 0,
    totalReturn: 0,
    netProfit: 0,
    hitRate: 0,
    yield: 0,
    averageOdds: 0,
    currentStreak: 0,
    longestWinningStreak: 0,
    longestLosingStreak: 0,
    last30DaysTips: 0,
    last30DaysProfit: 0,
    last30DaysHitRate: 0,
    createdAt: now,
    updatedAt: now
  };

  const result = await collection.insertOne(newStats);
  return { ...newStats, _id: result.insertedId };
}

// Record a tip result and update user statistics
export async function recordTipResult(
  tipId: string,
  userId: string,
  fixtureId: number,
  tipType: string,
  odds: number,
  stake: number,
  status: TipStatus,
  actualResult?: any,
  tipCreatedAt?: Date
): Promise<void> {
  const tipResultsCollection = getTipResultsCollection();
  const tipsterStatsCollection = getTipsterStatsCollection();
  const now = new Date();

  // Calculate profit based on status
  let profit = 0;
  if (status === TipStatus.WON) {
    profit = (odds - 1) * stake; // Profit = (odds - 1) * stake
  } else if (status === TipStatus.LOST) {
    profit = -stake; // Loss = -stake
  } else if (status === TipStatus.PUSH || status === TipStatus.VOID) {
    profit = 0; // No profit or loss
  }

  // Create tip result record
  const tipResult: TipResult = {
    tipId: new ObjectId(tipId),
    userId: new ObjectId(userId),
    fixtureId,
    tipType,
    odds,
    stake,
    status,
    actualResult,
    profit,
    tipCreatedAt: tipCreatedAt || now,
    resultCalculatedAt: now,
    createdAt: now
  };

  await tipResultsCollection.insertOne(tipResult);

  // Update user statistics
  await updateTipsterStatsAfterResult(userId, status, odds, stake, profit);
}

// Update tipster statistics after a tip result
async function updateTipsterStatsAfterResult(
  userId: string,
  status: TipStatus,
  odds: number,
  stake: number,
  profit: number
): Promise<void> {
  const collection = getTipsterStatsCollection();
  const userObjectId = new ObjectId(userId);
  const now = new Date();

  // Get current stats or create if doesn't exist
  let stats = await collection.findOne({ userId: userObjectId });
  if (!stats) {
    await createOrUpdateTipsterStats(userId);
    stats = await collection.findOne({ userId: userObjectId });
    if (!stats) return; // Safety check
  }

  // Calculate updates
  const updates: any = {
    $inc: {
      totalTips: 1,
      totalStake: stake,
      netProfit: profit
    },
    $set: {
      updatedAt: now,
      lastTipAt: now
    }
  };

  // Update specific counters based on status
  if (status === TipStatus.WON) {
    updates.$inc.wonTips = 1;
    updates.$inc.totalReturn = stake + profit; // Return stake + profit
  } else if (status === TipStatus.LOST) {
    updates.$inc.lostTips = 1;
  } else if (status === TipStatus.VOID) {
    updates.$inc.voidTips = 1;
    updates.$inc.totalReturn = stake; // Return just the stake
  } else if (status === TipStatus.PUSH) {
    updates.$inc.pushTips = 1;
    updates.$inc.totalReturn = stake; // Return just the stake
  }

  await collection.updateOne({ userId: userObjectId }, updates);

  // Recalculate derived statistics
  await recalculateTipsterStats(userId);
}

// Recalculate all derived statistics for a user
export async function recalculateTipsterStats(userId: string): Promise<void> {
  const collection = getTipsterStatsCollection();
  const tipResultsCollection = getTipResultsCollection();
  const userObjectId = new ObjectId(userId);
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

  // Get all tip results for the user
  const tipResults = await tipResultsCollection
    .find({ userId: userObjectId })
    .sort({ resultCalculatedAt: 1 })
    .toArray();

  if (tipResults.length === 0) {
    return;
  }

  // Calculate basic stats
  const totalTips = tipResults.length;
  const wonTips = tipResults.filter(r => r.status === TipStatus.WON).length;
  const lostTips = tipResults.filter(r => r.status === TipStatus.LOST).length;
  const voidTips = tipResults.filter(r => r.status === TipStatus.VOID).length;
  const pushTips = tipResults.filter(r => r.status === TipStatus.PUSH).length;

  const totalStake = tipResults.reduce((sum, r) => sum + r.stake, 0);
  const totalReturn = tipResults.reduce((sum, r) => {
    if (r.status === TipStatus.WON) return sum + r.stake + r.profit;
    if (r.status === TipStatus.VOID || r.status === TipStatus.PUSH) return sum + r.stake;
    return sum;
  }, 0);
  const netProfit = tipResults.reduce((sum, r) => sum + r.profit, 0);

  // Calculate performance metrics
  const decidedTips = wonTips + lostTips; // Exclude void and push
  const hitRate = decidedTips > 0 ? (wonTips / decidedTips) * 100 : 0;
  const yieldPercentage = totalStake > 0 ? ((totalReturn - totalStake) / totalStake) * 100 : 0;
  const averageOdds = tipResults.reduce((sum, r) => sum + r.odds, 0) / totalTips;

  // Calculate streaks
  let currentStreak = 0;
  let longestWinningStreak = 0;
  let longestLosingStreak = 0;
  let tempWinStreak = 0;
  let tempLoseStreak = 0;

  for (let i = tipResults.length - 1; i >= 0; i--) {
    const result = tipResults[i];
    if (result.status === TipStatus.WON) {
      if (currentStreak <= 0) currentStreak = 1;
      else currentStreak++;
      tempWinStreak++;
      tempLoseStreak = 0;
    } else if (result.status === TipStatus.LOST) {
      if (currentStreak >= 0) currentStreak = -1;
      else currentStreak--;
      tempLoseStreak++;
      tempWinStreak = 0;
    }
    
    longestWinningStreak = Math.max(longestWinningStreak, tempWinStreak);
    longestLosingStreak = Math.max(longestLosingStreak, tempLoseStreak);
  }

  // Calculate last 30 days stats
  const recent30DayResults = tipResults.filter(r => r.resultCalculatedAt >= thirtyDaysAgo);
  const last30DaysTips = recent30DayResults.length;
  const last30DaysProfit = recent30DayResults.reduce((sum, r) => sum + r.profit, 0);
  const last30DaysWon = recent30DayResults.filter(r => r.status === TipStatus.WON).length;
  const last30DaysLost = recent30DayResults.filter(r => r.status === TipStatus.LOST).length;
  const last30DaysDecided = last30DaysWon + last30DaysLost;
  const last30DaysHitRate = last30DaysDecided > 0 ? (last30DaysWon / last30DaysDecided) * 100 : 0;

  // Update the stats
  await collection.updateOne(
    { userId: userObjectId },
    {
      $set: {
        totalTips,
        wonTips,
        lostTips,
        voidTips,
        pushTips,
        totalStake,
        totalReturn,
        netProfit,
        hitRate,
        yield: yieldPercentage,
        averageOdds,
        currentStreak,
        longestWinningStreak,
        longestLosingStreak,
        last30DaysTips,
        last30DaysProfit,
        last30DaysHitRate,
        updatedAt: now
      }
    }
  );
}

// Get tipster rankings
export async function getTipsterRankings(limit: number = 50, sortBy: 'profit' | 'hitRate' | 'yield' = 'profit'): Promise<TipsterRanking[]> {
  const collection = getTipsterStatsCollection();
  
  // Define sort criteria
  let sortCriteria: any = {};
  switch (sortBy) {
    case 'profit':
      sortCriteria = { netProfit: -1 };
      break;
    case 'hitRate':
      sortCriteria = { hitRate: -1, totalTips: -1 }; // Secondary sort by total tips
      break;
    case 'yield':
      sortCriteria = { yield: -1, totalTips: -1 }; // Secondary sort by total tips
      break;
  }

  const pipeline = [
    {
      $match: {
        totalTips: { $gte: 5 } // Minimum 5 tips to be ranked
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'userId',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $project: {
        userId: 1,
        userName: '$user.name',
        username: '$user.username',
        profileImage: '$user.profileImage',
        netProfit: 1,
        hitRate: 1,
        yield: 1,
        totalTips: 1,
        averageOdds: 1,
        last30DaysProfit: 1,
        last30DaysHitRate: 1,
        currentStreak: 1
      }
    },
    {
      $sort: sortCriteria
    },
    {
      $limit: limit
    }
  ];

  const results = await collection.aggregate<Omit<TipsterRanking, 'rank'>>(pipeline).toArray();
  
  // Add rank to each result
  return results.map((result, index) => ({
    ...result,
    rank: index + 1
  }));
}

// Get user's tipster stats
export async function getUserTipsterStats(userId: string): Promise<TipsterStats | null> {
  const collection = getTipsterStatsCollection();
  return collection.findOne({ userId: new ObjectId(userId) });
}

// Get user's recent tip results
export async function getUserRecentTipResults(userId: string, limit: number = 20): Promise<TipResult[]> {
  const collection = getTipResultsCollection();
  return collection
    .find({ userId: new ObjectId(userId) })
    .sort({ resultCalculatedAt: -1 })
    .limit(limit)
    .toArray();
}
