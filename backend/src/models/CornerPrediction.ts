/**
 * Corner Prediction Models
 * 
 * Data structures for storing corner predictions using Poisson distribution
 * Focuses on Total Corners Over/Under markets only
 */

export interface CornerStatistics {
  fixtureId: number;
  homeCorners: number;
  awayCorners: number;
  totalCorners: number;
  homeTeamId: number;
  awayTeamId: number;
  leagueId: number;
  date: Date;
}

export interface TeamCornerStrength {
  teamId: number;
  leagueId: number;
  cornerAttack: number;      // Average corners won per game
  cornerDefense: number;     // Average corners conceded per game
  homeCornerBonus: number;   // Additional corner rate when playing at home (multiplier)
  matchCount: number;        // Number of matches used for calculation
  confidence: number;        // 0-1 confidence score based on data quality
  lastUpdated: Date;
}

export interface LeagueCornerParameters {
  leagueId: number;
  averageCorners: number;    // League average corners per match
  homeAdvantage: number;     // Home corner advantage multiplier (typically 1.15-1.25)
  varianceAdjustment: number; // League-specific variance adjustment
  matchesAnalyzed: number;   // Number of matches used for calculation
  hasCornerSupport: boolean; // Whether league has corner statistics available
  lastUpdated: Date;
}

export interface CornerMarketPrediction {
  threshold: number;         // e.g., 8.5, 9.5, 10.5, 11.5, 12.5
  overProbability: number;   // Probability of over threshold
  underProbability: number;  // Probability of under threshold
  value: number;            // Value rating (-100 to +100, positive = good value)
  confidence: 'low' | 'medium' | 'high';
}

export interface CornerPrediction {
  expectedTotal: number;     // Expected total corners in the match
  homeExpected: number;      // Expected corners for home team
  awayExpected: number;      // Expected corners for away team
  confidence: number;        // Overall confidence score (0-100)
  
  // Total Corners Over/Under markets
  markets: {
    over8_5: CornerMarketPrediction;
    over9_5: CornerMarketPrediction;
    over10_5: CornerMarketPrediction;
    over11_5: CornerMarketPrediction;
    over12_5: CornerMarketPrediction;
  };
  
  // Corner distribution (probability for each total corner count)
  distribution: Array<{
    corners: number;
    probability: number;
  }>;
  
  // Metadata
  algorithm: 'poisson';
  dataSource: 'historical' | 'league_average';
  modelVersion: string;
  calculatedAt: Date;
}

/**
 * Team Corner Strength Document
 * Stored in 'team_corner_strengths' collection
 */
export interface TeamCornerStrengthDocument {
  _id: string; // Format: "{teamId}_{leagueId}"
  teamId: number;
  leagueId: number;
  
  strength: {
    cornerAttack: number;
    cornerDefense: number;
    homeCornerBonus: number;
  };
  
  statistics: {
    matchesPlayed: number;
    cornersWon: number;
    cornersConceded: number;
    homeMatchesPlayed: number;
    awayMatchesPlayed: number;
    averageCornersHome: number;
    averageCornersAway: number;
  };
  
  form: {
    last5Matches: Array<{
      fixtureId: number;
      date: Date;
      opponent: number;
      homeAway: 'home' | 'away';
      cornersFor: number;
      cornersAgainst: number;
    }>;
    recentCornerForm: number; // Recent corner performance trend
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * League Corner Parameters Document
 * Stored in 'league_corner_parameters' collection
 */
export interface LeagueCornerParametersDocument {
  _id: number; // league ID
  leagueId: number;
  
  parameters: {
    averageCorners: number;
    homeAdvantage: number;
    varianceAdjustment: number;
  };
  
  statistics: {
    matchesAnalyzed: number;
    dateRange: {
      from: Date;
      to: Date;
    };
    totalCorners: number;
    averageHomeCorners: number;
    averageAwayCorners: number;
    cornerDistribution: { [corners: string]: number }; // Distribution of corner counts
  };
  
  coverage: {
    hasCornerSupport: boolean;
    dataQuality: 'low' | 'medium' | 'high';
    lastDataUpdate: Date;
  };

  lastUpdated: Date;
  calculatedAt: Date;
}

/**
 * Corner Prediction Accuracy Tracking
 * Stored in 'corner_prediction_accuracy' collection
 */
export interface CornerPredictionAccuracyDocument {
  _id: string; // Format: "{fixtureId}_corner"
  fixtureId: number;
  
  predictions: {
    expectedTotal: number;
    actualTotal: number;
    markets: {
      [market: string]: {
        predicted: number;
        actual: boolean;
        correct: boolean;
      };
    };
  };
  
  accuracy: {
    totalCornersError: number; // Absolute error in total corners
    marketAccuracy: number;    // Percentage of correct market predictions
    confidenceCalibration: number; // How well confidence matched actual accuracy
  };

  metadata: {
    predictionDate: Date;
    matchDate: Date;
    confidence: number;
    leagueId: number;
  };

  createdAt: Date;
}

/**
 * Utility functions for corner prediction calculations
 */
export class CornerPredictionUtils {
  /**
   * Calculate Poisson probability mass function
   */
  static poissonPMF(lambda: number, k: number): number {
    if (k < 0) return 0;
    return (Math.pow(lambda, k) * Math.exp(-lambda)) / this.factorial(k);
  }
  
  /**
   * Calculate Poisson cumulative distribution function
   */
  static poissonCDF(lambda: number, k: number): number {
    let sum = 0;
    for (let i = 0; i <= k; i++) {
      sum += this.poissonPMF(lambda, i);
    }
    return sum;
  }
  
  /**
   * Calculate factorial
   */
  static factorial(n: number): number {
    if (n <= 1) return 1;
    return n * this.factorial(n - 1);
  }
  
  /**
   * Calculate over probability for a given threshold
   */
  static calculateOverProbability(lambda: number, threshold: number): number {
    return 1 - this.poissonCDF(lambda, Math.floor(threshold));
  }
  
  /**
   * Calculate confidence level based on data quality
   */
  static calculateConfidence(
    matchCount: number,
    dataQuality: number = 1.0,
    minMatches: number = 10
  ): 'low' | 'medium' | 'high' {
    const adjustedCount = matchCount * dataQuality;
    
    if (adjustedCount >= minMatches * 2) return 'high';
    if (adjustedCount >= minMatches) return 'medium';
    return 'low';
  }
  
  /**
   * Calculate value rating for a market
   */
  static calculateValueRating(
    algorithmProbability: number,
    marketProbability?: number
  ): number {
    if (!marketProbability || marketProbability === 0) return 0;
    return ((algorithmProbability - marketProbability) / marketProbability) * 100;
  }
}
