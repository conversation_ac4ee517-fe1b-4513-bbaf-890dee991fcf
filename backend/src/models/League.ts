import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Interfaces based on API response structure for /leagues

interface ApiCountry { // Renamed to avoid conflict with Country model
    name: string;
    code: string | null;
    flag: string | null;
}

interface Coverage {
    fixtures: {
        events: boolean;
        lineups: boolean;
        statistics_fixtures: boolean;
        statistics_players: boolean;
    };
    standings: boolean;
    players: boolean;
    top_scorers: boolean;
    top_assists: boolean;
    top_cards: boolean;
    injuries: boolean;
    predictions: boolean;
    odds: boolean;
}

interface Season {
    year: number;
    start: string;
    end: string;
    current: boolean;
    coverage: Coverage;
}

interface LeagueInfo {
    id: number; // Use API ID as the primary identifier for leagues
    name: string;
    type: string; // "League" or "Cup"
    logo: string;
}

// Main League interface for MongoDB document
export interface League {
    _id: number; // Use the league ID from the API as the MongoDB _id
    apiId: number; // Duplicate of _id to match existing database index
    league: LeagueInfo;
    country: ApiCountry;
    seasons: Season[];
    lastUpdated: Date;
}

// Function to get the leagues collection
export function getLeaguesCollection(): Collection<League> {
    const db = getDb();
    return db.collection<League>('leagues');
}

// Optional: Add helper functions for common operations
// e.g., findLeagueById, findLeaguesByCountry, etc.
