import { Collection, UpdateResult } from 'mongodb';
import { getDb } from '../config/database';

// Interface for the document storing the list of seasons
export interface LeagueSeasonList {
    _id: string; // Use a fixed ID like 'all_seasons'
    seasons: number[];
    lastUpdated: Date;
}

const SEASONS_DOC_ID = 'all_seasons';

// Function to get the leagueSeasons collection
export function getLeagueSeasonsCollection(): Collection<LeagueSeasonList> {
    const db = getDb();
    // Using a dedicated collection, although storing in a general 'metadata' collection is also an option
    return db.collection<LeagueSeasonList>('leagueSeasons');
}

// Helper function to update the single document containing the season list
export async function updateLeagueSeasons(seasons: number[]): Promise<UpdateResult> {
    const collection = getLeagueSeasonsCollection();
    const now = new Date();
    return collection.updateOne(
        { _id: SEASONS_DOC_ID },
        { $set: { seasons: seasons, lastUpdated: now } },
        { upsert: true } // Create the document if it doesn't exist
    );
}

// Helper function to get the list of seasons
export async function getLeagueSeasons(): Promise<number[]> {
    const collection = getLeagueSeasonsCollection();
    const doc = await collection.findOne({ _id: SEASONS_DOC_ID });
    return doc?.seasons || []; // Return the seasons array or an empty array if not found
}
