import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import crypto from 'crypto';
import { Request } from 'express';

// Extend dayjs with timezone support
dayjs.extend(utc);
dayjs.extend(timezone);

export enum VoteCategory {
  MATCH_OUTCOME = 'match_outcome',
  OVER_UNDER = 'over_under',
  BTTS = 'btts'
}

export enum VoteOption {
  // Match Outcome options
  HOME_WIN = 'home',
  DRAW = 'draw',
  AWAY_WIN = 'away',

  // Over/Under options
  OVER_25 = 'over_2_5',
  UNDER_25 = 'under_2_5',

  // Both Teams to Score options
  BTTS_YES = 'btts_yes',
  BTTS_NO = 'btts_no'
}

export interface FixtureVote {
  _id?: ObjectId;
  userId?: ObjectId; // Optional for guest votes
  guestIdentifier?: string; // For guest votes - hash of IP + user agent
  fixtureId: number;
  category: VoteCategory; // Vote category (match_outcome, over_under, btts)
  vote: VoteOption;
  createdAt: Date;
  updatedAt: Date;
  isGuestVote: boolean; // Flag to identify guest vs user votes
}

export function getFixtureVotesCollection(): Collection<FixtureVote> {
  return getDb().collection<FixtureVote>('fixtureVotes');
}

/**
 * Validate that a vote option is valid for the given category
 * @param category Vote category
 * @param vote Vote option
 * @returns True if valid, false otherwise
 */
export function isValidVoteForCategory(category: VoteCategory, vote: VoteOption): boolean {
  switch (category) {
    case VoteCategory.MATCH_OUTCOME:
      return [VoteOption.HOME_WIN, VoteOption.DRAW, VoteOption.AWAY_WIN].includes(vote);
    case VoteCategory.OVER_UNDER:
      return [VoteOption.OVER_25, VoteOption.UNDER_25].includes(vote);
    case VoteCategory.BTTS:
      return [VoteOption.BTTS_YES, VoteOption.BTTS_NO].includes(vote);
    default:
      return false;
  }
}

/**
 * Get valid vote options for a category
 * @param category Vote category
 * @returns Array of valid vote options
 */
export function getValidVotesForCategory(category: VoteCategory): VoteOption[] {
  switch (category) {
    case VoteCategory.MATCH_OUTCOME:
      return [VoteOption.HOME_WIN, VoteOption.DRAW, VoteOption.AWAY_WIN];
    case VoteCategory.OVER_UNDER:
      return [VoteOption.OVER_25, VoteOption.UNDER_25];
    case VoteCategory.BTTS:
      return [VoteOption.BTTS_YES, VoteOption.BTTS_NO];
    default:
      return [];
  }
}

/**
 * Generate a unique identifier for guest users based on IP address and user agent
 * @param req Express request object
 * @returns Hashed identifier string
 */
export function generateGuestIdentifier(req: Request): string {
  // Get IP address (handle various proxy scenarios)
  const ip = req.ip ||
            req.connection.remoteAddress ||
            req.socket.remoteAddress ||
            (req.connection as any)?.socket?.remoteAddress ||
            'unknown';

  // Get user agent
  const userAgent = req.get('User-Agent') || 'unknown';

  // Create a hash of IP + user agent for privacy and uniqueness
  const identifier = `${ip}:${userAgent}`;
  return crypto.createHash('sha256').update(identifier).digest('hex');
}

// Create or update a vote for authenticated users
export async function createOrUpdateVote(
  userId: string,
  fixtureId: number,
  category: VoteCategory,
  vote: VoteOption
): Promise<FixtureVote> {
  // Validate vote option for category
  if (!isValidVoteForCategory(category, vote)) {
    throw new Error(`Invalid vote option '${vote}' for category '${category}'`);
  }

  const collection = getFixtureVotesCollection();
  const now = new Date();

  // Check if user has already voted for this fixture and category
  const existingVote = await collection.findOne({
    userId: new ObjectId(userId),
    fixtureId,
    category,
    isGuestVote: false
  });

  if (existingVote) {
    // Update existing vote
    await collection.updateOne(
      { _id: existingVote._id },
      {
        $set: {
          vote,
          updatedAt: now
        }
      }
    );

    return {
      ...existingVote,
      vote,
      updatedAt: now
    };
  } else {
    // Create new vote
    const newVote: FixtureVote = {
      userId: new ObjectId(userId),
      fixtureId,
      category,
      vote,
      createdAt: now,
      updatedAt: now,
      isGuestVote: false
    };

    const result = await collection.insertOne(newVote);
    return { ...newVote, _id: result.insertedId };
  }
}

// Create or update a vote for guest users
export async function createOrUpdateGuestVote(
  guestIdentifier: string,
  fixtureId: number,
  category: VoteCategory,
  vote: VoteOption
): Promise<FixtureVote> {
  // Validate vote option for category
  if (!isValidVoteForCategory(category, vote)) {
    throw new Error(`Invalid vote option '${vote}' for category '${category}'`);
  }

  const collection = getFixtureVotesCollection();
  const now = new Date();

  // Check if guest has already voted for this fixture and category
  const existingVote = await collection.findOne({
    guestIdentifier,
    fixtureId,
    category,
    isGuestVote: true
  });

  if (existingVote) {
    // Update existing vote
    await collection.updateOne(
      { _id: existingVote._id },
      {
        $set: {
          vote,
          updatedAt: now
        }
      }
    );

    return {
      ...existingVote,
      vote,
      updatedAt: now
    };
  } else {
    // Create new vote
    const newVote: FixtureVote = {
      guestIdentifier,
      fixtureId,
      category,
      vote,
      createdAt: now,
      updatedAt: now,
      isGuestVote: true
    };

    const result = await collection.insertOne(newVote);
    return { ...newVote, _id: result.insertedId };
  }
}

// Get a user's vote for a fixture and category (authenticated users only)
export async function getUserVote(
  userId: string,
  fixtureId: number,
  category: VoteCategory
): Promise<FixtureVote | null> {
  const collection = getFixtureVotesCollection();
  return collection.findOne({
    userId: new ObjectId(userId),
    fixtureId,
    category,
    isGuestVote: false
  });
}

// Get all user's votes for a fixture (all categories)
export async function getUserVotesForFixture(
  userId: string,
  fixtureId: number
): Promise<{ [key in VoteCategory]?: FixtureVote }> {
  const collection = getFixtureVotesCollection();
  const votes = await collection.find({
    userId: new ObjectId(userId),
    fixtureId,
    isGuestVote: false
  }).toArray();

  const result: { [key in VoteCategory]?: FixtureVote } = {};
  votes.forEach(vote => {
    result[vote.category] = vote;
  });

  return result;
}

// Get vote statistics for a fixture (all categories)
export async function getFixtureVoteStats(fixtureId: number): Promise<{
  match_outcome: {
    homeVotes: number;
    drawVotes: number;
    awayVotes: number;
    totalVotes: number;
  };
  over_under: {
    over25Votes: number;
    under25Votes: number;
    totalVotes: number;
  };
  btts: {
    yesVotes: number;
    noVotes: number;
    totalVotes: number;
  };
}> {
  const collection = getFixtureVotesCollection();

  const stats = await collection.aggregate([
    { $match: { fixtureId } },
    { $group: {
        _id: { category: '$category', vote: '$vote' },
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  // Initialize result structure
  const result = {
    match_outcome: {
      homeVotes: 0,
      drawVotes: 0,
      awayVotes: 0,
      totalVotes: 0
    },
    over_under: {
      over25Votes: 0,
      under25Votes: 0,
      totalVotes: 0
    },
    btts: {
      yesVotes: 0,
      noVotes: 0,
      totalVotes: 0
    }
  };

  // Process aggregation results
  stats.forEach(stat => {
    const category = stat._id.category;
    const vote = stat._id.vote;
    const count = stat.count;

    switch (category) {
      case VoteCategory.MATCH_OUTCOME:
        if (vote === VoteOption.HOME_WIN) result.match_outcome.homeVotes = count;
        else if (vote === VoteOption.DRAW) result.match_outcome.drawVotes = count;
        else if (vote === VoteOption.AWAY_WIN) result.match_outcome.awayVotes = count;
        break;
      case VoteCategory.OVER_UNDER:
        if (vote === VoteOption.OVER_25) result.over_under.over25Votes = count;
        else if (vote === VoteOption.UNDER_25) result.over_under.under25Votes = count;
        break;
      case VoteCategory.BTTS:
        if (vote === VoteOption.BTTS_YES) result.btts.yesVotes = count;
        else if (vote === VoteOption.BTTS_NO) result.btts.noVotes = count;
        break;
    }
  });

  // Calculate totals
  result.match_outcome.totalVotes = result.match_outcome.homeVotes + result.match_outcome.drawVotes + result.match_outcome.awayVotes;
  result.over_under.totalVotes = result.over_under.over25Votes + result.over_under.under25Votes;
  result.btts.totalVotes = result.btts.yesVotes + result.btts.noVotes;

  return result;
}

// Get vote statistics for a specific category
export async function getFixtureVoteStatsByCategory(
  fixtureId: number,
  category: VoteCategory
): Promise<any> {
  const collection = getFixtureVotesCollection();

  const stats = await collection.aggregate([
    { $match: { fixtureId, category } },
    { $group: {
        _id: '$vote',
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  switch (category) {
    case VoteCategory.MATCH_OUTCOME:
      const homeVotes = stats.find(s => s._id === VoteOption.HOME_WIN)?.count || 0;
      const drawVotes = stats.find(s => s._id === VoteOption.DRAW)?.count || 0;
      const awayVotes = stats.find(s => s._id === VoteOption.AWAY_WIN)?.count || 0;
      return {
        homeVotes,
        drawVotes,
        awayVotes,
        totalVotes: homeVotes + drawVotes + awayVotes
      };

    case VoteCategory.OVER_UNDER:
      const over25Votes = stats.find(s => s._id === VoteOption.OVER_25)?.count || 0;
      const under25Votes = stats.find(s => s._id === VoteOption.UNDER_25)?.count || 0;
      return {
        over25Votes,
        under25Votes,
        totalVotes: over25Votes + under25Votes
      };

    case VoteCategory.BTTS:
      const yesVotes = stats.find(s => s._id === VoteOption.BTTS_YES)?.count || 0;
      const noVotes = stats.find(s => s._id === VoteOption.BTTS_NO)?.count || 0;
      return {
        yesVotes,
        noVotes,
        totalVotes: yesVotes + noVotes
      };

    default:
      return { totalVotes: 0 };
  }
}

// Get vote statistics for multiple fixtures (all categories)
export async function getMultipleFixtureVoteStats(fixtureIds: number[]): Promise<{
  [fixtureId: number]: {
    match_outcome: {
      homeVotes: number;
      drawVotes: number;
      awayVotes: number;
      totalVotes: number;
    };
    over_under: {
      over25Votes: number;
      under25Votes: number;
      totalVotes: number;
    };
    btts: {
      yesVotes: number;
      noVotes: number;
      totalVotes: number;
    };
  }
}> {
  if (!fixtureIds.length) {
    return {};
  }

  const collection = getFixtureVotesCollection();

  const stats = await collection.aggregate([
    { $match: { fixtureId: { $in: fixtureIds } } },
    { $group: {
        _id: { fixtureId: '$fixtureId', category: '$category', vote: '$vote' },
        count: { $sum: 1 }
      }
    }
  ]).toArray();

  const result: {
    [fixtureId: number]: {
      match_outcome: {
        homeVotes: number;
        drawVotes: number;
        awayVotes: number;
        totalVotes: number;
      };
      over_under: {
        over25Votes: number;
        under25Votes: number;
        totalVotes: number;
      };
      btts: {
        yesVotes: number;
        noVotes: number;
        totalVotes: number;
      };
    }
  } = {};

  // Initialize result with zeros for all fixture IDs
  fixtureIds.forEach(fixtureId => {
    result[fixtureId] = {
      match_outcome: {
        homeVotes: 0,
        drawVotes: 0,
        awayVotes: 0,
        totalVotes: 0
      },
      over_under: {
        over25Votes: 0,
        under25Votes: 0,
        totalVotes: 0
      },
      btts: {
        yesVotes: 0,
        noVotes: 0,
        totalVotes: 0
      }
    };
  });

  // Fill in the actual vote counts
  stats.forEach(stat => {
    const fixtureId = stat._id.fixtureId;
    const category = stat._id.category;
    const vote = stat._id.vote;
    const count = stat.count;

    switch (category) {
      case VoteCategory.MATCH_OUTCOME:
        if (vote === VoteOption.HOME_WIN) result[fixtureId].match_outcome.homeVotes = count;
        else if (vote === VoteOption.DRAW) result[fixtureId].match_outcome.drawVotes = count;
        else if (vote === VoteOption.AWAY_WIN) result[fixtureId].match_outcome.awayVotes = count;
        result[fixtureId].match_outcome.totalVotes += count;
        break;
      case VoteCategory.OVER_UNDER:
        if (vote === VoteOption.OVER_25) result[fixtureId].over_under.over25Votes = count;
        else if (vote === VoteOption.UNDER_25) result[fixtureId].over_under.under25Votes = count;
        result[fixtureId].over_under.totalVotes += count;
        break;
      case VoteCategory.BTTS:
        if (vote === VoteOption.BTTS_YES) result[fixtureId].btts.yesVotes = count;
        else if (vote === VoteOption.BTTS_NO) result[fixtureId].btts.noVotes = count;
        result[fixtureId].btts.totalVotes += count;
        break;
    }
  });

  return result;
}

// Helper function to determine if a vote was correct
function isVoteCorrect(vote: VoteOption, category: VoteCategory, fixture: any): { isCorrect: boolean | null, result: string | null } {
  // If the fixture doesn't have a status or it's not finished, we can't determine if the vote was correct
  if (!fixture || !fixture.fixture || !fixture.fixture.status ||
      !['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short)) {
    return { isCorrect: null, result: null };
  }

  // Get the goals
  const homeGoals = fixture.goals.home;
  const awayGoals = fixture.goals.away;

  // If goals are not available, we can't determine the result
  if (homeGoals === null || awayGoals === null) {
    return { isCorrect: null, result: null };
  }

  switch (category) {
    case VoteCategory.MATCH_OUTCOME:
      // Determine the actual match result
      let actualResult: VoteOption;
      if (homeGoals > awayGoals) {
        actualResult = VoteOption.HOME_WIN;
      } else if (homeGoals < awayGoals) {
        actualResult = VoteOption.AWAY_WIN;
      } else {
        actualResult = VoteOption.DRAW;
      }

      const isCorrect = vote === actualResult;
      let resultString: string;
      if (actualResult === VoteOption.HOME_WIN) {
        resultString = 'Home Win';
      } else if (actualResult === VoteOption.AWAY_WIN) {
        resultString = 'Away Win';
      } else {
        resultString = 'Draw';
      }

      return { isCorrect, result: resultString };

    case VoteCategory.OVER_UNDER:
      const totalGoals = homeGoals + awayGoals;
      const actualOverUnder = totalGoals > 2.5 ? VoteOption.OVER_25 : VoteOption.UNDER_25;
      return {
        isCorrect: vote === actualOverUnder,
        result: totalGoals > 2.5 ? 'Over 2.5' : 'Under 2.5'
      };

    case VoteCategory.BTTS:
      const bothTeamsScored = homeGoals > 0 && awayGoals > 0;
      const actualBTTS = bothTeamsScored ? VoteOption.BTTS_YES : VoteOption.BTTS_NO;
      return {
        isCorrect: vote === actualBTTS,
        result: bothTeamsScored ? 'Both Teams Scored' : 'Not Both Teams Scored'
      };

    default:
      return { isCorrect: null, result: null };
  }
}

// Get the count of votes for a user (authenticated users only)
export async function getVoteCount(userId: string): Promise<number> {
  const collection = getFixtureVotesCollection();

  // Count the votes for this user
  const count = await collection.countDocuments({
    userId: new ObjectId(userId),
    isGuestVote: false
  });

  return count;
}

// Get all dates for which a user has votes (authenticated users only)
export async function getVoteDates(userId: string): Promise<string[]> {
  const collection = getFixtureVotesCollection();
  const fixturesCollection = getDb().collection('fixtures');

  // First, get all votes for the user
  const votes = await collection.find({
    userId: new ObjectId(userId),
    isGuestVote: false
  }).toArray();

  // If no votes, return empty array
  if (!votes.length) {
    return [];
  }

  // Get all fixture IDs from the votes
  const fixtureIds = votes.map(vote => vote.fixtureId);

  // Fetch fixture dates
  const fixtures = await fixturesCollection.aggregate([
    { $match: { _id: { $in: fixtureIds } } },
    { $project: { "fixture.date": 1 } }
  ]).toArray();

  // Extract dates and format them as YYYY-MM-DD
  const dates = fixtures.map(fixture => {
    const date = new Date(fixture.fixture.date);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
  });

  // Remove duplicates and sort
  return [...new Set(dates)].sort((a, b) => b.localeCompare(a));
}

// Get all votes for a user with fixture details, optionally filtered by date (authenticated users only)
export async function getUserVotes(userId: string, dateFilter?: string, timezone?: string): Promise<any[]> {
  const collection = getFixtureVotesCollection();
  const fixturesCollection = getDb().collection('fixtures');

  // First, get all votes for the user
  const votes = await collection.find({
    userId: new ObjectId(userId),
    isGuestVote: false
  }).toArray();

  // If no votes, return empty array
  if (!votes.length) {
    return [];
  }

  // Get all fixture IDs from the votes
  const fixtureIds = votes.map(vote => vote.fixtureId);

  // Prepare the aggregation pipeline
  const pipeline: any[] = [
    { $match: { _id: { $in: fixtureIds } } }
  ];

  // Add projection to only include necessary fields
  pipeline.push({
    $project: {
      "fixture.id": 1,
      "fixture.date": 1,
      "fixture.status": 1,
      "teams.home.name": 1,
      "teams.home.logo": 1,
      "teams.away.name": 1,
      "teams.away.logo": 1,
      "goals": 1
    }
  });

  // Fetch fixture details with minimal data
  const fixtures = await fixturesCollection.aggregate(pipeline).toArray();

  // Create a map of fixture ID to fixture details for quick lookup
  const fixtureMap: { [key: string]: any } = {};
  fixtures.forEach(fixture => {
    // Convert _id to string to avoid type issues
    fixtureMap[fixture._id.toString()] = fixture;
  });

  // Combine votes with fixture details and add correctness information
  let votesWithFixtures = votes.map(vote => {
    const fixture = fixtureMap[vote.fixtureId.toString()] || null;
    const { isCorrect, result } = isVoteCorrect(vote.vote, vote.category, fixture);

    return {
      ...vote,
      fixture,
      voteResult: {
        isCorrect,
        result
      }
    };
  });

  // Filter by date if dateFilter is provided
  if (dateFilter) {
    votesWithFixtures = votesWithFixtures.filter(vote => {
      if (!vote.fixture || !vote.fixture.fixture || !vote.fixture.fixture.date) {
        return false;
      }

      // Get the fixture timestamp (stored in UTC)
      const fixtureTimestamp = new Date(vote.fixture.fixture.date).getTime() / 1000;

      // Calculate day boundaries in the specified timezone (or UTC if not provided)
      let startOfDay: number;
      let endOfDay: number;

      if (timezone) {
        try {
          startOfDay = dayjs.tz(dateFilter, timezone).startOf('day').unix();
          endOfDay = dayjs.tz(dateFilter, timezone).endOf('day').unix();
        } catch (error) {
          console.warn(`Invalid timezone provided: ${timezone}, falling back to UTC`);
          startOfDay = dayjs(dateFilter).startOf('day').unix();
          endOfDay = dayjs(dateFilter).endOf('day').unix();
        }
      } else {
        startOfDay = dayjs(dateFilter).startOf('day').unix();
        endOfDay = dayjs(dateFilter).endOf('day').unix();
      }

      // Check if fixture timestamp falls within the day boundaries
      return fixtureTimestamp >= startOfDay && fixtureTimestamp <= endOfDay;
    });
  }

  return votesWithFixtures;
}
