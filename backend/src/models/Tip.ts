import { Collection, ObjectId } from 'mongodb';
import { getDb } from '../config/database';

// Enum for tip types
export enum TipType {
  BTTS = 'btts', // Both Teams To Score
  OVER_UNDER_GOALS = 'over_under_goals', // Over/Under total goals
  OVER_UNDER_CORNERS = 'over_under_corners', // Over/Under total corners
  OVER_UNDER_CARDS = 'over_under_cards', // Over/Under total cards
  MATCH_RESULT = 'match_result', // 1X2 (Home/Draw/Away)
  DOUBLE_CHANCE = 'double_chance', // 1X, X2, 12
  FIRST_GOAL = 'first_goal', // Team to score first goal
  HANDICAP = 'handicap', // Asian handicap
  CORRECT_SCORE = 'correct_score', // Exact score prediction
  HALF_TIME_RESULT = 'half_time_result' // Half time result
}

// Enum for tip status
export enum TipStatus {
  PENDING = 'pending', // Tip is active, match hasn't started or finished
  WON = 'won', // Tip was correct
  LOST = 'lost', // Tip was incorrect
  VOID = 'void', // Tip was voided (match cancelled, etc.)
  PUSH = 'push' // Tip resulted in a push (exact line hit)
}

// Interface for tip details based on type
export interface TipDetails {
  // Market selection
  market?: 'fulltime' | 'firsthalf' | 'secondhalf';

  // For BTTS
  bttsValue?: 'yes' | 'no';

  // For Over/Under types
  line?: number; // e.g., 2.5, 9.5, 4.5
  overUnder?: 'over' | 'under';

  // For Match Result
  result?: 'home' | 'draw' | 'away';

  // For Double Chance
  doubleChance?: '1X' | 'X2' | '12';

  // For First Goal
  firstGoalTeam?: 'home' | 'away';

  // For Handicap
  handicapValue?: number; // e.g., -1.5, +0.5
  handicapTeam?: 'home' | 'away';

  // For Correct Score
  homeScore?: number;
  awayScore?: number;

  // For Half Time Result
  halfTimeResult?: 'home' | 'draw' | 'away';
}

// Main Tip interface for MongoDB document
export interface Tip {
  _id?: ObjectId;
  userId: ObjectId; // Reference to User who created the tip
  fixtureId: number; // Reference to Fixture
  tipType: TipType;
  details: TipDetails;
  odds: number; // Decimal odds (e.g., 1.85, 2.50)
  stake?: number; // Optional stake amount
  description?: string; // Optional description/reasoning
  status: TipStatus;
  result?: {
    actualValue?: any; // Actual result that determined the outcome
    calculatedAt: Date; // When the result was calculated
  };
  createdAt: Date;
  updatedAt: Date;
  
  // Additional metadata
  isPublic: boolean; // Whether tip is visible to other users
  likes?: number; // Number of likes from other users
  comments?: number; // Number of comments
}

// Interface for tip with user and fixture details (for display)
export interface TipWithDetails extends Tip {
  user: {
    _id: ObjectId;
    name?: string;
    profileImage?: string;
  };
  fixture: {
    _id: number;
    teams: {
      home: { name: string; logo?: string };
      away: { name: string; logo?: string };
    };
    league: {
      name: string;
      country: string;
      logo?: string;
    };
    fixture: {
      date: string;
      status: { short: string; long: string };
    };
  };
}

// Function to get the tips collection
export function getTipsCollection(): Collection<Tip> {
  return getDb().collection<Tip>('tips');
}

// Create a new tip
export async function createTip(tipData: Omit<Tip, '_id' | 'createdAt' | 'updatedAt' | 'status' | 'likes' | 'comments'>): Promise<Tip> {
  const collection = getTipsCollection();
  const now = new Date();

  const newTip: Tip = {
    ...tipData,
    status: TipStatus.PENDING,
    createdAt: now,
    updatedAt: now,
    likes: 0,
    comments: 0
  };

  const result = await collection.insertOne(newTip);
  return { ...newTip, _id: result.insertedId };
}

// Find tips by fixture ID
export async function findTipsByFixture(fixtureId: number, limit: number = 50): Promise<Tip[]> {
  const collection = getTipsCollection();
  return collection
    .find({ fixtureId, isPublic: true })
    .sort({ createdAt: -1 })
    .limit(limit)
    .toArray();
}

// Find tips by user ID
export async function findTipsByUser(userId: string, limit: number = 50): Promise<Tip[]> {
  const collection = getTipsCollection();
  return collection
    .find({ userId: new ObjectId(userId) })
    .sort({ createdAt: -1 })
    .limit(limit)
    .toArray();
}

// Update tip status and result
export async function updateTipResult(
  tipId: string,
  status: TipStatus,
  actualValue?: any
): Promise<void> {
  const collection = getTipsCollection();
  const now = new Date();

  await collection.updateOne(
    { _id: new ObjectId(tipId) },
    {
      $set: {
        status,
        result: {
          actualValue,
          calculatedAt: now
        },
        updatedAt: now
      }
    }
  );
}

// Delete a tip (only by owner)
export async function deleteTip(tipId: string, userId: string): Promise<boolean> {
  const collection = getTipsCollection();
  const result = await collection.deleteOne({
    _id: new ObjectId(tipId),
    userId: new ObjectId(userId)
  });
  
  return result.deletedCount > 0;
}

// Update tip likes count
export async function updateTipLikes(tipId: string, increment: number): Promise<void> {
  const collection = getTipsCollection();
  await collection.updateOne(
    { _id: new ObjectId(tipId) },
    {
      $inc: { likes: increment },
      $set: { updatedAt: new Date() }
    }
  );
}

// Get tips with user and fixture details for display
export async function getTipsWithDetails(fixtureId: number, limit: number = 50): Promise<TipWithDetails[]> {
  const collection = getTipsCollection();
  
  const pipeline = [
    {
      $match: { fixtureId, isPublic: true }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'userId',
        foreignField: '_id',
        as: 'user'
      }
    },
    {
      $lookup: {
        from: 'fixtures',
        localField: 'fixtureId',
        foreignField: '_id',
        as: 'fixture'
      }
    },
    {
      $unwind: '$user'
    },
    {
      $unwind: '$fixture'
    },
    {
      $project: {
        _id: 1,
        userId: 1,
        fixtureId: 1,
        tipType: 1,
        details: 1,
        odds: 1,
        stake: 1,
        description: 1,
        status: 1,
        result: 1,
        createdAt: 1,
        updatedAt: 1,
        isPublic: 1,
        likes: 1,
        comments: 1,
        'user._id': 1,
        'user.name': 1,
        'user.profileImage': 1,
        'fixture._id': 1,
        'fixture.teams': 1,
        'fixture.league': 1,
        'fixture.fixture.date': 1,
        'fixture.fixture.status': 1
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $limit: limit
    }
  ];

  return collection.aggregate<TipWithDetails>(pipeline).toArray();
}

// Helper function to validate tip details based on type
export function validateTipDetails(tipType: TipType, details: TipDetails): boolean {
  switch (tipType) {
    case TipType.BTTS:
      return details.bttsValue !== undefined;
    
    case TipType.OVER_UNDER_GOALS:
    case TipType.OVER_UNDER_CORNERS:
    case TipType.OVER_UNDER_CARDS:
      return details.line !== undefined && details.overUnder !== undefined;
    
    case TipType.MATCH_RESULT:
    case TipType.HALF_TIME_RESULT:
      return details.result !== undefined;
    
    case TipType.DOUBLE_CHANCE:
      return details.doubleChance !== undefined;

    case TipType.FIRST_GOAL:
      return details.firstGoalTeam !== undefined;
    
    case TipType.HANDICAP:
      return details.handicapValue !== undefined && details.handicapTeam !== undefined;
    
    case TipType.CORRECT_SCORE:
      return details.homeScore !== undefined && details.awayScore !== undefined;
    
    default:
      return false;
  }
}
