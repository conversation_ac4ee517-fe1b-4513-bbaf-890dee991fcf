import { Collection } from 'mongodb';
import { getDb } from '../config/database';

// Player information in transfer
export interface TransferPlayer {
    id: number;
    name: string;
}

// Team information in transfer
export interface TransferTeam {
    id: number;
    name: string;
    logo: string | null;
}

// Transfer information
export interface TransferInfo {
    date: string;
    type: string | null; // e.g., "Permanent", "Loan"
    teams: {
        in: TransferTeam;
        out: TransferTeam;
    };
}

// Main Transfer document for MongoDB
export interface Transfer {
    _id: string; // Composite key: playerId:transferDate
    player: TransferPlayer;
    update: string; // Date when the transfer was last updated
    transfers: TransferInfo[];
    lastUpdated: Date;
}

// Function to get the transfers collection
export function getTransfersCollection(): Collection<Transfer> {
    const db = getDb();
    return db.collection<Transfer>('transfers');
}

// Helper function to create a unique ID for a transfer
export function createTransferId(playerId: number, updateDate: string): string {
    return `${playerId}:${updateDate}`;
}
