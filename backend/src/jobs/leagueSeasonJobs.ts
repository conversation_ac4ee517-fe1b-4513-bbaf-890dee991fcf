import { fetchLeagueSeasons } from '../services/apiFootball';
import { updateLeagueSeasons } from '../models/LeagueSeason';

export async function fetchAndUpdateLeagueSeasons() {
    console.log('Starting fetchAndUpdateLeagueSeasons job...');
    try {
        const seasonsFromApi = await fetchLeagueSeasons(); // Fetch all seasons

        if (!seasonsFromApi || seasonsFromApi.length === 0) {
            console.log('No league seasons received from API.');
            return;
        }

        // Sort seasons numerically (descending is common for display)
        const sortedSeasons = seasonsFromApi.sort((a, b) => b - a);

        const result = await updateLeagueSeasons(sortedSeasons);

        if (result.modifiedCount > 0 || result.upsertedCount > 0) {
            console.log(`fetchAndUpdateLeagueSeasons job finished. Seasons updated/inserted.`);
        } else {
            console.log('fetchAndUpdateLeagueSeasons job: No changes detected.');
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateLeagueSeasons job:', error);
    }
}
