import { fetchTeams } from '../services/apiFootball';
import { getTeamsCollection, Team, addLeagueAssociation } from '../models/Team';
import { getLeaguesCollection } from '../models/League'; // Need leagues to know which teams to fetch
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchAndUpdateTeamsForActiveLeagues() {
    console.log('Starting fetchAndUpdateTeamsForActiveLeagues job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const teamsCollection = getTeamsCollection();
        const now = new Date();

        // 1. Find all targeted leagues with a currently active season
        // We only need the league ID and the current season year
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: targetedLeagues }, // Only include targeted leagues
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found to fetch teams for.');
            return;
        }

        console.log(`Found ${activeLeagues.length} leagues with active seasons.`);

        // 2. Iterate through active leagues/seasons and fetch teams
        let totalUpserted = 0;
        let totalModified = 0;
        let totalLeagueAssociations = 0;

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue; // Should not happen based on query, but good practice

            const leagueId = league._id;
            const seasonYear = currentSeason.year;

            console.log(`Fetching teams for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                const teamsFromApi = await fetchTeams({ league: leagueId, season: seasonYear });

                if (!teamsFromApi || teamsFromApi.length === 0) {
                    console.log(`No teams received from API for League ${leagueId}, Season ${seasonYear}.`);
                    continue; // Move to the next league/season
                }

                const bulkOps: AnyBulkWriteOperation<Team>[] = [];
                const leagueAssociationOps: AnyBulkWriteOperation<Team>[] = [];

                for (const teamApi of teamsFromApi) {
                    const teamId = teamApi.team.id;
                    const filter = { _id: teamId }; // Use team ID as _id
                    const updateDoc: Omit<Team, '_id'> & { _id: number, apiId: number } = {
                        _id: teamId,
                        apiId: teamId, // Add apiId field to match existing database index
                        team: teamApi.team,
                        venue: teamApi.venue,
                        lastUpdated: now,
                    };

                    // First operation: Update the team data
                    bulkOps.push({
                        updateOne: {
                            filter: filter,
                            update: { $set: updateDoc },
                            upsert: true,
                        },
                    });

                    // Second operation: Add the league association
                    leagueAssociationOps.push(addLeagueAssociation(teamId, leagueId, seasonYear));
                }

                // Execute the team updates
                if (bulkOps.length > 0) {
                    const result = await teamsCollection.bulkWrite(bulkOps);
                    console.log(`  League ${leagueId}/${seasonYear}: Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Execute the league association updates
                if (leagueAssociationOps.length > 0) {
                    const assocResult = await teamsCollection.bulkWrite(leagueAssociationOps);
                    console.log(`  Added ${leagueAssociationOps.length} league associations for League ${leagueId}/${seasonYear}`);
                    totalLeagueAssociations += leagueAssociationOps.length;
                }

                // Add a small delay between league fetches to respect rate limits
                await delay(500);

            } catch (leagueError) {
                console.error(`Error fetching teams for League ${leagueId}, Season ${seasonYear}:`, leagueError);
                // Continue to the next league even if one fails
            }
        }

        console.log(`fetchAndUpdateTeamsForActiveLeagues job finished.`);
        console.log(`Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);
        console.log(`Total League Associations Added: ${totalLeagueAssociations}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateTeamsForActiveLeagues job:', error);
    }
}
