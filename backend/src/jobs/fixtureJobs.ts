import { fetchFixtures } from '../services/apiFootball';
import { getFixturesCollection, Fixture } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import dayjs from 'dayjs'; // Using dayjs for easier date formatting
import { filterByTargetedLeagues } from '../config/targetedLeagues';
import { invalidateFixtureCache, invalidateFixtureCaches, getRedisClient } from '../config/redis';
import { processFinishedFixtureTips } from './tipProcessingJobs';
// Conditional imports to avoid server startup when running jobs standalone
let getSocketIO: any = null;
let broadcastFixtureUpdates: any = null;

// Only import server-related modules if we're not in a standalone job context
if (!process.env.STANDALONE_JOB) {
    try {
        const serverModule = require('../server');
        getSocketIO = serverModule.getSocketIO;

        const fixtureSocketModule = require('../services/fixtureSocketService');
        broadcastFixtureUpdates = fixtureSocketModule.broadcastFixtureUpdates;
    } catch (error) {
        // If server modules can't be imported (e.g., in standalone job), continue without them
        console.log('Running in standalone mode - server features disabled');
    }
}

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));



// Helper function to process fixtures and create bulk operations
async function createFixtureBulkOps(fixturesFromApi: any[], now: Date): Promise<AnyBulkWriteOperation<Fixture>[]> {
    const bulkOps: AnyBulkWriteOperation<Fixture>[] = [];

    for (const fixtureApi of fixturesFromApi) {
        // Use fixture ID as the unique identifier (_id) for upsert
        const fixtureId = fixtureApi.fixture.id;
        const filter = { _id: fixtureId };

        // Extract the date from the fixture timestamp for easier querying
        const fixtureDate = new Date(fixtureApi.fixture.timestamp * 1000);
        // Reset hours, minutes, seconds, and milliseconds to get just the date
        fixtureDate.setHours(0, 0, 0, 0);

        // Create an update document with only the fields that are present in the API response
        // This ensures we don't overwrite existing detailed data with undefined values
        const baseUpdateDoc = {
            apiId: fixtureId,
            fixture: fixtureApi.fixture,
            league: fixtureApi.league,
            teams: fixtureApi.teams,
            goals: fixtureApi.goals,
            score: fixtureApi.score,
            date: fixtureDate, // Add the extracted date field for easier querying
            lastUpdated: now,
        };

        // Only include detailed fields if they are present in the API response
        // This prevents overwriting existing data with undefined values
        const updateDoc: any = { ...baseUpdateDoc };

        // For events, just use the API events directly without reconciliation
        if (fixtureApi.events) {
            updateDoc.events = fixtureApi.events;
            console.log(`Using ${fixtureApi.events.length} events from API for fixture ${fixtureId}`);
        }

        if (fixtureApi.lineups) updateDoc.lineups = fixtureApi.lineups;
        if (fixtureApi.statistics) updateDoc.statistics = fixtureApi.statistics;
        if (fixtureApi.players) updateDoc.players = fixtureApi.players;

        bulkOps.push({
            updateOne: {
                filter: filter,
                update: { $set: updateDoc },
                upsert: true, // Insert if not found, update if found
            },
        });
    }

    return bulkOps;
}

// Fetches fixtures for a specific date (defaults to today)
export async function fetchAndUpdateFixturesByDate(date?: string) {
    const targetDate = date || dayjs().format('YYYY-MM-DD'); // Default to today
    console.log(`Starting fetchAndUpdateFixtures job for date: ${targetDate}...`);
    try {
        const allFixturesFromApi = await fetchFixtures({ date: targetDate });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log(`No fixtures received from API for date: ${targetDate}.`);
            return;
        }

        // Filter fixtures to only include targeted leagues
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);
        console.log(`Filtered ${allFixturesFromApi.length} fixtures down to ${fixturesFromApi.length} fixtures from targeted leagues for date: ${targetDate}.`);

        const collection = getFixturesCollection();
        const now = new Date();
        const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`fetchAndUpdateFixtures job for ${targetDate} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Invalidate cache if there were updates
            if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                // Invalidate individual fixture caches
                const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                await invalidateFixtureCaches(fixtureIds);

                // Also invalidate the date cache
                try {
                    const redisClient = getRedisClient();
                    await redisClient.del(`fixtures:date:${targetDate}`);
                    console.log(`Cache invalidated for fixtures on date ${targetDate}`);
                } catch (error) {
                    console.error(`Error invalidating fixtures cache for date ${targetDate}:`, error);
                }

                // Check for newly finished matches and fetch detailed data for them
                const finishedFixtures = fixturesFromApi.filter(fixture =>
                    ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
                );

                if (finishedFixtures.length > 0) {
                    console.log(`Found ${finishedFixtures.length} finished fixtures. Fetching detailed data...`);
                    const finishedFixtureIds = finishedFixtures.map(fixture => fixture.fixture.id);
                    await fetchAndUpdateFixturesById(finishedFixtureIds);

                    // Process tips for finished fixtures
                    if (!process.env.STANDALONE_JOB) {
                        await processFinishedFixtureTips(finishedFixtureIds);
                    }
                }
            }
        } else {
            console.log(`fetchAndUpdateFixtures job for ${targetDate}: No changes detected.`);
        }

    } catch (error) {
        console.error(`Error in fetchAndUpdateFixtures job for date ${targetDate}:`, error);
    }
}

// Fetches detailed fixture information by ID or batch of IDs
export async function fetchAndUpdateFixturesById(fixtureIds: number[] | number) {
    // Convert single ID to array for consistent handling
    const ids = Array.isArray(fixtureIds) ? fixtureIds : [fixtureIds];

    // API has a limit of 20 IDs per request
    const MAX_BATCH_SIZE = 20;

    console.log(`Starting fetchAndUpdateFixturesById job for ${ids.length} fixture(s)...`);
    try {
        const collection = getFixturesCollection();
        const now = new Date();
        let totalUpserted = 0;
        let totalModified = 0;

        // Process in batches of MAX_BATCH_SIZE
        for (let i = 0; i < ids.length; i += MAX_BATCH_SIZE) {
            const batchIds = ids.slice(i, i + MAX_BATCH_SIZE);
            const idString = batchIds.join('-');

            console.log(`Fetching batch ${Math.floor(i/MAX_BATCH_SIZE) + 1}: ${idString}`);

            const fixturesFromApi = await fetchFixtures({ ids: idString });

            if (!fixturesFromApi || fixturesFromApi.length === 0) {
                console.log(`No fixtures received from API for batch: ${idString}.`);
                continue;
            }

            console.log(`Received ${fixturesFromApi.length} fixtures for batch: ${idString}.`);

            const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

            if (bulkOps.length > 0) {
                const result = await collection.bulkWrite(bulkOps);
                console.log(`Batch ${Math.floor(i/MAX_BATCH_SIZE) + 1} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                totalUpserted += result.upsertedCount;
                totalModified += result.modifiedCount;

                // Invalidate cache for all fixtures in this batch
                // This ensures that the next request for these fixtures will get fresh data
                if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                    await invalidateFixtureCaches(batchIds);
                }
            }

            // Add a delay between batches to respect API rate limits
            if (i + MAX_BATCH_SIZE < ids.length) {
                await delay(500);
            }
        }

        console.log(`fetchAndUpdateFixturesById job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error(`Error in fetchAndUpdateFixturesById job:`, error);
    }
}

// --- Live Fixture Handling ---
// Fetches all currently live fixtures
export async function fetchAndUpdateLiveFixtures() {
    console.log('Starting fetchAndUpdateLiveFixtures job...');
    try {
        // First, get the list of fixtures that were previously live
        const collection = getFixturesCollection();
        const liveStatuses = ['LIVE', '1H', 'HT', '2H', 'ET', 'BT', 'P', 'SUSP', 'INT'];

        // Add date validation to prevent future fixtures from being considered live
        const currentTime = new Date();
        const maxLiveDate = new Date(currentTime.getTime() + (24 * 60 * 60 * 1000)); // Allow up to 24 hours in future

        const previouslyLiveFixtures = await collection.find({
            'fixture.status.short': { $in: liveStatuses },
            'fixture.timestamp': { $lte: Math.floor(maxLiveDate.getTime() / 1000) }
        }, { projection: { _id: 1 } }).toArray();
        const previouslyLiveIds = previouslyLiveFixtures.map(f => f._id);

        // Now fetch current live fixtures from API
        const allFixturesFromApi = await fetchFixtures({ live: 'all' });

        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
            console.log('No live fixtures currently available.');

            // Check if any previously live fixtures are no longer live (they finished)
            if (previouslyLiveIds.length > 0) {
                console.log(`Checking ${previouslyLiveIds.length} previously live fixtures that may have finished...`);

                // For efficiency, if there's only one previously live fixture, fetch it directly
                if (previouslyLiveIds.length === 1) {
                    const fixtureId = previouslyLiveIds[0];

                    // First check if the fixture is already marked as finished in our database
                    const existingFixture = await collection.findOne({
                        _id: fixtureId,
                        'fixture.status.short': { $in: ['FT', 'AET', 'PEN', 'WO', 'AWD'] }
                    });

                    // If the fixture is already marked as finished, no need to fetch it again
                    if (existingFixture) {
                        console.log(`Fixture ${fixtureId} is already marked as finished (${existingFixture.fixture.status.short}). No need to fetch again.`);
                        return;
                    }

                    console.log(`Only one previously live fixture (${fixtureId}). Fetching it directly...`);

                    try {
                        // Fetch the specific fixture by ID to get its current status
                        const fixtureFromApi = await fetchFixtures({ id: fixtureId });

                        if (fixtureFromApi && fixtureFromApi.length > 0) {
                            const fixture = fixtureFromApi[0];
                            console.log(`Fixture ${fixtureId} current status: ${fixture.fixture.status.short} (${fixture.fixture.status.long})`);

                            // Create and execute bulk operation for this specific fixture
                            const now = new Date();
                            const bulkOps = await createFixtureBulkOps([fixture], now);

                            if (bulkOps.length > 0) {
                                const result = await collection.bulkWrite(bulkOps);
                                console.log(`Updated fixture ${fixtureId}. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

                                // Invalidate cache for this fixture
                                await invalidateFixtureCache(fixtureId);

                                // Broadcast the updated fixture via socket (if server features are available)
                                if (getSocketIO && broadcastFixtureUpdates) {
                                    const updatedFixture = await collection.findOne({ _id: fixtureId });
                                    const io = getSocketIO();
                                    if (io && updatedFixture) {
                                        console.log(`Broadcasting update for finished fixture ${fixtureId} via socket...`);
                                        broadcastFixtureUpdates(io, [updatedFixture]);
                                    }
                                }
                            }
                        } else {
                            console.log(`No fixture data received from API for ID: ${fixtureId}. Falling back to date-based fetch.`);
                            // If direct fetch returns no data, fall back to fetching today's fixtures
                            const today = dayjs().format('YYYY-MM-DD');
                            await fetchAndUpdateFixturesByDate(today);
                        }
                    } catch (error) {
                        console.error(`Error fetching fixture ${fixtureId}:`, error);

                        // If direct fetch fails, fall back to fetching today's fixtures
                        const today = dayjs().format('YYYY-MM-DD');
                        await fetchAndUpdateFixturesByDate(today);
                    }
                } else {
                    // If there are multiple previously live fixtures, filter out any that are already finished
                    const finishedFixtures = await collection.find({
                        _id: { $in: previouslyLiveIds },
                        'fixture.status.short': { $in: ['FT', 'AET', 'PEN', 'WO', 'AWD'] }
                    }, { projection: { _id: 1 } }).toArray();

                    const finishedIds = finishedFixtures.map(f => f._id);
                    const remainingIds = previouslyLiveIds.filter(id => !finishedIds.includes(id));

                    if (finishedIds.length > 0) {
                        console.log(`Skipping ${finishedIds.length} fixtures that are already marked as finished.`);
                    }

                    if (remainingIds.length === 0) {
                        console.log('All previously live fixtures are already marked as finished. No need to fetch updates.');
                        return;
                    }

                    // Use the existing approach for the remaining fixtures
                    const today = dayjs().format('YYYY-MM-DD');
                    await fetchAndUpdateFixturesByDate(today);

                    // IMPORTANT FIX: Specifically fetch and update previously live fixtures by ID
                    // This ensures we get the correct final status (FT, Match Finished) for these fixtures
                    console.log(`Directly fetching updated status for ${remainingIds.length} fixtures that were previously live...`);
                    await fetchAndUpdateFixturesById(remainingIds);

                    // After fetching updated fixtures, broadcast their finished status (if server features are available)
                    // Only broadcast the fixtures that were actually updated (the remaining ones)
                    if (getSocketIO && broadcastFixtureUpdates) {
                        const updatedFixtures = await collection.find({ _id: { $in: remainingIds } }).toArray();
                        const io = getSocketIO();
                        if (io && updatedFixtures.length > 0) {
                            console.log(`Broadcasting updates for ${updatedFixtures.length} finished fixtures via socket...`);
                            broadcastFixtureUpdates(io, updatedFixtures);
                        }
                    }
                }
            }
            return;
        }

        // Filter fixtures to only include targeted leagues
        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);
        console.log(`Filtered ${allFixturesFromApi.length} live fixtures down to ${fixturesFromApi.length} fixtures from targeted leagues.`);

        const now = new Date();
        const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`fetchAndUpdateLiveFixtures job finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);

            // Get the fixture IDs for cache invalidation
            const currentLiveIds = fixturesFromApi.map(fixture => fixture.fixture.id);

            // Invalidate the live fixtures cache
            if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                // Invalidate individual fixture caches
                await invalidateFixtureCaches(currentLiveIds);

                // Also invalidate the live:all cache
                try {
                    const redisClient = getRedisClient();
                    await redisClient.del('fixtures:live:all');
                    console.log('Cache invalidated for live fixtures');
                } catch (error) {
                    console.error('Error invalidating live fixtures cache:', error);
                }

                // CRITICAL FIX: Also invalidate date-based caches for live fixtures
                // This ensures that when users refresh the page, they get fresh data instead of stale 10-minute cached data
                try {
                    const redisClient = getRedisClient();
                    const uniqueDates = new Set<string>();

                    // Collect all unique dates from the live fixtures
                    fixturesFromApi.forEach(fixture => {
                        if (fixture.fixture && fixture.fixture.date) {
                            const fixtureDate = dayjs(fixture.fixture.date).format('YYYY-MM-DD');
                            uniqueDates.add(fixtureDate);
                        }
                    });

                    // Invalidate cache for each unique date
                    for (const date of uniqueDates) {
                        await redisClient.del(`fixtures:date:${date}`);
                        // Also invalidate timezone-specific caches if they exist
                        const keys = await redisClient.keys(`fixtures:date:${date}:tz:*`);
                        if (keys.length > 0) {
                            await redisClient.del(...keys);
                        }
                    }

                    console.log(`Date cache invalidated for ${uniqueDates.size} dates with live fixtures: ${Array.from(uniqueDates).join(', ')}`);
                } catch (error) {
                    console.error('Error invalidating date-based caches for live fixtures:', error);
                }
            }

            // Now fetch detailed information for each live fixture
            if (fixturesFromApi.length > 0) {
                console.log(`Fetching detailed information for ${fixturesFromApi.length} live fixtures...`);
                await fetchAndUpdateFixturesById(currentLiveIds);

                // Broadcast fixture updates via socket (if server features are available)
                if (getSocketIO && broadcastFixtureUpdates) {
                    const io = getSocketIO();
                    if (io && (result.upsertedCount > 0 || result.modifiedCount > 0)) {
                        // Fetch the updated fixtures from the database to ensure we have the correct types
                        const updatedFixtures = await collection.find({ _id: { $in: currentLiveIds } }).toArray();
                        console.log(`Broadcasting updates for ${updatedFixtures.length} live fixtures via socket...`);
                        broadcastFixtureUpdates(io, updatedFixtures);
                    }
                }
            }

            // Check for fixtures that were live before but are no longer live (they finished)
            const finishedIds = previouslyLiveIds.filter(id => !currentLiveIds.includes(id));
            if (finishedIds.length > 0) {
                console.log(`Found ${finishedIds.length} fixtures that were live but have now finished. Fetching detailed data...`);
                await fetchAndUpdateFixturesById(finishedIds);

                // Fetch the updated fixtures to broadcast their finished status
                if (finishedIds.length > 0) {
                    const finishedFixtures = await collection.find({ _id: { $in: finishedIds } }).toArray();

                    // Broadcast finished fixture updates via socket (if server features are available)
                    if (getSocketIO && broadcastFixtureUpdates) {
                        const io = getSocketIO();
                        if (io && finishedFixtures.length > 0) {
                            console.log(`Broadcasting updates for ${finishedFixtures.length} finished fixtures via socket...`);
                            broadcastFixtureUpdates(io, finishedFixtures);
                        }
                    }
                }
            }
        } else {
            console.log(`fetchAndUpdateLiveFixtures job: No changes detected.`);

            // CRITICAL FIX: Even when no changes are detected in live fixtures,
            // we still need to check if previously live fixtures have finished
            // This handles the case where a fixture was live but is no longer in the API live response
            if (previouslyLiveIds.length > 0) {
                const currentLiveIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                const potentiallyFinishedIds = previouslyLiveIds.filter(id => !currentLiveIds.includes(id));

                if (potentiallyFinishedIds.length > 0) {
                    console.log(`Found ${potentiallyFinishedIds.length} previously live fixtures that are no longer in API live response. Checking their status...`);

                    try {
                        // Fetch these fixtures directly from the API to check their current status
                        const idsString = potentiallyFinishedIds.join('-');
                        const potentiallyFinishedFixtures = await fetchFixtures({ ids: idsString });

                        if (potentiallyFinishedFixtures && potentiallyFinishedFixtures.length > 0) {
                            console.log(`Fetched ${potentiallyFinishedFixtures.length} potentially finished fixtures from API.`);

                            // Update these fixtures in the database
                            const now = new Date();
                            const bulkOps = await createFixtureBulkOps(potentiallyFinishedFixtures, now);

                            if (bulkOps.length > 0) {
                                const result = await collection.bulkWrite(bulkOps);
                                console.log(`Updated potentially finished fixtures. Modified: ${result.modifiedCount}`);

                                // Invalidate cache for these fixtures
                                await invalidateFixtureCaches(potentiallyFinishedIds);

                                // Also invalidate date-based caches for potentially finished fixtures
                                try {
                                    const redisClient = getRedisClient();
                                    const uniqueDates = new Set<string>();

                                    // Collect all unique dates from the potentially finished fixtures
                                    potentiallyFinishedFixtures.forEach(fixture => {
                                        if (fixture.fixture && fixture.fixture.date) {
                                            const fixtureDate = dayjs(fixture.fixture.date).format('YYYY-MM-DD');
                                            uniqueDates.add(fixtureDate);
                                        }
                                    });

                                    // Invalidate cache for each unique date
                                    for (const date of uniqueDates) {
                                        await redisClient.del(`fixtures:date:${date}`);
                                        // Also invalidate timezone-specific caches if they exist
                                        const keys = await redisClient.keys(`fixtures:date:${date}:tz:*`);
                                        if (keys.length > 0) {
                                            await redisClient.del(...keys);
                                        }
                                    }

                                    if (uniqueDates.size > 0) {
                                        console.log(`Date cache invalidated for ${uniqueDates.size} dates with potentially finished fixtures: ${Array.from(uniqueDates).join(', ')}`);
                                    }
                                } catch (error) {
                                    console.error('Error invalidating date-based caches for potentially finished fixtures:', error);
                                }

                                // Check for newly finished fixtures and fetch detailed information
                                const finishedFixtures = potentiallyFinishedFixtures.filter(fixture =>
                                    ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
                                );

                                if (finishedFixtures.length > 0) {
                                    const finishedIds = finishedFixtures.map(fixture => fixture.fixture.id);
                                    console.log(`${finishedFixtures.length} fixtures have finished. Fetching detailed information...`);
                                    await fetchAndUpdateFixturesById(finishedIds);

                                    // Process tips for finished fixtures
                                    if (!process.env.STANDALONE_JOB) {
                                        await processFinishedFixtureTips(finishedIds);
                                    }

                                    // Broadcast the finished fixture updates via socket (if server features are available)
                                    if (getSocketIO && broadcastFixtureUpdates) {
                                        const io = getSocketIO();
                                        if (io) {
                                            const updatedFixtures = await collection.find({ _id: { $in: finishedIds } }).toArray();
                                            if (updatedFixtures.length > 0) {
                                                console.log(`Broadcasting finished fixture updates for ${updatedFixtures.length} fixtures via socket...`);
                                                broadcastFixtureUpdates(io, updatedFixtures);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } catch (error) {
                        console.error('Error fetching potentially finished fixtures:', error);
                    }
                }
            }
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateLiveFixtures job:', error);
    }
}

// Fetches upcoming fixtures for the next N days
export async function fetchAndUpdateUpcomingFixtures(days: number = 7) {
    console.log(`Starting fetchAndUpdateUpcomingFixtures job for next ${days} days...`);
    try {
        const collection = getFixturesCollection();
        const now = new Date();
        let totalUpserted = 0;
        let totalModified = 0;

        // Process one day at a time to avoid large result sets
        for (let i = 0; i < days; i++) {
            const targetDate = dayjs().add(i, 'day').format('YYYY-MM-DD');
            console.log(`Fetching fixtures for date: ${targetDate}`);

            const allFixturesFromApi = await fetchFixtures({ date: targetDate });

            if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
                console.log(`No fixtures received from API for date: ${targetDate}.`);
                continue;
            }

            // Filter fixtures to only include targeted leagues
            const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);
            console.log(`Filtered ${allFixturesFromApi.length} fixtures down to ${fixturesFromApi.length} fixtures from targeted leagues for date: ${targetDate}.`);

            const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

            if (bulkOps.length > 0) {
                const result = await collection.bulkWrite(bulkOps);
                console.log(`Day ${i+1} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                totalUpserted += result.upsertedCount;
                totalModified += result.modifiedCount;

                // Invalidate cache if there were updates
                if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                    // Invalidate individual fixture caches
                    const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                    await invalidateFixtureCaches(fixtureIds);

                    // Also invalidate the date cache
                    try {
                        const redisClient = getRedisClient();
                        await redisClient.del(`fixtures:date:${targetDate}`);
                        console.log(`Cache invalidated for fixtures on date ${targetDate}`);
                    } catch (error) {
                        console.error(`Error invalidating fixtures cache for date ${targetDate}:`, error);
                    }
                }
            }

            // Add a delay between days to respect API rate limits
            if (i < days - 1) {
                await delay(1000);
            }
        }

        console.log(`fetchAndUpdateUpcomingFixtures job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error(`Error in fetchAndUpcomingFixtures job:`, error);
    }
}

// Fetches lineups for upcoming fixtures within the next few hours
export async function fetchAndUpdateUpcomingFixtureLineups(hoursAhead: number = 3) {
    console.log(`Starting fetchAndUpdateUpcomingFixtureLineups job for fixtures in the next ${hoursAhead} hours...`);
    try {
        const collection = getFixturesCollection();
        const now = dayjs().unix();
        const futureTime = dayjs().add(hoursAhead, 'hour').unix();

        // Find fixtures that are scheduled to start within the next few hours
        // and have status NS (Not Started)
        const upcomingFixtures = await collection.find({
            'fixture.timestamp': { $gte: now, $lte: futureTime },
            'fixture.status.short': 'NS'
        }, { projection: { _id: 1 } }).toArray();

        const upcomingFixtureIds = upcomingFixtures.map(f => f._id);

        if (upcomingFixtureIds.length === 0) {
            console.log(`No upcoming fixtures found in the next ${hoursAhead} hours.`);
            return;
        }

        console.log(`Found ${upcomingFixtureIds.length} upcoming fixtures in the next ${hoursAhead} hours. Fetching detailed data including lineups...`);

        // Fetch detailed information for these fixtures, which includes lineups if available
        await fetchAndUpdateFixturesById(upcomingFixtureIds);

        console.log(`fetchAndUpdateUpcomingFixtureLineups job finished. Processed ${upcomingFixtureIds.length} fixtures.`);

    } catch (error) {
        console.error(`Error in fetchAndUpdateUpcomingFixtureLineups job:`, error);
    }
}

// Fetches fixtures for cup competitions by league and season
// This ensures we get all fixtures for cup competitions, including future rounds like semi-finals and finals
export async function fetchAndUpdateCupCompetitionFixtures() {
    console.log('Starting fetchAndUpdateCupCompetitionFixtures job...');

    // Define cup competition league IDs that need special handling
    const cupCompetitionLeagues = [
        15,   // FIFA Club World Cup
        2,    // UEFA Champions League
        3,    // UEFA Europa League
        848,  // UEFA Europa Conference League
        1,    // World Cup
        8,    // World Cup
        4,    // European Championship (UEFA Euro)
        9,    // Copa America (CONMEBOL)
        6,    // Africa Cup of Nations
        7,    // AFC Asian Cup
        17,   // AFC Champions League Elite
        1132, // AFC Champions League Two
        13,   // Copa Libertadores (CONMEBOL)
        11,   // Copa Sudamericana (CONMEBOL)
        12,   // CAF Champions League
        22,   // CONCACAF Gold Cup
        856,  // CONCACAF Champions Cup
        45,   // FA Cup
        48,   // League Cup (Carabao Cup)
        66,   // Coupe de France
        81,   // DFB Pokal
        137,  // Coppa Italia
        143,  // Copa Del Rey
        96,   // Taça De Portugal
    ];

    try {
        const collection = getFixturesCollection();
        const leaguesCollection = getLeaguesCollection();
        let totalUpserted = 0;
        let totalModified = 0;

        // Get active seasons for cup competitions from the database
        // This ensures we use the actual active seasons rather than hardcoded years
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: cupCompetitionLeagues }, // Only include cup competition leagues
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active cup competition leagues found.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active cup competition leagues.`);

        // Create a map of league ID to active seasons
        const leagueSeasonMap = new Map<number, number[]>();
        for (const league of activeLeagues) {
            const activeSeasons = league.seasons
                .filter((season: any) => season.current)
                .map((season: any) => season.year);

            if (activeSeasons.length > 0) {
                leagueSeasonMap.set(league._id, activeSeasons);
            }
        }

        // Also include current and next year as fallback for leagues that might not be in our database yet
        const currentYear = new Date().getFullYear();
        const fallbackSeasons = [currentYear, currentYear + 1];

        for (const leagueId of cupCompetitionLeagues) {
            // Get seasons for this league (from database or fallback)
            const seasonsToFetch = leagueSeasonMap.get(leagueId) || fallbackSeasons;

            for (const season of seasonsToFetch) {
                try {
                    console.log(`  📅 Fetching fixtures for Cup Competition League ${leagueId}, Season ${season}...`);

                    const allFixturesFromApi = await fetchFixtures({
                        league: leagueId,
                        season: season
                    });

                    if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
                        console.log(`    ⚠️  No fixtures found for League ${leagueId}, Season ${season}`);
                        continue;
                    }

                    console.log(`    ✅ Found ${allFixturesFromApi.length} fixtures`);

                    // Filter fixtures to only include targeted leagues (should already be included but double-check)
                    const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

                    if (fixturesFromApi.length === 0) {
                        console.log(`    ⚠️  No fixtures after filtering for targeted leagues`);
                        continue;
                    }

                    const now = new Date();
                    const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

                    if (bulkOps.length > 0) {
                        const result = await collection.bulkWrite(bulkOps);
                        console.log(`    💾 League ${leagueId} Season ${season}: Upserted ${result.upsertedCount}, Modified ${result.modifiedCount}`);

                        totalUpserted += result.upsertedCount;
                        totalModified += result.modifiedCount;

                        // Invalidate cache for updated fixtures
                        if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                            const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                            await invalidateFixtureCaches(fixtureIds);
                        }

                        // Fetch detailed data for finished fixtures
                        const finishedFixtures = fixturesFromApi.filter(fixture =>
                            ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
                        );

                        if (finishedFixtures.length > 0) {
                            console.log(`    🔍 Fetching detailed data for ${finishedFixtures.length} finished fixtures...`);
                            const finishedIds = finishedFixtures.map(f => f.fixture.id);
                            await fetchAndUpdateFixturesById(finishedIds);

                            // Process tips for finished fixtures
                            if (!process.env.STANDALONE_JOB) {
                                await processFinishedFixtureTips(finishedIds);
                            }
                        }
                    }

                    // Add delay between API calls to respect rate limits
                    await delay(1000);

                } catch (error) {
                    console.error(`Error fetching fixtures for League ${leagueId}, Season ${season}:`, error);
                    // Continue with next league/season instead of failing completely
                    continue;
                }
            }
        }

        console.log(`fetchAndUpdateCupCompetitionFixtures job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateCupCompetitionFixtures job:', error);
    }
}

// Fetches complete season fixtures for all targeted leagues
// This job runs daily and processes ALL leagues to ensure complete coverage
// Replaces the need for separate daily upcoming fixtures job
export async function fetchAndUpdateSeasonFixtures() {
    console.log('Starting fetchAndUpdateSeasonFixtures job...');

    try {
        const collection = getFixturesCollection();
        const leaguesCollection = getLeaguesCollection();
        const { targetedLeagues } = await import('../config/targetedLeagues');

        // Process ALL targeted leagues daily for complete coverage
        const leaguesToProcess = targetedLeagues;

        console.log(`📅 Processing ALL ${leaguesToProcess.length} targeted leagues for complete daily coverage`);

        if (leaguesToProcess.length === 0) {
            console.log('No leagues to process.');
            return;
        }

        // Get active seasons for these leagues from the database
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: leaguesToProcess },
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1, "league.name": 1 } }
        ).toArray();

        console.log(`Found ${activeLeagues.length} leagues with active seasons out of ${leaguesToProcess.length} targeted leagues.`);

        // Create a map of league ID to active seasons
        const leagueSeasonMap = new Map<number, { seasons: number[], name: string }>();
        for (const league of activeLeagues) {
            const activeSeasons = league.seasons
                .filter((season: any) => season.current)
                .map((season: any) => season.year);

            if (activeSeasons.length > 0) {
                leagueSeasonMap.set(league._id, {
                    seasons: activeSeasons,
                    name: league.league?.name || `League ${league._id}`
                });
            }
        }

        // Also include current and next year as fallback for leagues not in database
        const currentYear = new Date().getFullYear();
        const fallbackSeasons = [currentYear, currentYear + 1];

        let totalUpserted = 0;
        let totalModified = 0;
        let processedLeagues = 0;

        for (const leagueId of leaguesToProcess) {
            try {
                // Get seasons for this league (from database or fallback)
                const leagueInfo = leagueSeasonMap.get(leagueId);
                const seasonsToFetch = leagueInfo?.seasons || fallbackSeasons;
                const leagueName = leagueInfo?.name || `League ${leagueId}`;

                console.log(`  🏆 Processing ${leagueName} (ID: ${leagueId}) - Seasons: ${seasonsToFetch.join(', ')}`);

                for (const season of seasonsToFetch) {
                    try {
                        console.log(`    📅 Fetching season ${season}...`);

                        const allFixturesFromApi = await fetchFixtures({
                            league: leagueId,
                            season: season
                        });

                        if (!allFixturesFromApi || allFixturesFromApi.length === 0) {
                            console.log(`    ⚠️  No fixtures found for season ${season}`);
                            continue;
                        }

                        console.log(`    ✅ Found ${allFixturesFromApi.length} fixtures for season ${season}`);

                        // Filter fixtures to only include targeted leagues (should already be included but double-check)
                        const fixturesFromApi = filterByTargetedLeagues(allFixturesFromApi);

                        if (fixturesFromApi.length === 0) {
                            console.log(`    ⚠️  No fixtures after filtering for targeted leagues`);
                            continue;
                        }

                        const now = new Date();
                        const bulkOps = await createFixtureBulkOps(fixturesFromApi, now);

                        if (bulkOps.length > 0) {
                            const result = await collection.bulkWrite(bulkOps);
                            console.log(`    💾 Season ${season}: Upserted ${result.upsertedCount}, Modified ${result.modifiedCount}`);

                            totalUpserted += result.upsertedCount;
                            totalModified += result.modifiedCount;

                            // Invalidate cache for updated fixtures
                            if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                                const fixtureIds = fixturesFromApi.map(fixture => fixture.fixture.id);
                                await invalidateFixtureCaches(fixtureIds);

                                // Also invalidate league-based caches
                                try {
                                    const redisClient = getRedisClient();
                                    await redisClient.del(`fixtures:league:${leagueId}:season:${season}`);
                                    console.log(`    🗑️  Cache invalidated for league ${leagueId} season ${season}`);
                                } catch (error) {
                                    console.error(`    ❌ Error invalidating cache for league ${leagueId}:`, error);
                                }
                            }

                            // Fetch detailed data for finished fixtures
                            const finishedFixtures = fixturesFromApi.filter(fixture =>
                                ['FT', 'AET', 'PEN', 'WO', 'AWD'].includes(fixture.fixture.status.short)
                            );

                            if (finishedFixtures.length > 0) {
                                console.log(`    🔍 Fetching detailed data for ${finishedFixtures.length} finished fixtures...`);
                                const finishedIds = finishedFixtures.map(f => f.fixture.id);
                                await fetchAndUpdateFixturesById(finishedIds);

                                // Process tips for finished fixtures
                                if (!process.env.STANDALONE_JOB) {
                                    await processFinishedFixtureTips(finishedIds);
                                }
                            }
                        }

                        // Add delay between seasons to respect API rate limits
                        await delay(1000);

                    } catch (error) {
                        console.error(`    ❌ Error fetching season ${season} for league ${leagueId}:`, error);
                        // Continue with next season instead of failing completely
                        continue;
                    }
                }

                processedLeagues++;
                console.log(`  ✅ Completed ${leagueName} (${processedLeagues}/${leaguesToProcess.length})`);

                // Add delay between leagues to respect API rate limits
                await delay(2000);

            } catch (error) {
                console.error(`❌ Error processing league ${leagueId}:`, error);
                // Continue with next league instead of failing completely
                continue;
            }
        }

        console.log(`🎉 fetchAndUpdateSeasonFixtures job finished.`);
        console.log(`📊 Processed: ${processedLeagues}/${leaguesToProcess.length} leagues`);
        console.log(`📈 Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('❌ Error in fetchAndUpdateSeasonFixtures job:', error);
    }
}
