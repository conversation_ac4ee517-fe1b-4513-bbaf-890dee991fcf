import { fetchTransfers } from '../services/transferService';
import { getTransfersCollection, Transfer, createTransferId } from '../models/Transfer';
import { getTeamsCollection } from '../models/Team';
import { getPlayersCollection } from '../models/Player';
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Fetch transfers for active teams
export async function fetchAndUpdateTeamTransfers() {
    console.log('Starting fetchAndUpdateTeamTransfers job...');
    try {
        const teamsCollection = getTeamsCollection();
        const transfersCollection = getTransfersCollection();
        const now = new Date();

        // Get teams from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Find teams that are associated with targeted leagues
        const teamsInTargetedLeagues = await leaguesCollection.aggregate([
            { $match: { _id: { $in: targetedLeagues } } },
            { $lookup: {
                from: 'teams',
                localField: '_id',
                foreignField: 'league.id',
                as: 'teams'
            }},
            { $unwind: '$teams' },
            { $project: { 'teamId': '$teams._id' } }
        ]).toArray();

        const teamIds = teamsInTargetedLeagues.map(t => t.teamId);

        // Get all teams from targeted leagues with numeric IDs
        const teams = await teamsCollection.find({
            _id: { $in: teamIds, $type: 16 } // 16 is the BSON type code for Int32
        }).toArray();

        if (!teams || teams.length === 0) {
            console.log('No teams found to fetch transfers for.');
            return;
        }

        console.log(`Found ${teams.length} teams.`);

        let totalUpserted = 0;
        let totalModified = 0;

        for (const team of teams) {
            const teamId = team._id;

            console.log(`Fetching transfers for Team ID: ${teamId}`);

            try {
                const transfers = await fetchTransfers({ team: teamId });

                if (!transfers || transfers.length === 0) {
                    console.log(`No transfer information received for Team ${teamId}.`);
                    continue;
                }

                console.log(`Received ${transfers.length} transfer record(s) for Team ${teamId}.`);

                const bulkOps: AnyBulkWriteOperation<Transfer>[] = [];

                for (const transfer of transfers) {
                    const playerId = transfer.player.id;
                    const updateDate = transfer.update;
                    const transferId = createTransferId(playerId, updateDate);

                    // Create transfer document
                    const transferDoc: Transfer = {
                        _id: transferId,
                        player: transfer.player,
                        update: transfer.update,
                        transfers: transfer.transfers,
                        lastUpdated: now
                    };

                    bulkOps.push({
                        updateOne: {
                            filter: { _id: transferId },
                            update: { $set: transferDoc },
                            upsert: true
                        }
                    });
                }

                if (bulkOps.length > 0) {
                    const result = await transfersCollection.bulkWrite(bulkOps);
                    console.log(`Team ${teamId} transfers updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Add a delay between teams to respect API rate limits
                await delay(1000);

            } catch (teamError) {
                console.error(`Error fetching transfers for Team ${teamId}:`, teamError);
                // Continue to the next team even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdateTeamTransfers job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateTeamTransfers job:', error);
    }
}

// Fetch transfers for top players
export async function fetchAndUpdatePlayerTransfers() {
    console.log('Starting fetchAndUpdatePlayerTransfers job...');
    try {
        const playersCollection = getPlayersCollection();
        const transfersCollection = getTransfersCollection();
        const now = new Date();

        // Get players from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Find teams that are associated with targeted leagues
        const teamsInTargetedLeagues = await leaguesCollection.aggregate([
            { $match: { _id: { $in: targetedLeagues } } },
            { $lookup: {
                from: 'teams',
                localField: '_id',
                foreignField: 'league.id',
                as: 'teams'
            }},
            { $unwind: '$teams' },
            { $project: { 'teamId': '$teams._id' } }
        ]).toArray();

        const teamIds = teamsInTargetedLeagues.map(t => t.teamId);

        // Get top players from teams in targeted leagues
        const topPlayers = await playersCollection.find({
            'statistics.*.team.id': { $in: teamIds }
        })
            .sort({ 'profile.marketValue': -1 }) // Sort by market value if available
            .limit(100) // Limit to top 100 players
            .toArray();

        if (!topPlayers || topPlayers.length === 0) {
            console.log('No players found to fetch transfers for.');
            return;
        }

        console.log(`Found ${topPlayers.length} players.`);

        let totalUpserted = 0;
        let totalModified = 0;

        for (const player of topPlayers) {
            const playerId = player._id;

            console.log(`Fetching transfers for Player ID: ${playerId}`);

            try {
                const transfers = await fetchTransfers({ player: playerId });

                if (!transfers || transfers.length === 0) {
                    console.log(`No transfer information received for Player ${playerId}.`);
                    continue;
                }

                console.log(`Received ${transfers.length} transfer record(s) for Player ${playerId}.`);

                const bulkOps: AnyBulkWriteOperation<Transfer>[] = [];

                for (const transfer of transfers) {
                    const updateDate = transfer.update;
                    const transferId = createTransferId(playerId, updateDate);

                    // Create transfer document
                    const transferDoc: Transfer = {
                        _id: transferId,
                        player: transfer.player,
                        update: transfer.update,
                        transfers: transfer.transfers,
                        lastUpdated: now
                    };

                    bulkOps.push({
                        updateOne: {
                            filter: { _id: transferId },
                            update: { $set: transferDoc },
                            upsert: true
                        }
                    });
                }

                if (bulkOps.length > 0) {
                    const result = await transfersCollection.bulkWrite(bulkOps);
                    console.log(`Player ${playerId} transfers updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Add a delay between players to respect API rate limits
                await delay(1000);

            } catch (playerError) {
                console.error(`Error fetching transfers for Player ${playerId}:`, playerError);
                // Continue to the next player even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdatePlayerTransfers job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdatePlayerTransfers job:', error);
    }
}
