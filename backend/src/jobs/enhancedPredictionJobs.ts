/**
 * Enhanced Prediction Background Jobs
 * 
 * Implements automated background jobs for:
 * 1. Training Dixon-Coles models (calculating team strengths and league parameters)
 * 2. Generating enhanced predictions for upcoming fixtures
 * 3. Refreshing models with latest match data
 */

import { AnyBulkWriteOperation } from 'mongodb';
import { getFixturesCollection } from '../models/Fixture';
import { getLeaguesCollection } from '../models/League';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { EnhancedPrediction, TeamStrengthDocument, LeagueParametersDocument } from '../models/EnhancedPrediction';
import { EnhancedPredictionService } from '../services/enhancedPredictionService';
import { TeamStrengthService, MatchData } from '../services/teamStrengthService';
import { targetedLeagues } from '../config/targetedLeagues';
import { getRedisClient } from '../config/redis';
import connectDB from '../config/database';
import dayjs from 'dayjs';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to create null prediction for jobs
function createNullPredictionForJob(
    fixtureId: number,
    homeTeam: any,
    awayTeam: any,
    league: any,
    fixtureDate: Date
): EnhancedPrediction {
    return {
        _id: fixtureId,
        fixture: {
            id: fixtureId,
            date: fixtureDate,
            status: 'NS'
        },
        league: {
            id: league.id,
            name: league.name,
            country: league.country,
            logo: league.logo,
            flag: league.flag,
            season: league.season,
            round: league.round,
            standings: league.standings
        },
        teams: {
            home: {
                id: homeTeam.id,
                name: homeTeam.name,
                logo: homeTeam.logo
            },
            away: {
                id: awayTeam.id,
                name: awayTeam.name,
                logo: awayTeam.logo
            }
        },
        predictions: {
            correctScore: {
                mostLikely: { home: 0, away: 0, probability: 0 },
                top5Scores: [],
                matrix: {},
                confidence: 'low' as const,
                algorithm: 'dixon-coles' as const
            },
            bothTeamsToScore: {
                prediction: false,
                probability: 0,
                confidence: 'low' as const
            },
            matchOutcome: {
                homeWin: 0,
                draw: 0,
                awayWin: 0,
                confidence: 'low' as const
            },
            expectedGoals: {
                home: 0,
                away: 0,
                total: 0,
                confidence: 'low' as const
            },
            goalDistribution: {
                under05: 0, under15: 0, under25: 0, under35: 0,
                over05: 0, over15: 0, over25: 0, over35: 0
            }
        },
        dixonColesParams: {
            homeAttack: 0,
            homeDefense: 0,
            awayAttack: 0,
            awayDefense: 0,
            homeAdvantage: 0,
            rho: 0,
            leagueId: league.id,
            calculatedAt: new Date()
        },
        metadata: {
            algorithm: 'dixon-coles' as const,
            dataSource: 'historical' as const,
            confidence: 0,
            processingTime: 0,
            lastUpdated: new Date(),
            modelVersion: '2.0',
            matchesUsedForTraining: 0,
            eloData: null
        },
        createdAt: new Date(),
        lastUpdated: new Date()
    };
}

/**
 * Train Dixon-Coles models for all active leagues
 * Calculates and caches team strengths and league parameters
 * This job should be run daily to keep models current
 */
export async function trainDixonColesModelsForActiveLeagues() {
    console.log('Starting trainDixonColesModelsForActiveLeagues job...');
    try {
        const db = await connectDB();
        const leaguesCollection = getLeaguesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const now = new Date();
        let totalLeaguesProcessed = 0;
        let totalLeaguesUpdated = 0;
        let totalLeaguesSkipped = 0;

        // 1. Get leagues with prediction coverage and active seasons
        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.predictions': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(league => league._id);
        console.log(`Found ${leagueIdsWithCoverage.length} leagues with prediction coverage.`);

        if (leagueIdsWithCoverage.length === 0) {
            console.log('No leagues with prediction coverage found.');
            return;
        }

        // 2. Get active leagues with current seasons
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: leagueIdsWithCoverage },
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found with prediction coverage.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active leagues to train models for.`);

        // 3. Process leagues in batches to avoid overwhelming the system
        const BATCH_SIZE = 5; // Smaller batch size for intensive calculations
        const leagueBatches = [];

        for (let i = 0; i < activeLeagues.length; i += BATCH_SIZE) {
            leagueBatches.push(activeLeagues.slice(i, i + BATCH_SIZE));
        }

        console.log(`Processing ${leagueBatches.length} batches of leagues...`);

        for (let batchIndex = 0; batchIndex < leagueBatches.length; batchIndex++) {
            const batch = leagueBatches[batchIndex];
            console.log(`Processing batch ${batchIndex + 1}/${leagueBatches.length} (${batch.length} leagues)...`);

            for (const league of batch) {
                try {
                    totalLeaguesProcessed++;
                    const leagueId = league._id;

                    console.log(`Training model for league ${leagueId}...`);

                    // Check if we already have recent team strengths (less than 6 hours old)
                    const existingLeagueParams = await db.collection<LeagueParametersDocument>('league_parameters')
                        .findOne({ _id: leagueId });

                    const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                    
                    if (existingLeagueParams && existingLeagueParams.lastUpdated > sixHoursAgo) {
                        console.log(`Skipping league ${leagueId}: model updated recently.`);
                        totalLeaguesSkipped++;
                        continue;
                    }

                    // Get historical match data for the league
                    const cutoffDate = new Date(now);
                    cutoffDate.setMonth(cutoffDate.getMonth() - 18); // 18 months of data

                    const fixtures = await db.collection('fixtures').find({
                        'league.id': leagueId,
                        'fixture.status.short': 'FT', // Only finished matches
                        'date': { $gte: cutoffDate, $lt: now },
                        'goals.home': { $ne: null },
                        'goals.away': { $ne: null }
                    }).toArray();

                    if (fixtures.length < 10) {
                        console.log(`Insufficient match data for league ${leagueId}: ${fixtures.length} matches (minimum 10)`);
                        continue;
                    }

                    // Convert to MatchData format
                    const matchData: MatchData[] = fixtures.map(fixture => ({
                        homeTeamId: fixture.teams.home.id,
                        awayTeamId: fixture.teams.away.id,
                        homeGoals: fixture.goals.home,
                        awayGoals: fixture.goals.away,
                        date: new Date(fixture.date),
                        leagueId: fixture.league.id
                    }));

                    // Calculate team strengths and league parameters
                    const { teamStrengths, leagueParams } = await TeamStrengthService.calculateFromMatches(
                        matchData,
                        now
                    );

                    // Cache the results using the existing methods
                    await cacheTeamStrengths(teamStrengths, leagueId);
                    await cacheLeagueParameters({ ...leagueParams, leagueId });

                    console.log(`Model trained for league ${leagueId}: ${teamStrengths.size} teams, ${leagueParams.matchesAnalyzed} matches analyzed`);
                    totalLeaguesUpdated++;

                    // Add delay between leagues to avoid overwhelming the system
                    await delay(1000);

                } catch (leagueError) {
                    console.error(`Error training model for league ${league._id}:`, leagueError);
                    // Continue to the next league even if one fails
                    await delay(2000); // Longer delay after an error
                }
            }

            // Add delay between batches
            await delay(3000);
        }

        console.log(`trainDixonColesModelsForActiveLeagues job completed.`);
        console.log(`Total leagues processed: ${totalLeaguesProcessed}`);
        console.log(`Total leagues updated: ${totalLeaguesUpdated}`);
        console.log(`Total leagues skipped: ${totalLeaguesSkipped}`);

    } catch (error) {
        console.error('Error in trainDixonColesModelsForActiveLeagues job:', error);
    }
}

/**
 * Generate enhanced predictions for upcoming fixtures
 * This job should be run daily to ensure predictions are available for upcoming matches
 * 
 * @param days Number of days to look ahead for upcoming fixtures (default: 7)
 */
export async function generateEnhancedPredictionsForUpcomingFixtures(days: number = 7) {
    console.log(`Starting generateEnhancedPredictionsForUpcomingFixtures job for the next ${days} days...`);
    try {
        const db = await connectDB();
        const fixturesCollection = getFixturesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const redisClient = getRedisClient();
        const now = new Date();
        let totalProcessed = 0;
        let totalGenerated = 0;
        let totalSkipped = 0;
        let totalFailed = 0;

        // 1. Get leagues with prediction coverage
        // First, let's check what we have in the coverage collection
        const totalCoverage = await coverageCollection.countDocuments();
        const coverageWithPredictions = await coverageCollection.countDocuments({ 'coverage.predictions': true });
        console.log(`📊 Total coverage documents: ${totalCoverage}, with predictions: ${coverageWithPredictions}`);

        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.predictions': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(league => league._id);
        console.log(`Found ${leagueIdsWithCoverage.length} targeted leagues with prediction coverage.`);

        // If no targeted leagues have coverage, let's try without the targetedLeagues filter
        if (leagueIdsWithCoverage.length === 0) {
            console.log('No targeted leagues with prediction coverage found. Trying all leagues with coverage...');
            const allLeaguesWithCoverage = await coverageCollection.find(
                { 'coverage.predictions': true },
                { projection: { _id: 1 } }
            ).limit(50).toArray(); // Limit to 50 for testing

            const allLeagueIds = allLeaguesWithCoverage.map(league => league._id);
            console.log(`Found ${allLeagueIds.length} total leagues with prediction coverage (using first 50).`);

            if (allLeagueIds.length === 0) {
                console.log('No leagues with prediction coverage found at all.');
                return;
            }

            // Use all leagues with coverage instead of targeted leagues
            leagueIdsWithCoverage.push(...allLeagueIds);
        }

        // 2. Find upcoming fixtures in leagues with prediction coverage
        const startDate = dayjs().startOf('day').toDate();
        const endDate = dayjs().add(days, 'day').endOf('day').toDate();

        console.log(`📅 Searching for fixtures between ${startDate.toISOString()} and ${endDate.toISOString()}`);
        console.log(`📊 Searching in ${leagueIdsWithCoverage.length} leagues: ${leagueIdsWithCoverage.slice(0, 10).join(', ')}${leagueIdsWithCoverage.length > 10 ? '...' : ''}`);

        // First, let's check total fixtures in date range
        const totalFixturesInRange = await fixturesCollection.countDocuments({
            'fixture.date': { $gte: startDate, $lte: endDate },
            'fixture.status.short': 'NS'
        });
        console.log(`📊 Total NS fixtures in date range: ${totalFixturesInRange}`);

        // Check fixtures in our target leagues
        const fixturesInTargetLeagues = await fixturesCollection.countDocuments({
            'league.id': { $in: leagueIdsWithCoverage },
            'fixture.date': { $gte: startDate, $lte: endDate },
            'fixture.status.short': 'NS'
        });
        console.log(`📊 NS fixtures in target leagues: ${fixturesInTargetLeagues}`);

        const upcomingFixtures = await fixturesCollection.find({
            'league.id': { $in: leagueIdsWithCoverage },
            'fixture.date': { $gte: startDate, $lte: endDate },
            'fixture.status.short': 'NS' // Not Started fixtures only
        }, {
            projection: {
                _id: 1,
                'teams.home.id': 1,
                'teams.away.id': 1,
                'league.id': 1,
                'fixture.date': 1
            }
        }).toArray();

        if (upcomingFixtures.length === 0) {
            console.log(`❌ No upcoming fixtures found for the next ${days} days in leagues with prediction coverage.`);

            // Debug: Let's see what leagues actually have fixtures
            const leaguesWithFixtures = await fixturesCollection.distinct('league.id', {
                'fixture.date': { $gte: startDate, $lte: endDate },
                'fixture.status.short': 'NS'
            });
            console.log(`📊 Leagues with NS fixtures in date range: ${leaguesWithFixtures.length}`);
            console.log(`📊 First 10 leagues with fixtures: ${leaguesWithFixtures.slice(0, 10).join(', ')}`);

            return;
        }

        console.log(`Found ${upcomingFixtures.length} upcoming fixtures for prediction generation.`);

        // 3. Process fixtures in batches to avoid overwhelming the system
        const BATCH_SIZE = 10;
        const fixtureBatches = [];

        for (let i = 0; i < upcomingFixtures.length; i += BATCH_SIZE) {
            fixtureBatches.push(upcomingFixtures.slice(i, i + BATCH_SIZE));
        }

        console.log(`Processing ${fixtureBatches.length} batches of fixtures...`);

        for (let batchIndex = 0; batchIndex < fixtureBatches.length; batchIndex++) {
            const batch = fixtureBatches[batchIndex];
            console.log(`Processing batch ${batchIndex + 1}/${fixtureBatches.length} (${batch.length} fixtures)...`);

            const bulkOps: AnyBulkWriteOperation<EnhancedPrediction>[] = [];

            for (const fixture of batch) {
                try {
                    totalProcessed++;
                    const fixtureId = fixture._id;

                    // Check if we already have a recent prediction (less than 6 hours old)
                    const existingPrediction = await db.collection<EnhancedPrediction>('enhanced_predictions_v2')
                        .findOne({ _id: fixtureId }, { projection: { lastUpdated: 1 } });

                    const sixHoursAgo = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                    
                    if (existingPrediction && existingPrediction.lastUpdated > sixHoursAgo) {
                        console.log(`Skipping fixture ${fixtureId}: prediction updated recently.`);
                        totalSkipped++;
                        continue;
                    }

                    // Generate enhanced prediction
                    const prediction = await EnhancedPredictionService.generatePrediction(
                        fixtureId,
                        fixture.teams.home.id,
                        fixture.teams.away.id,
                        fixture.league.id,
                        new Date(fixture.fixture.date)
                    );

                    if (prediction) {
                        // Add to bulk operations
                        bulkOps.push({
                            updateOne: {
                                filter: { _id: fixtureId },
                                update: { $set: prediction },
                                upsert: true
                            }
                        });

                        // Cache in Redis
                        const cacheKey = `enhanced_prediction:${fixtureId}`;
                        await redisClient.setex(cacheKey, 60 * 60 * 6, JSON.stringify(prediction)); // 6 hours

                        totalGenerated++;
                    } else {
                        // Create null prediction for insufficient data
                        const nullPrediction = createNullPredictionForJob(
                            fixtureId,
                            fixture.teams.home,
                            fixture.teams.away,
                            fixture.league,
                            new Date(fixture.fixture.date)
                        );

                        // Add null prediction to bulk operations
                        bulkOps.push({
                            updateOne: {
                                filter: { _id: fixtureId },
                                update: { $set: nullPrediction },
                                upsert: true
                            }
                        });

                        // Cache null prediction (shorter TTL)
                        const cacheKey = `enhanced_prediction:${fixtureId}`;
                        await redisClient.setex(cacheKey, 60 * 30, JSON.stringify(nullPrediction)); // 30 minutes

                        console.log(`Created null prediction for fixture ${fixtureId} due to insufficient data`);
                        totalGenerated++; // Count null predictions as generated
                    }

                    // Add delay between predictions to avoid overwhelming the system
                    await delay(200);

                } catch (error) {
                    console.error(`Error processing prediction for fixture ${fixture._id}:`, error);
                    totalFailed++;
                }
            }

            // Execute bulk operations if any
            if (bulkOps.length > 0) {
                try {
                    const result = await db.collection<EnhancedPrediction>('enhanced_predictions_v2').bulkWrite(bulkOps);
                    console.log(`Batch ${batchIndex + 1} completed. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                } catch (bulkError) {
                    console.error(`Error executing bulk operations for batch ${batchIndex + 1}:`, bulkError);
                }
            }

            // Add delay between batches
            await delay(2000);
        }

        console.log(`generateEnhancedPredictionsForUpcomingFixtures job completed.`);
        console.log(`Total fixtures processed: ${totalProcessed}`);
        console.log(`Total predictions generated: ${totalGenerated}`);
        console.log(`Total fixtures skipped: ${totalSkipped}`);
        console.log(`Total failures: ${totalFailed}`);

    } catch (error) {
        console.error('Error in generateEnhancedPredictionsForUpcomingFixtures job:', error);
    }
}

/**
 * Refresh Dixon-Coles models by recalculating team strengths for leagues with recent matches
 * This job should be run weekly to keep models current as new match results come in
 */
export async function refreshDixonColesModels() {
    console.log('Starting refreshDixonColesModels job...');
    try {
        const db = await connectDB();
        const now = new Date();
        let totalLeaguesProcessed = 0;
        let totalLeaguesRefreshed = 0;

        // Find leagues that have had matches in the last 7 days
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

        const recentMatches = await db.collection('fixtures').aggregate([
            {
                $match: {
                    'fixture.status.short': 'FT',
                    'date': { $gte: sevenDaysAgo, $lt: now },
                    'league.id': { $in: targetedLeagues }
                }
            },
            {
                $group: {
                    _id: '$league.id',
                    matchCount: { $sum: 1 },
                    latestMatch: { $max: '$date' }
                }
            },
            {
                $match: {
                    matchCount: { $gte: 3 } // Only refresh if at least 3 matches in the last week
                }
            }
        ]).toArray();

        if (recentMatches.length === 0) {
            console.log('No leagues with sufficient recent matches found for model refresh.');
            return;
        }

        console.log(`Found ${recentMatches.length} leagues with recent matches for model refresh.`);

        // Process each league
        for (const leagueData of recentMatches) {
            try {
                totalLeaguesProcessed++;
                const leagueId = leagueData._id;

                console.log(`Refreshing model for league ${leagueId} (${leagueData.matchCount} recent matches)...`);

                // Force recalculation by removing existing cache
                await db.collection('league_parameters').deleteOne({ _id: leagueId });
                await db.collection('team_strengths').deleteMany({ leagueId });

                // Get historical match data for the league
                const cutoffDate = new Date(now);
                cutoffDate.setMonth(cutoffDate.getMonth() - 18); // 18 months of data

                const fixtures = await db.collection('fixtures').find({
                    'league.id': leagueId,
                    'fixture.status.short': 'FT',
                    'date': { $gte: cutoffDate, $lt: now },
                    'goals.home': { $ne: null },
                    'goals.away': { $ne: null }
                }).toArray();

                if (fixtures.length < 10) {
                    console.log(`Insufficient match data for league ${leagueId}: ${fixtures.length} matches`);
                    continue;
                }

                // Convert to MatchData format
                const matchData: MatchData[] = fixtures.map(fixture => ({
                    homeTeamId: fixture.teams.home.id,
                    awayTeamId: fixture.teams.away.id,
                    homeGoals: fixture.goals.home,
                    awayGoals: fixture.goals.away,
                    date: new Date(fixture.date),
                    leagueId: fixture.league.id
                }));

                // Recalculate team strengths and league parameters
                const { teamStrengths, leagueParams } = await TeamStrengthService.calculateFromMatches(
                    matchData,
                    now
                );

                // Cache the refreshed results
                await cacheTeamStrengths(teamStrengths, leagueId);
                await cacheLeagueParameters({ ...leagueParams, leagueId });

                console.log(`Model refreshed for league ${leagueId}: ${teamStrengths.size} teams, ${leagueParams.matchesAnalyzed} matches analyzed`);
                totalLeaguesRefreshed++;

                // Invalidate related prediction caches
                const redisClient = getRedisClient();
                const keys = await redisClient.keys(`enhanced_prediction:*`);
                const leagueKeys = [];

                for (const key of keys) {
                    // Check if this prediction is for the refreshed league
                    const fixtureId = key.split(':')[1];
                    const fixture = await db.collection('fixtures').findOne(
                        { _id: parseInt(fixtureId) as any },
                        { projection: { 'league.id': 1 } }
                    );

                    if (fixture && fixture.league.id === leagueId) {
                        leagueKeys.push(key);
                    }
                }

                if (leagueKeys.length > 0) {
                    await redisClient.del(...leagueKeys);
                    console.log(`Invalidated ${leagueKeys.length} cached predictions for league ${leagueId}`);
                }

                // Add delay between leagues
                await delay(2000);

            } catch (leagueError) {
                console.error(`Error refreshing model for league ${leagueData._id}:`, leagueError);
                await delay(3000); // Longer delay after an error
            }
        }

        console.log(`refreshDixonColesModels job completed.`);
        console.log(`Total leagues processed: ${totalLeaguesProcessed}`);
        console.log(`Total leagues refreshed: ${totalLeaguesRefreshed}`);

    } catch (error) {
        console.error('Error in refreshDixonColesModels job:', error);
    }
}

/**
 * Helper function to cache team strengths in database
 * Replicates the logic from EnhancedPredictionService.cacheTeamStrengths
 */
async function cacheTeamStrengths(
    teamStrengths: Map<number, any>,
    leagueId: number
): Promise<void> {
    const db = await connectDB();
    const collection = db.collection('team_strengths');

    const bulkOps = [];
    for (const [teamId, strength] of teamStrengths) {
        const doc: TeamStrengthDocument = {
            _id: `${teamId}_${leagueId}`,
            teamId,
            leagueId,
            strength: {
                attack: strength.attack,
                defense: strength.defense
            },
            statistics: {
                matchesPlayed: strength.matchesPlayed,
                goalsScored: 0, // TODO: Calculate from historical data
                goalsConceded: 0, // TODO: Calculate from historical data
                homeMatchesPlayed: 0, // TODO: Calculate from historical data
                awayMatchesPlayed: 0 // TODO: Calculate from historical data
            },
            form: {
                last5Matches: [], // TODO: Calculate from historical data
                recentForm: '' // TODO: Calculate from historical data
            },
            lastUpdated: new Date(),
            calculatedAt: new Date()
        };

        bulkOps.push({
            updateOne: {
                filter: { _id: doc._id },
                update: { $set: doc },
                upsert: true
            }
        });
    }

    if (bulkOps.length > 0) {
        await collection.bulkWrite(bulkOps);
    }
}

/**
 * Helper function to cache league parameters in database
 * Replicates the logic from EnhancedPredictionService.cacheLeagueParameters
 */
async function cacheLeagueParameters(leagueParams: any): Promise<void> {
    const db = await connectDB();
    const collection = db.collection('league_parameters');

    const doc: LeagueParametersDocument = {
        _id: leagueParams.leagueId,
        leagueId: leagueParams.leagueId,
        parameters: {
            homeAdvantage: leagueParams.homeAdvantage,
            averageGoalsPerGame: leagueParams.averageGoalsPerGame,
            rho: leagueParams.rho
        },
        statistics: {
            matchesAnalyzed: leagueParams.matchesAnalyzed,
            dateRange: {
                from: new Date(), // TODO: Calculate actual range
                to: new Date()
            },
            totalGoals: 0, // TODO: Calculate from data
            homeWins: 0, // TODO: Calculate from data
            draws: 0, // TODO: Calculate from data
            awayWins: 0 // TODO: Calculate from data
        },
        lastUpdated: new Date(),
        calculatedAt: new Date()
    };

    await collection.updateOne(
        { _id: doc._id as any },
        { $set: doc },
        { upsert: true }
    );
}
