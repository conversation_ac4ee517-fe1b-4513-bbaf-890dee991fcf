import { getUsersCollection, removeDeviceToken } from '../models/User';
import { getFixturesCollection } from '../models/Fixture';
import { <PERSON>ronJob } from 'cron';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import apn from 'node-apn';
import dotenv from 'dotenv';
import path from 'path';

dotenv.config();
dayjs.extend(utc);
dayjs.extend(timezone);

// --- APNs Provider Configuration ---
let apnProvider: apn.Provider | null = null;

// Initialize the APNs provider
function initializeAPNsProvider(): apn.Provider {
  if (apnProvider) return apnProvider;

  const options: apn.ProviderOptions = {
    token: {
      // Path to your .p8 key file (you'll need to obtain this from Apple Developer account)
      key: process.env.APNS_KEY_PATH || path.join(__dirname, '../../certs/AuthKey_F8D572C22G.p8'),
      keyId: process.env.APNS_KEY_ID || 'F8D572C22G', // Your Key ID from Apple Developer account
      teamId: process.env.APNS_TEAM_ID || '5NEAGT64RQ', // Your Team ID from Apple Developer account
    },
    production: process.env.NODE_ENV === 'production' // Use development APNs server in non-production environments
  };

  try {
    apnProvider = new apn.Provider(options);
    console.log('APNs provider initialized successfully');
    return apnProvider;
  } catch (error) {
    console.error('Failed to initialize APNs provider:', error);
    // Fallback to logging only in case of initialization failure
    return null as unknown as apn.Provider; // This is a hack, but we'll handle the null case
  }
}

// Send push notification via APNs
async function sendPushNotification(token: string, title: string, body: string, userId?: string): Promise<boolean> {
  // If we're in development mode without proper certificates, just log
  if (process.env.MOCK_NOTIFICATIONS === 'true') {
    console.log(`[PUSH SENT - Mock Mode] To: ${token}, Title: "${title}", Body: "${body}"`);
    return true;
  }

  try {
    const provider = initializeAPNsProvider();
    if (!provider) {
      console.log(`[PUSH FAILED - No Provider] To: ${token}`);
      return false;
    }

    // Create notification
    const notification = new apn.Notification();
    notification.expiry = Math.floor(Date.now() / 1000) + 3600; // Expires in 1 hour
    notification.badge = 1;
    notification.sound = 'ping.aiff';
    notification.alert = {
      title: title,
      body: body
    };
    notification.topic = process.env.APNS_BUNDLE_ID || 'com.kickoffscore.kickoffscore'; // Your app's bundle ID

    // Send notification
    const result = await provider.send(notification, token);

    // Check for failures
    if (result.failed.length > 0) {
      const failure = result.failed[0];
      console.error(`[PUSH FAILED] To: ${token}, Reason: ${failure.response?.reason}`);

      // Handle invalid tokens
      if (failure.response?.reason === 'BadDeviceToken' ||
          failure.response?.reason === 'Unregistered' ||
          failure.response?.reason === 'DeviceTokenNotForTopic') {
        if (userId) {
          console.log(`Removing invalid token ${token} for user ${userId}`);
          await removeDeviceToken(userId, token);
        }
      }
      return false;
    }

    console.log(`[PUSH SENT] To: ${token}, Title: "${title}", Body: "${body}"`);
    return true;
  } catch (error) {
    console.error(`[PUSH ERROR] To: ${token}:`, error);
    return false;
  }
}

// --- Job Function ---
export async function sendUpcomingFixtureNotifications() {
  console.log('Running job: sendUpcomingFixtureNotifications...');

  const usersCollection = getUsersCollection();
  const fixturesCollection = getFixturesCollection(); // Make sure this function exists in Fixture model

  const today = dayjs().tz('Europe/London'); // Use a relevant timezone
  const tomorrowStart = today.add(1, 'day').startOf('day');
  const tomorrowEnd = today.add(1, 'day').endOf('day');

  console.log(`Checking for fixtures between ${tomorrowStart.toISOString()} and ${tomorrowEnd.toISOString()}`);

  try {
    // 1. Find fixtures happening tomorrow
    const upcomingFixtures = await fixturesCollection.find({
      'fixture.date': {
        $gte: tomorrowStart.toDate(),
        $lte: tomorrowEnd.toDate()
      },
      // Add any other relevant status checks, e.g., 'NS' (Not Started)
      'fixture.status.short': 'NS'
    }).toArray();

    if (upcomingFixtures.length === 0) {
      console.log('No upcoming fixtures found for tomorrow.');
      return;
    }

    console.log(`Found ${upcomingFixtures.length} upcoming fixtures.`);

    // 2. Find users who favorited teams in these fixtures and have device tokens
    for (const fixture of upcomingFixtures) {
      const teamIds = [fixture.teams.home.id, fixture.teams.away.id];
      const fixtureInfo = `${fixture.teams.home.name} vs ${fixture.teams.away.name} at ${dayjs(fixture.fixture.date).tz('Europe/London').format('HH:mm')}`; // Adjust formatting as needed

      // Find users who favorited teams, have tokens, AND have notifications enabled
      const usersToNotify = await usersCollection.find({
        'favorites.teams': { $in: teamIds }, // Favorited team is playing
        'deviceTokens.0': { $exists: true }, // Has at least one device token
        'preferences.notifications.upcomingFixtures': true // Has preference enabled
      }).project({ _id: 1, deviceTokens: 1 }).toArray(); // Only get necessary fields

      if (usersToNotify.length > 0) {
        console.log(`Found ${usersToNotify.length} users with notifications enabled to notify for fixture ID ${fixture.fixture.id} (${fixtureInfo})`);

        // 3. Send notifications using APNs
        const notificationTitle = 'Upcoming Match Reminder';
        const notificationBody = `Reminder: ${fixtureInfo} is happening tomorrow!`;

        for (const user of usersToNotify) {
          const userId = user._id.toString();
          console.log(`-- Sending notifications for User ${userId}`);

          // Track success/failure stats
          let successCount = 0;
          let failureCount = 0;

          for (const token of user.deviceTokens) {
            try {
              // Pass userId to allow automatic token cleanup
              const success = await sendPushNotification(token, notificationTitle, notificationBody, userId);
              if (success) {
                successCount++;
              } else {
                failureCount++;
              }
            } catch (error) {
              console.error(`---- Failed to send notification to token ${token} for user ${userId}:`, error);
              failureCount++;
            }
          }

          console.log(`---- Notification results for User ${userId}: ${successCount} sent, ${failureCount} failed`);
        }
      }
    }

    console.log('Finished job: sendUpcomingFixtureNotifications.');

  } catch (error) {
    console.error('Error running sendUpcomingFixtureNotifications job:', error);
  }
}

// Example: Schedule to run daily at 7:00 AM London time
// Adjust the cron time string as needed ('0 7 * * *')
// See: https://crontab.guru/
export const upcomingFixtureNotificationJob = new CronJob(
  '0 7 * * *', // Cron time string: second(0-59) minute(0-59) hour(0-23) dayOfMonth(1-31) month(1-12) dayOfWeek(0-7)
  async () => {
    console.log('Triggering scheduled job: sendUpcomingFixtureNotifications');
    await sendUpcomingFixtureNotifications();
  },
  null, // onComplete
  false, // start automatically? Set to true later or start in scheduler.ts
  'Europe/London' // Timezone
);