import { fetchCountries } from '../services/apiFootball';
import { getCountriesCollection, Country } from '../models/Country';
import { AnyBulkWriteOperation } from 'mongodb'; // Corrected import type

export async function fetchAndUpdateCountries() {
    console.log('Starting fetchAndUpdateCountries job...');
    try {
        const countriesFromApi = await fetchCountries(); // Fetch all countries

        if (!countriesFromApi || countriesFromApi.length === 0) {
            console.log('No countries received from API.');
            return;
        }

        const collection = getCountriesCollection();
        const bulkOps: AnyBulkWriteOperation<Country>[] = []; // Use the corrected type
        const now = new Date();

        for (const countryApi of countriesFromApi) {
            // Use country name as the unique identifier for upsert
            const filter = { name: countryApi.name };
            const updateDoc: Country = {
                ...countryApi, // Spread the fields from the API response
                lastUpdated: now,
            };

            bulkOps.push({
                updateOne: {
                    filter: filter,
                    update: { $set: updateDoc },
                    upsert: true, // Insert if not found, update if found
                },
            });
        }

        if (bulkOps.length > 0) {
            const result = await collection.bulkWrite(bulkOps);
            console.log(`fetchAndUpdateCountries job finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
        } else {
            console.log('fetchAndUpdateCountries job: No changes detected.');
        }

    } catch (error) {
        console.error('Error in fetchAndUpdateCountries job:', error);
    }
}
