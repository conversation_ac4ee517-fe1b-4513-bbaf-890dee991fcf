import { TipResultService } from '../services/tipResultService';
import { getFixturesCollection } from '../models/Fixture';
import { getTipsCollection, TipStatus } from '../models/Tip';
import { getSocketIO } from '../server';
import { broadcastTipResults } from '../services/tipSocketService';
import { getTipResultsCollection } from '../models/TipResult';

/**
 * Process tips for finished fixtures
 * This job should be called whenever fixtures finish
 */
export async function processFinishedFixtureTips(fixtureIds: number[]): Promise<void> {
  if (!fixtureIds || fixtureIds.length === 0) {
    return;
  }

  console.log(`🎯 Processing tips for ${fixtureIds.length} finished fixtures...`);

  for (const fixtureId of fixtureIds) {
    try {
      await TipResultService.processFixtureTips(fixtureId);
      
      // Broadcast tip results to connected clients
      const io = getSocketIO();
      if (io) {
        // Get the processed tip results for this fixture
        const tipResultsCollection = getTipResultsCollection();
        const results = await tipResultsCollection.find({ fixtureId }).toArray();
        
        if (results.length > 0) {
          broadcastTipResults(io, fixtureId, results);
        }
      }
    } catch (error) {
      console.error(`❌ Error processing tips for fixture ${fixtureId}:`, error);
    }
  }

  console.log(`✅ Completed processing tips for finished fixtures`);
}

/**
 * Process all pending tips for fixtures that have finished
 * This is a cleanup job that can be run periodically to catch any missed fixtures
 */
export async function processAllPendingTips(): Promise<void> {
  console.log('🔄 Running cleanup job: Processing all pending tips for finished fixtures...');

  try {
    // Find all fixtures that are finished but might have unprocessed tips
    const fixturesCollection = getFixturesCollection();
    const tipsCollection = getTipsCollection();

    // Get all fixture IDs that have pending tips
    const fixturesWithPendingTips = await tipsCollection.distinct('fixtureId', {
      status: TipStatus.PENDING
    });

    if (fixturesWithPendingTips.length === 0) {
      console.log('✅ No pending tips found');
      return;
    }

    console.log(`🔍 Found ${fixturesWithPendingTips.length} fixtures with pending tips`);

    // Check which of these fixtures are actually finished
    const finishedFixtures = await fixturesCollection.find({
      _id: { $in: fixturesWithPendingTips },
      'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] }
    }, { projection: { _id: 1 } }).toArray();

    const finishedFixtureIds = finishedFixtures.map(f => f._id);

    if (finishedFixtureIds.length === 0) {
      console.log('✅ No finished fixtures with pending tips found');
      return;
    }

    console.log(`🎯 Processing ${finishedFixtureIds.length} finished fixtures with pending tips`);

    // Process tips for these finished fixtures
    await processFinishedFixtureTips(finishedFixtureIds);

    console.log('✅ Cleanup job completed successfully');
  } catch (error) {
    console.error('❌ Error in cleanup job:', error);
  }
}

/**
 * Get statistics about tip processing
 */
export async function getTipProcessingStats(): Promise<{
  totalTips: number;
  pendingTips: number;
  processedTips: number;
  finishedFixturesWithPendingTips: number;
}> {
  try {
    const tipsCollection = getTipsCollection();
    const fixturesCollection = getFixturesCollection();

    // Get tip counts by status
    const [totalTips, pendingTips, processedTips] = await Promise.all([
      tipsCollection.countDocuments({}),
      tipsCollection.countDocuments({ status: TipStatus.PENDING }),
      tipsCollection.countDocuments({ 
        status: { $in: [TipStatus.WON, TipStatus.LOST, TipStatus.VOID, TipStatus.PUSH] }
      })
    ]);

    // Get fixtures with pending tips that are finished
    const fixturesWithPendingTips = await tipsCollection.distinct('fixtureId', {
      status: TipStatus.PENDING
    });

    const finishedFixturesWithPendingTips = await fixturesCollection.countDocuments({
      _id: { $in: fixturesWithPendingTips },
      'fixture.status.short': { $in: ['FT', 'AET', 'PEN'] }
    });

    return {
      totalTips,
      pendingTips,
      processedTips,
      finishedFixturesWithPendingTips
    };
  } catch (error) {
    console.error('Error getting tip processing stats:', error);
    return {
      totalTips: 0,
      pendingTips: 0,
      processedTips: 0,
      finishedFixturesWithPendingTips: 0
    };
  }
}
