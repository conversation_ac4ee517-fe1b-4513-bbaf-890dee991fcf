import {
    fetchPlayerProfiles,
    fetchPlayerStatistics,
    fetchPlayerSquads,
    // fetchPlayerTeams, // Unused import
    fetchTopScorers,
    fetchTopAssists,
    fetchTopYellowCards,
    fetchTopRedCards
} from '../services/playerService';
import {
    getPlayersCollection,
    Player,
    createPlayerStatKey,
    getPlayerSquadsCollection,
    getTopPlayersCollection,
    createTopPlayersKey
} from '../models/Player';
import { getLeaguesCollection } from '../models/League';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { getTeamsCollection } from '../models/Team';
import { AnyBulkWriteOperation } from 'mongodb';
import { targetedLeagues } from '../config/targetedLeagues';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Fetch player profiles (paginated)
export async function fetchAndUpdatePlayerProfiles(maxPages: number = 10) {
    console.log('Starting fetchAndUpdatePlayerProfiles job...');
    try {
        const playersCollection = getPlayersCollection();
        const now = new Date();
        let totalUpserted = 0;
        let totalModified = 0;

        // Process page by page
        for (let page = 1; page <= maxPages; page++) {
            console.log(`Fetching player profiles page ${page}...`);

            try {
                const playerProfiles = await fetchPlayerProfiles({ page });

                if (!playerProfiles || playerProfiles.length === 0) {
                    console.log(`No more player profiles available after page ${page-1}.`);
                    break;
                }

                console.log(`Received ${playerProfiles.length} player profiles for page ${page}.`);

                const bulkOps: AnyBulkWriteOperation<Player>[] = [];

                for (const profile of playerProfiles) {
                    const filter = { _id: profile.id };

                    // Use $set with dot notation to update only the profile field
                    // This preserves any existing statistics or teams data
                    bulkOps.push({
                        updateOne: {
                            filter,
                            update: {
                                $set: {
                                    profile,
                                    apiId: profile.id, // Add apiId field to match existing database index
                                    lastUpdated: now
                                }
                            },
                            upsert: true
                        }
                    });
                }

                if (bulkOps.length > 0) {
                    const result = await playersCollection.bulkWrite(bulkOps);
                    console.log(`Page ${page} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                    totalUpserted += result.upsertedCount;
                    totalModified += result.modifiedCount;
                }

                // Add a delay between pages to respect API rate limits
                if (page < maxPages) {
                    await delay(1000);
                }

            } catch (pageError) {
                console.error(`Error fetching player profiles page ${page}:`, pageError);
                // Continue to the next page even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdatePlayerProfiles job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdatePlayerProfiles job:', error);
    }
}

// Fetch player statistics for active leagues
export async function fetchAndUpdatePlayerStatistics() {
    console.log('Starting fetchAndUpdatePlayerStatistics job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const playersCollection = getPlayersCollection();
        const now = new Date();

        // 1. Get leagues with players coverage
        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.players': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(l => l._id);

        if (leagueIdsWithCoverage.length === 0) {
            console.log('No leagues with players coverage found.');
            return;
        }

        console.log(`Found ${leagueIdsWithCoverage.length} leagues with players coverage.`);

        // 2. Find all targeted leagues with a currently active season and players coverage
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: leagueIdsWithCoverage }, // Only include leagues with players coverage
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found to fetch player statistics for.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active leagues with players coverage.`);

        // 2. Iterate through active leagues/seasons
        let totalUpserted = 0;
        let totalModified = 0;

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;

            console.log(`Fetching player statistics for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                // Fetch player statistics for this league/season
                // Note: This might be paginated, so we need to handle multiple pages
                let page = 1;
                let hasMorePages = true;

                while (hasMorePages) {
                    console.log(`Fetching page ${page} for League ID: ${leagueId}, Season: ${seasonYear}`);

                    const playerStats = await fetchPlayerStatistics({
                        league: leagueId,
                        season: seasonYear,
                        page
                    });

                    if (!playerStats || playerStats.length === 0) {
                        console.log(`No more player statistics for League ${leagueId}, Season ${seasonYear} after page ${page-1}.`);
                        hasMorePages = false;
                        continue;
                    }

                    console.log(`Received ${playerStats.length} player statistics for League ${leagueId}, Season ${seasonYear}, Page ${page}.`);

                    const bulkOps: AnyBulkWriteOperation<Player>[] = [];

                    for (const playerStat of playerStats) {
                        const playerId = playerStat.player.id;
                        const filter = { _id: playerId };
                        const statKey = createPlayerStatKey(leagueId, seasonYear);

                        // Create the update document
                        // We need to use dot notation to update the specific statistics for this league/season
                        const updateDoc: any = {
                            $set: {
                                [`statistics.${statKey}`]: playerStat.statistics,
                                apiId: playerId, // Add apiId field to match existing database index
                                lastUpdated: now
                            }
                        };

                        // If the player profile doesn't exist yet, add it
                        if (playerStat.player) {
                            updateDoc.$setOnInsert = {
                                profile: playerStat.player
                            };
                        }

                        bulkOps.push({
                            updateOne: {
                                filter,
                                update: updateDoc,
                                upsert: true
                            }
                        });
                    }

                    if (bulkOps.length > 0) {
                        const result = await playersCollection.bulkWrite(bulkOps);
                        console.log(`League ${leagueId}, Season ${seasonYear}, Page ${page} finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                        totalUpserted += result.upsertedCount;
                        totalModified += result.modifiedCount;
                    }

                    // Move to the next page
                    page++;

                    // Add a delay between pages to respect API rate limits
                    await delay(1000);
                }

                // Add a delay between leagues to respect API rate limits
                await delay(2000);

            } catch (leagueError) {
                console.error(`Error fetching player statistics for League ${leagueId}, Season ${seasonYear}:`, leagueError);
                // Continue to the next league even if one fails
                await delay(3000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdatePlayerStatistics job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdatePlayerStatistics job:', error);
    }
}

// Fetch player squads for active teams
export async function fetchAndUpdatePlayerSquads() {
    console.log('Starting fetchAndUpdatePlayerSquads job...');
    try {
        const teamsCollection = getTeamsCollection();
        const squadsCollection = getPlayerSquadsCollection();
        // const now = new Date(); // Unused variable

        // Get teams from targeted leagues
        // First, get teams that belong to targeted leagues
        const leaguesCollection = getLeaguesCollection();

        // Find teams that are associated with targeted leagues
        const teamsInTargetedLeagues = await leaguesCollection.aggregate([
            { $match: { _id: { $in: targetedLeagues } } },
            { $lookup: {
                from: 'teams',
                localField: '_id',
                foreignField: 'league.id',
                as: 'teams'
            }},
            { $unwind: '$teams' },
            { $project: { 'teamId': '$teams._id' } }
        ]).toArray();

        const teamIds = teamsInTargetedLeagues.map(t => t.teamId);

        // Get all teams from targeted leagues
        const teams = await teamsCollection.find({
            _id: { $in: teamIds }
        }).toArray();

        if (!teams || teams.length === 0) {
            console.log('No teams found to fetch player squads for.');
            return;
        }

        console.log(`Found ${teams.length} teams.`);

        let totalUpserted = 0;
        let totalModified = 0;

        for (const team of teams) {
            const teamId = team._id;

            console.log(`Fetching player squad for Team ID: ${teamId}`);

            try {
                const squads = await fetchPlayerSquads({ team: teamId });

                if (!squads || squads.length === 0) {
                    console.log(`No squad information received for Team ${teamId}.`);
                    continue;
                }

                // There should be only one squad per team
                const squad = squads[0];

                if (!squad || !squad.players || squad.players.length === 0) {
                    console.log(`Empty squad for Team ${teamId}.`);
                    continue;
                }

                console.log(`Received ${squad.players.length} players in squad for Team ${teamId}.`);

                // Update the squad in the database
                const result = await squadsCollection.updateOne(
                    { 'team.id': teamId },
                    { $set: squad },
                    { upsert: true }
                );

                console.log(`Team ${teamId} squad updated. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
                totalUpserted += result.upsertedCount;
                totalModified += result.modifiedCount;

                // Add a delay between teams to respect API rate limits
                await delay(1000);

            } catch (teamError) {
                console.error(`Error fetching player squad for Team ${teamId}:`, teamError);
                // Continue to the next team even if one fails
                await delay(2000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdatePlayerSquads job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdatePlayerSquads job:', error);
    }
}

// Fetch top players (scorers, assists, cards) for active leagues
export async function fetchAndUpdateTopPlayers() {
    console.log('Starting fetchAndUpdateTopPlayers job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const topPlayersCollection = getTopPlayersCollection();
        // const now = new Date(); // Unused variable

        // 1. Get leagues with coverage for top players features
        const leaguesWithTopScorers = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.top_scorers': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leaguesWithTopAssists = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                'coverage.top_assists': true
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leaguesWithTopCards = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues },
                $or: [
                    { 'coverage.top_cards': true },
                    { 'coverage.top_yellow_cards': true },
                    { 'coverage.top_red_cards': true }
                ]
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithTopScorers = leaguesWithTopScorers.map(l => l._id);
        const leagueIdsWithTopAssists = leaguesWithTopAssists.map(l => l._id);
        const leagueIdsWithTopCards = leaguesWithTopCards.map(l => l._id);

        console.log(`Found ${leagueIdsWithTopScorers.length} leagues with top scorers coverage.`);
        console.log(`Found ${leagueIdsWithTopAssists.length} leagues with top assists coverage.`);
        console.log(`Found ${leagueIdsWithTopCards.length} leagues with top cards coverage.`);

        // 2. Find all targeted leagues with a currently active season
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: targetedLeagues }, // Only include targeted leagues
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found to fetch top players for.');
            return;
        }

        console.log(`Found ${activeLeagues.length} leagues with active seasons.`);

        // 2. Iterate through active leagues/seasons
        // let totalUpserted = 0; // Unused variable
        let totalModified = 0;

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;

            console.log(`Fetching top players for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                // Fetch top scorers if league has coverage
                if (leagueIdsWithTopScorers.includes(leagueId)) {
                    console.log(`Fetching top scorers for League ${leagueId}, Season ${seasonYear}`);
                    const topScorersData = await fetchTopScorers({ league: leagueId, season: seasonYear });

                    if (topScorersData && topScorersData.length > 0) {
                        const scorersKey = createTopPlayersKey(leagueId, seasonYear, 'scorers');
                        await topPlayersCollection.updateOne(
                            { _id: scorersKey },
                            { $set: { players: topScorersData } },
                            { upsert: true }
                        );
                        console.log(`Updated ${topScorersData.length} top scorers for League ${leagueId}, Season ${seasonYear}`);
                        totalModified++;
                    }

                    await delay(1000); // Delay between API calls
                } else {
                    console.log(`Skipping top scorers for League ${leagueId} (no coverage)`);
                }

                // Fetch top assists if league has coverage
                if (leagueIdsWithTopAssists.includes(leagueId)) {
                    console.log(`Fetching top assists for League ${leagueId}, Season ${seasonYear}`);
                    const topAssistsData = await fetchTopAssists({ league: leagueId, season: seasonYear });

                    if (topAssistsData && topAssistsData.length > 0) {
                        const assistsKey = createTopPlayersKey(leagueId, seasonYear, 'assists');
                        await topPlayersCollection.updateOne(
                            { _id: assistsKey },
                            { $set: { players: topAssistsData } },
                            { upsert: true }
                        );
                        console.log(`Updated ${topAssistsData.length} top assists for League ${leagueId}, Season ${seasonYear}`);
                        totalModified++;
                    }

                    await delay(1000); // Delay between API calls
                } else {
                    console.log(`Skipping top assists for League ${leagueId} (no coverage)`);
                }

                // Fetch top cards if league has coverage
                if (leagueIdsWithTopCards.includes(leagueId)) {
                    // Fetch top yellow cards
                    console.log(`Fetching top yellow cards for League ${leagueId}, Season ${seasonYear}`);
                    const topYellowCardsData = await fetchTopYellowCards({ league: leagueId, season: seasonYear });

                    if (topYellowCardsData && topYellowCardsData.length > 0) {
                        const yellowCardsKey = createTopPlayersKey(leagueId, seasonYear, 'yellowcards');
                        await topPlayersCollection.updateOne(
                            { _id: yellowCardsKey },
                            { $set: { players: topYellowCardsData } },
                            { upsert: true }
                        );
                        console.log(`Updated ${topYellowCardsData.length} top yellow cards for League ${leagueId}, Season ${seasonYear}`);
                        totalModified++;
                    }

                    await delay(1000); // Delay between API calls

                    // Fetch top red cards
                    console.log(`Fetching top red cards for League ${leagueId}, Season ${seasonYear}`);
                    const topRedCardsData = await fetchTopRedCards({ league: leagueId, season: seasonYear });

                    if (topRedCardsData && topRedCardsData.length > 0) {
                        const redCardsKey = createTopPlayersKey(leagueId, seasonYear, 'redcards');
                        await topPlayersCollection.updateOne(
                            { _id: redCardsKey },
                            { $set: { players: topRedCardsData } },
                            { upsert: true }
                        );
                        console.log(`Updated ${topRedCardsData.length} top red cards for League ${leagueId}, Season ${seasonYear}`);
                        totalModified++;
                    }
                } else {
                    console.log(`Skipping top cards for League ${leagueId} (no coverage)`);
                }

                // Add a delay between leagues to respect API rate limits
                await delay(2000);

            } catch (leagueError) {
                console.error(`Error fetching top players for League ${leagueId}, Season ${seasonYear}:`, leagueError);
                // Continue to the next league even if one fails
                await delay(3000); // Longer delay after an error
            }
        }

        console.log(`fetchAndUpdateTopPlayers job finished. Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateTopPlayers job:', error);
    }
}
