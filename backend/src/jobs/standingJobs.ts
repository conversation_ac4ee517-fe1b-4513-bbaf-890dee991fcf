import { fetchStandings } from '../services/apiFootball';
import { getStandingsCollection, Standing, createStandingId } from '../models/Standing';
import { getLeaguesCollection } from '../models/League';
import { getLeagueCoverageCollection } from '../models/LeagueCoverage';
import { targetedLeagues } from '../config/targetedLeagues';
import { getDb } from '../config/database';
import { recordStandingsUpdate } from '../models/StandingUpdate';
import dayjs from 'dayjs';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Interface for fixture data used in standings updates
interface FixtureUpdateInfo {
    fixtureId: number;
    fixtureDate: string;
    fixtureStatus: string;
    season: number;
}

export async function fetchAndUpdateStandings() {
    console.log('Starting fetchAndUpdateStandings job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const coverageCollection = getLeagueCoverageCollection();
        const standingsCollection = getStandingsCollection();
        const now = new Date();
        const today = dayjs().format('YYYY-MM-DD');

        // Array to store leagues that need updates
        const leagueUpdates = [];

        // 1. Get ALL fixtures for today (not just completed ones)
        const fixturesCollection = getDb().collection('fixtures');
        const todaysFixtures = await fixturesCollection.find(
            {
                'fixture.date': { $regex: `^${today}` }
                // Removed status filter - include all fixtures (upcoming, live, completed)
            },
            {
                projection: {
                    'league.id': 1,
                    'fixture.id': 1,
                    'fixture.date': 1,
                    'fixture.status.short': 1,
                    'league.season': 1
                }
            }
        ).toArray();

        let fixtureBasedUpdates = false;
        if (todaysFixtures.length > 0) {
            console.log(`Found ${todaysFixtures.length} fixtures for today (all statuses).`);
            fixtureBasedUpdates = true;
        } else {
            console.log('No fixtures found for today. Will check for time-based updates.');
        }

        // 2. Find all targeted leagues with coverage for standings
        const leaguesWithCoverage = await coverageCollection.find(
            {
                _id: { $in: targetedLeagues }, // Only include targeted leagues
                'coverage.standings': true // Only fetch if standings are covered
            },
            { projection: { _id: 1 } }
        ).toArray();

        const leagueIdsWithCoverage = leaguesWithCoverage.map(l => l._id);

        // 3. Process fixture-based updates if we have fixtures
        if (fixtureBasedUpdates) {
            // Group fixtures by league
            const leagueFixturesMap = new Map<number, FixtureUpdateInfo[]>();

            for (const fixture of todaysFixtures) {
                const leagueId = fixture.league?.id;
                const season = fixture.league?.season;

                if (!leagueId || !season || !leagueIdsWithCoverage.includes(leagueId)) {
                    continue;
                }

                if (!leagueFixturesMap.has(leagueId)) {
                    leagueFixturesMap.set(leagueId, []);
                }

                leagueFixturesMap.get(leagueId)!.push({
                    fixtureId: fixture.fixture.id,
                    fixtureDate: fixture.fixture.date,
                    fixtureStatus: fixture.fixture.status.short,
                    season: season
                });
            }

            // 4. Check which leagues need standings updates based on having fixtures today
            for (const [leagueId, fixtures] of leagueFixturesMap.entries()) {
                // Sort fixtures by date (newest first)
                fixtures.sort((a: FixtureUpdateInfo, b: FixtureUpdateInfo) =>
                    new Date(b.fixtureDate).getTime() - new Date(a.fixtureDate).getTime()
                );

                // Get the most recent fixture for this league
                const mostRecentFixture = fixtures[0];

                // Check when was the last time we updated standings for this league
                const standingId = createStandingId(leagueId, mostRecentFixture.season);
                const lastStandings = await standingsCollection.findOne(
                    { _id: standingId },
                    { projection: { lastUpdated: 1 } }
                );

                // For leagues with fixtures today, update every hour
                const shouldCheckUpdate = !lastStandings ||
                    !lastStandings.lastUpdated ||
                    (now.getTime() - new Date(lastStandings.lastUpdated).getTime() > 1 * 60 * 60 * 1000); // 1 hour

                if (shouldCheckUpdate) {
                    leagueUpdates.push({
                        leagueId,
                        season: mostRecentFixture.season,
                        fixtureDate: mostRecentFixture.fixtureDate,
                        fixtureId: mostRecentFixture.fixtureId,
                        fixtureStatus: mostRecentFixture.fixtureStatus,
                        updateSource: 'fixture-day',
                        hasFixturesToday: true
                    });
                    console.log(`League ${leagueId} has fixtures today - scheduling hourly update`);
                }
            }
        }

        // 5. Add time-based updates for active leagues that don't have fixtures today
        // This ensures we always check for standings updates even if there are no fixtures
        console.log('Adding time-based update checks for leagues without fixtures today.');

        // Get leagues that already have fixture-based updates
        const leaguesWithFixtures = new Set(leagueUpdates.map(update => update.leagueId));

        // Get all active leagues with standings coverage
        const activeLeaguesForFallback = await leaguesCollection.find(
            {
                _id: { $in: leagueIdsWithCoverage },
                'seasons.current': true
            },
            { projection: { _id: 1, 'seasons.year': 1, 'seasons.current': 1 } }
        ).toArray();

        for (const league of activeLeaguesForFallback) {
            // Skip leagues that already have fixture-based updates
            if (leaguesWithFixtures.has(league._id)) continue;

            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            // Check when was the last time we updated standings for this league
            const standingId = createStandingId(league._id, currentSeason.year);
            const lastStandings = await standingsCollection.findOne(
                { _id: standingId },
                { projection: { lastUpdated: 1 } }
            );

            // For leagues without fixtures today, update every 6 hours (fallback)
            const shouldCheckUpdate = !lastStandings ||
                !lastStandings.lastUpdated ||
                (now.getTime() - new Date(lastStandings.lastUpdated).getTime() > 6 * 60 * 60 * 1000); // 6 hours

            if (shouldCheckUpdate) {
                leagueUpdates.push({
                    leagueId: league._id,
                    season: currentSeason.year,
                    fixtureDate: now.toISOString(),
                    fixtureId: null,
                    fixtureStatus: null,
                    updateSource: 'time-fallback',
                    hasFixturesToday: false
                });
                console.log(`League ${league._id} has no fixtures today - scheduling 6-hour fallback update`);
            }
        }

        // Log update strategy breakdown
        const fixtureBasedCount = leagueUpdates.filter(u => u.updateSource === 'fixture-day').length;
        const timeBasedCount = leagueUpdates.filter(u => u.updateSource === 'time-fallback').length;

        console.log(`Total leagues to update: ${leagueUpdates.length}`);
        console.log(`- Fixture-day based updates (hourly): ${fixtureBasedCount}`);
        console.log(`- Time-based fallback updates (6-hourly): ${timeBasedCount}`);

        if (leagueUpdates.length === 0) {
            console.log('No leagues need standings updates at this time.');
            return;
        }

        // 5. Get active season information for these leagues
        const leagueIdsToUpdate = leagueUpdates.map(l => l.leagueId);
        const activeLeagues = await leaguesCollection.find(
            {
                _id: { $in: leagueIdsToUpdate },
                "seasons.current": true
            },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found that need standings updates.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active leagues that need standings updates.`);

        // 2. Iterate through active leagues/seasons and fetch standings
        let totalUpserted = 0;
        let totalModified = 0;

        // Create a map of league updates for quick lookup
        const leagueUpdateMap = new Map();
        leagueUpdates.forEach(update => {
            leagueUpdateMap.set(update.leagueId, update);
        });

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;
            const updateInfo = leagueUpdateMap.get(leagueId);

            if (!updateInfo) {
                console.log(`No update info found for League ID: ${leagueId}. Skipping.`);
                continue;
            }

            console.log(`Fetching standings for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                // Fetch standings data from API
                const standingsDataArray = await fetchStandings({ league: leagueId, season: seasonYear });

                // API returns an array, usually with one element containing the league/standings
                if (!standingsDataArray || standingsDataArray.length === 0 || !standingsDataArray[0].league) {
                    console.log(`No standings data received for League ${leagueId}, Season ${seasonYear}.`);
                    continue;
                }

                // Extract the relevant part (the standings array itself)
                const standingsResult = standingsDataArray[0]; // Get the first element
                const standingsGroups = standingsResult.league.standings; // This is TeamStanding[][]

                const standingId = createStandingId(leagueId, seasonYear);
                const updateDoc: Standing = {
                    _id: standingId,
                    league: {
                        apiId: leagueId,
                        season: seasonYear
                    },
                    leagueId: leagueId, // For backward compatibility
                    season: seasonYear, // For backward compatibility
                    standings: standingsGroups, // Store the array of arrays
                    lastUpdated: now,
                };

                // Upsert the entire standing document for the league/season
                const result = await standingsCollection.updateOne(
                    { _id: standingId },
                    { $set: updateDoc },
                    { upsert: true }
                );

                if (result.upsertedCount > 0 || result.modifiedCount > 0) {
                    // Record that we updated the standings for this league
                    await recordStandingsUpdate(
                        leagueId,
                        seasonYear,
                        updateInfo.fixtureDate,
                        updateInfo.fixtureId
                    );

                    if (result.upsertedCount > 0) totalUpserted++;
                    if (result.modifiedCount > 0) totalModified++;
                }

                // Delay between fetches
                await delay(400); // Adjust delay

            } catch (standingError) {
                console.error(`Error fetching standings for League ${leagueId}, Season ${seasonYear}:`, standingError);
                // Continue to the next league
            }
        }

        console.log(`fetchAndUpdateStandings job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateStandings job:', error);
    }
}
