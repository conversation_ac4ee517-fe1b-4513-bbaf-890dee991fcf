import { fetchFixtureRounds } from '../services/apiFootball';
import { getFixtureRoundsCollection, FixtureRoundList, createFixtureRoundId } from '../models/FixtureRound';
import { getLeaguesCollection } from '../models/League';
import { AnyBulkWriteOperation } from 'mongodb';

// Helper function to delay execution
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

export async function fetchAndUpdateFixtureRounds() {
    console.log('Starting fetchAndUpdateFixtureRounds job...');
    try {
        const leaguesCollection = getLeaguesCollection();
        const roundsCollection = getFixtureRoundsCollection();
        const now = new Date();

        // 1. Find all leagues with a currently active season
        const activeLeagues = await leaguesCollection.find(
            { "seasons.current": true },
            { projection: { _id: 1, "seasons.year": 1, "seasons.current": 1 } }
        ).toArray();

        if (!activeLeagues || activeLeagues.length === 0) {
            console.log('No active leagues found to fetch fixture rounds for.');
            return;
        }

        console.log(`Found ${activeLeagues.length} active leagues for fixture rounds update.`);

        // 2. Iterate through active leagues/seasons and fetch rounds
        let totalUpserted = 0;
        let totalModified = 0;

        for (const league of activeLeagues) {
            const currentSeason = league.seasons.find(s => s.current);
            if (!currentSeason) continue;

            const leagueId = league._id;
            const seasonYear = currentSeason.year;

            console.log(`Fetching fixture rounds for League ID: ${leagueId}, Season: ${seasonYear}`);

            try {
                // Fetch rounds data from API (only names for now)
                const roundsFromApi = await fetchFixtureRounds({ league: leagueId, season: seasonYear });

                if (!roundsFromApi || roundsFromApi.length === 0) {
                    console.log(`No fixture rounds received for League ${leagueId}, Season ${seasonYear}.`);
                    // Consider if we should delete existing rounds if API returns empty? For now, just skip update.
                    continue;
                }

                const roundId = createFixtureRoundId(leagueId, seasonYear);
                const updateDoc: FixtureRoundList = {
                    _id: roundId,
                    leagueId: leagueId,
                    season: seasonYear,
                    rounds: roundsFromApi, // Store the array of round names
                    lastUpdated: now,
                };

                // Upsert the fixture round document for the league/season
                const result = await roundsCollection.updateOne(
                    { _id: roundId },
                    { $set: updateDoc },
                    { upsert: true }
                );

                if (result.upsertedCount > 0) totalUpserted++;
                if (result.modifiedCount > 0) totalModified++;

                // Delay between fetches
                await delay(300); // Adjust delay

            } catch (roundError) {
                console.error(`Error fetching fixture rounds for League ${leagueId}, Season ${seasonYear}:`, roundError);
                // Continue to the next league
            }
        }

        console.log(`fetchAndUpdateFixtureRounds job finished. Total Upserted: ${totalUpserted}, Total Modified: ${totalModified}`);

    } catch (error) {
        console.error('Error in fetchAndUpdateFixtureRounds job:', error);
    }
}
