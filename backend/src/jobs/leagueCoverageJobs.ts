import { fetchLeagues } from '../services/apiFootball';
import { getLeagueCoverageCollection, LeagueCoverage } from '../models/LeagueCoverage';
import { AnyBulkWriteOperation } from 'mongodb';

// Fetch and update league coverage information
export async function fetchAndUpdateLeagueCoverage(): Promise<void> {
  console.log('Starting fetchAndUpdateLeagueCoverage job...');
  try {
    // Fetch all leagues from the API
    const allLeaguesFromApi = await fetchLeagues({});

    if (!allLeaguesFromApi || allLeaguesFromApi.length === 0) {
      console.log('No leagues received from API.');
      return;
    }

    console.log(`Received ${allLeaguesFromApi.length} leagues from API.`);

    const collection = getLeagueCoverageCollection();
    const now = new Date();
    const bulkOps: AnyBulkWriteOperation<LeagueCoverage>[] = [];

    for (const leagueApi of allLeaguesFromApi) {
      // Get the current season if available
      const currentSeason = leagueApi.seasons.find(s => s.current);

      // Extract coverage information from the API response
      // Since the API response doesn't have a top-level coverage field,
      // we'll extract it from the current season's coverage
      const coverage = {
        standings: currentSeason?.coverage?.standings || false,
        players: currentSeason?.coverage?.players || false,
        top_scorers: currentSeason?.coverage?.top_scorers || false,
        top_assists: currentSeason?.coverage?.top_assists || false,
        top_cards: currentSeason?.coverage?.top_cards || false,
        injuries: currentSeason?.coverage?.injuries || false,
        predictions: currentSeason?.coverage?.predictions || false,
        odds: currentSeason?.coverage?.odds || false,
        fixtures: {
          events: currentSeason?.coverage?.fixtures?.events || false,
          lineups: currentSeason?.coverage?.fixtures?.lineups || false,
          statistics: currentSeason?.coverage?.fixtures?.statistics_fixtures || false,
          players_statistics: currentSeason?.coverage?.fixtures?.statistics_players || false
        }
      };

      // Log the coverage for debugging
      console.log(`League ${leagueApi.league.id} (${leagueApi.league.name}) coverage:`, JSON.stringify(currentSeason?.coverage));

      // Get the season year
      const seasonYear = currentSeason?.year || leagueApi.seasons[0]?.year;

      if (!seasonYear) {
        console.log(`Skipping league ${leagueApi.league.id} (${leagueApi.league.name}) - no season information available`);
        continue;
      }

      // Create update operation
      bulkOps.push({
        updateOne: {
          filter: {
            _id: leagueApi.league.id,
            season: seasonYear
          },
          update: {
            $set: {
              name: leagueApi.league.name,
              country: leagueApi.country.name,
              season: seasonYear,
              coverage: coverage,
              lastUpdated: now
            }
          },
          upsert: true
        }
      });
    }

    if (bulkOps.length > 0) {
      const result = await collection.bulkWrite(bulkOps);
      console.log(`fetchAndUpdateLeagueCoverage job finished. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`);
    } else {
      console.log('No league coverage updates to perform.');
    }

  } catch (error) {
    console.error('Error in fetchAndUpdateLeagueCoverage job:', error);
  }
}
