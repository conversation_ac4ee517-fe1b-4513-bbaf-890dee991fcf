module.exports = {
  apps: [{
    name: "kickoffscore-api",
    script: "dist/server.js",
    cwd: "/var/www/api.kickoffpredictions.com",
    instances: 2, // Utilize both vCPUs
    exec_mode: "cluster", // Enable cluster mode
    autorestart: true,
    watch: false,
    max_memory_restart: "1536M", // 1.5GB per instance
    min_uptime: "10s",
    max_restarts: 5,
    env: {
      NODE_ENV: "production",
      NODE_OPTIONS: "--max-old-space-size=1536" // Optimize V8 heap
    },
    log_date_format: "YYYY-MM-DD HH:mm:ss",
    error_file: "/var/www/api.kickoffpredictions.com/logs/error.log",
    out_file: "/var/www/api.kickoffpredictions.com/logs/out.log",
    merge_logs: true,
    // Performance optimizations
    node_args: [
      "--max-old-space-size=1536",
      "--optimize-for-size"
    ]
  }]
};
