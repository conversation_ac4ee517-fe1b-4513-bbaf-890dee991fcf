const { MongoClient } = require('mongodb');
const Redis = require('ioredis');

async function debugFixture() {
  const mongoClient = new MongoClient('mongodb://localhost:27017');
  const redisClient = new Redis('redis://localhost:6379');

  try {
    await mongoClient.connect();
    const db = mongoClient.db('kickoffscore');
    const collection = db.collection('fixtures');

    console.log('=== DEBUGGING FIXTURE 1437226 ===\n');

    // 1. Check what's in the database
    console.log('1. Database record:');
    const dbFixture = await collection.findOne({ _id: 1437226 });
    if (dbFixture) {
      console.log(`   Status: ${dbFixture.fixture.status.short} (${dbFixture.fixture.status.long})`);
      console.log(`   Has players: ${dbFixture.players ? dbFixture.players.length : 0} players`);
      console.log(`   Last updated: ${dbFixture.updatedAt}`);
    } else {
      console.log('   Fixture not found in database!');
    }

    // 2. Check if there are multiple records
    console.log('\n2. Multiple records check:');
    const allRecords = await collection.find({ _id: 1437226 }).toArray();
    console.log(`   Found ${allRecords.length} records with _id 1437226`);

    // 3. Test the exact query used by the API
    console.log('\n3. API query simulation:');
    const filter = { _id: 1437226 };
    const projection = { lastUpdated: 0 };
    const sort = { 'fixture.timestamp': -1, '_id': 1 };

    const apiResult = await collection.find(filter, { projection }).sort(sort).toArray();
    console.log(`   API query returned ${apiResult.length} fixtures`);
    if (apiResult.length > 0) {
      console.log(`   First result status: ${apiResult[0].fixture.status.short}`);
      if (apiResult.length > 1) {
        console.log(`   Second result status: ${apiResult[1].fixture.status.short}`);
      }
    }

    // 4. Check cache keys
    console.log('\n4. Cache keys:');
    const cacheKeys = await redisClient.keys('fixtures:id:1437226*');
    console.log(`   Found cache keys: ${cacheKeys.join(', ')}`);

    for (const key of cacheKeys) {
      const cachedData = await redisClient.get(key);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        console.log(`   ${key}: Status = ${parsed.fixture.status.short}`);
      }
    }

    // 5. Clear all caches
    console.log('\n5. Clearing all caches...');
    const allKeys = await redisClient.keys('*1437226*');
    if (allKeys.length > 0) {
      await redisClient.del(...allKeys);
      console.log(`   Deleted ${allKeys.length} cache keys`);
    }

    console.log('\n=== DEBUG COMPLETE ===');

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoClient.close();
    await redisClient.quit();
  }
}

debugFixture();
