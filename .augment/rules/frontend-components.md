---
type: "always_apply"
description: "Frontend Development Guidelines"
---

  You are an expert AI programmer specializing in Next.js, TypeScript, and modern web development practices.
  Your task is to help me build a Football Live Score web application.

  **Project Name:** LiveScore Pro

  **Core Functionality:**
  - Display live football match scores, updated in real-time.
  - Show match schedules, league tables, and team information.
  - Provide detailed pages for individual matches, leagues, and teams.

  **Key Technical Requirements:**
  1.  **Framework:** Use **Next.js 14** with the **App Router**. Prioritize Server-Side Rendering (SSR) and Static Site Generation (SSG) for optimal performance and SEO.
  2.  **Language:** All code must be written in **TypeScript**. Enforce strict type safety.
  3.  **Styling:** Use **Tailwind CSS** for all styling. Keep custom CSS to a minimum. The design should be responsive and modern. Frontend should be in both light and dark mode.
  4.  **UI Components:** Use **Shadcn/UI** as the primary component library. Components should be generated into `src/components/ui`.
  5.  **Real-Time Data:** The application must use **WebSockets** for live data. Use the `socket.io-client` library to connect to the backend and manage real-time events. Data that is not live (e.g., team history) can be fetched via standard server-side `fetch`. Also install TANSTACK React Query v5 to track the socket informations.
    6.  **SEO:** This is a top priority.
        - **Fonts:** Use `next/font` to load and self-host all fonts.
        - **Images:** Always use the `<Image>` component from `next/image` instead of `<img>`. Ensure `alt`, `width`, and `height` props are present. Use the `priority` prop for above-the-fold images.
        - **Caching:** Default to static rendering for pages wherever possible to leverage Next.js caching. Be mindful that using dynamic functions like `cookies()` or `headers()` opts a page into dynamic rendering.
        - **Metadata:** Use `generateMetadata` for dynamic pages and static `metadata` objects for layouts to ensure every page is SEO-optimized.
        - Implement a `sitemap.ts` file for dynamic sitemap generation.
        - Use semantic HTML and ensure content is crawlable.
  7.  **Code Structure:**
      - All source code is within the `src/` directory.
      - Components are organized: `src/components/ui` (Shadcn), `src/components/layout` (nav, footer), `src/components/features` (e.g., MatchList, Scoreboard).
      - API routes are in `src/app/api/`.
      - Type definitions are in `src/lib/types.ts`.
      - Utility functions are in `src/lib/utils.ts`.
  8. **MCP**
  - Use Sequential thinking MCP when working the task is complex.
  - Always use the context7 MCP server to reference documentation for libraries like Tailwind CSS, Next.js, React, etc.
  
  9. **Tasklist**
  - For complex tasks, break them down into smaller tasks and ask me to approve before moving on to the next task.
  Skip it when: 
– You’re debugging 
– You don’t know the goal yet 
– It’s a one-off

## Frontend Css to follow:
- Always use border colour #2a2a2a for dark theme and #f5f5f5 for light theme. Always use a padding of 16px throughout our card and website.
- The padding should be 16px on all cards.
- The frontend should be fully responsive and work on all devices.
- The frontend should be fully accessible and work with screen readers.
- The frontend should be fully optimized for performance and load quickly.

    When I ask you to create a component, you should provide the complete `.tsx` file including necessary imports, type definitions, and JSX, following all the rules above.

rules:
  -
    # Enforce TypeScript for all React components and pages.
    for: ["*.js", "*.jsx"]
    where: ["src/**"]
    except: ["*.config.js"]
    run: |
      You must use TypeScript (.ts/.tsx) for all source files. Please rewrite this file using TypeScript.
  -
    # Ensure Next.js 14 App Router conventions are followed.
    for: ["src/pages/**"]
    run: |
      This project uses the Next.js App Router. Please refactor this file from the `pages` directory structure to the `app` directory structure. For example, `pages/index.tsx` becomes `app/page.tsx`, and `pages/users/[id].tsx` becomes `app/users/[id]/page.tsx`.
  -
    # Guide the AI to use Shadcn/UI components correctly.
    for: ["*.tsx"]
    where: ["src/components/**"]
    run: |
      When creating UI elements, prioritize using components from the Shadcn/UI library (e.g., Card, Button, Table) located in `src/components/ui`. Only create a custom component if a suitable Shadcn/UI component does not exist.
  -
    # Enforce SEO best practices on page routes.
    for: ["**/page.tsx", "**/layout.tsx"]
    where: ["src/app/**"]
    run: |
      This file must include SEO optimizations.
      - If it's a `page.tsx`, it must export a `generateMetadata` function.
      - If it's a `layout.tsx`, it should define the root metadata for its segment.
      - Use semantic HTML tags (`<main>`, `<section>`, `<article>`, `<header>`).
  -
    # Guide the AI to use SWR for client-side data fetching.
    for: ["*.tsx"]
    where: ["src/components/features/**"]
    run: |
      For any component that requires real-time data or frequent updates, use the `useSWR` hook for data fetching. For data that is static per page load, it should be passed down as a prop from a server component.

# Optional: Define initial setup commands to guide the user (or an AI agent)
# on how to set up the project from scratch.
commands:
  - name: "Project Setup"
    description: "Initialize the Next.js project and all dependencies."
    steps:
      - "npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias '@/*'"
      - "npx shadcn-ui@latest init"
      - "npm install swr"
      - "npm install next-sitemap --save-dev"