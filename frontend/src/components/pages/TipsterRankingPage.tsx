'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { TipsterCard } from '@/components/features/TipsterCard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

import { Skeleton } from '@/components/ui/skeleton';
import { Trophy, TrendingUp, Target, DollarSign } from 'lucide-react';
import { api } from '@/lib/api';
import { TIPS_QUERY_KEYS } from '@/hooks/useTips';

export function TipsterRankingPage() {
  const [limit] = useState(50);

  // Fetch tipster rankings (sorted by profit by default)
  const { data: rankings, isLoading, error } = useQuery({
    queryKey: [...TIPS_QUERY_KEYS.tipsterRanking(limit)],
    queryFn: () => api.getTipsterRanking(limit),
    staleTime: 1000 * 60 * 10, // 10 minutes
    gcTime: 1000 * 60 * 30, // 30 minutes
  });

  // Rankings are already sorted by profit from backend
  const sortedRankings = rankings || [];

  // Calculate top performers for summary cards
  const topProfit = sortedRankings.find(r => (r.profit || 0) > 0);
  const topHitRate = [...sortedRankings].sort((a, b) => (b.hitRate || 0) - (a.hitRate || 0))[0];
  const topYield = [...sortedRankings].sort((a, b) => (b.yield || 0) - (a.yield || 0))[0];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-background">
        <div className="container mx-auto py-8 px-4 max-w-6xl">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-2">Tipster Rankings</h1>
            <p className="text-muted-foreground">
              Discover the top performing tipsters on KickoffScore. Rankings are updated in real-time based on verified results.
            </p>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-500" />
                  <CardTitle className="text-sm font-medium">Top Profit</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {isLoading ? (
                  <Skeleton className="h-6 w-20" />
                ) : topProfit ? (
                  <div>
                    <div className="text-2xl font-bold text-green-500">
                      +{topProfit.profit?.toFixed(2) || '0.00'}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {topProfit.totalTips} tips
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">No data</div>
                )}
              </CardContent>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  <CardTitle className="text-sm font-medium">Best Hit Rate</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {isLoading ? (
                  <Skeleton className="h-6 w-20" />
                ) : topHitRate ? (
                  <div>
                    <div className="text-2xl font-bold text-blue-500">
                      {topHitRate.hitRate?.toFixed(1) || '0.0'}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {topHitRate.totalTips} tips
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">No data</div>
                )}
              </CardContent>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                  <CardTitle className="text-sm font-medium">Best Yield</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {isLoading ? (
                  <Skeleton className="h-6 w-20" />
                ) : topYield ? (
                  <div>
                    <div className="text-2xl font-bold text-purple-500">
                      {topYield.yield?.toFixed(1) || '0.0'}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {topYield.totalTips} tips
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">No data</div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Rankings */}
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardHeader className="p-3.5">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Trophy className="h-5 w-5" />
                    Tipster Leaderboard
                  </CardTitle>
                  <CardDescription>
                    Top {limit} tipsters ranked by performance
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-3.5 pt-0">
              <div className="space-y-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {Array.from({ length: 10 }).map((_, i) => (
                        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                          <Skeleton className="h-10 w-10 rounded-full" />
                          <div className="space-y-2 flex-1">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-3 w-48" />
                          </div>
                          <div className="space-y-2">
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-3 w-12" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : error ? (
                    <div className="text-center py-12">
                      <div className="text-red-500 text-4xl mb-4">⚠️</div>
                      <p className="text-lg text-red-600">Failed to load rankings</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Please check your connection and try again
                      </p>
                    </div>
                  ) : sortedRankings.length === 0 ? (
                    <div className="text-center py-12">
                      <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-lg text-muted-foreground">No tipsters found</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Be the first to create tips and climb the rankings!
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {sortedRankings.map((tipster, index) => (
                        <TipsterCard
                          key={tipster.userId}
                          tipster={tipster}
                          rank={index + 1}
                          sortBy="profit"
                        />
                      ))}
                    </div>
                  )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
