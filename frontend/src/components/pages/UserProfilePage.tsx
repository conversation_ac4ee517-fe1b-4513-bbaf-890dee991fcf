'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { generateUsernameSlug } from '@/lib/auth';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { 
  User, 
  Mail, 
  Calendar, 
  Heart, 
  Trophy, 
  Edit2, 
  Users,
  Plus,
  Shield,
  Palette,
  Globe,
  Trash2,
  AlertTriangle,
  Moon,
  Sun,
  Monitor
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { UserTipsSection } from '@/components/features/UserTipsSection';

interface UserProfilePageProps {
  username: string;
}

export function UserProfilePage({ username }: UserProfilePageProps) {
  return (
    <ProtectedRoute>
      <UserProfileContent username={username} />
    </ProtectedRoute>
  );
}

function UserProfileContent({ username }: UserProfilePageProps) {
  const { user, updateProfile, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
  });
  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    pushNotifications: false,
    autoRefresh: true,
    compactView: false,
  });

  // Check if the current user owns this profile
  const isOwnProfile = user?.name ? generateUsernameSlug(user.name) === username.toLowerCase() : false;

  useEffect(() => {
    if (user && !isOwnProfile) {
      // Redirect to their own profile if trying to access someone else's
      const userSlug = generateUsernameSlug(user.name);
      router.push(`/${userSlug}`);
    }
  }, [user, username, isOwnProfile, router]);

  useEffect(() => {
    if (user) {
      setFormData({
        name: user.name,
        email: user.email,
      });
    }
  }, [user]);

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast.success('Profile updated successfully!');
      
      // If name changed, redirect to new URL
      const newSlug = generateUsernameSlug(formData.name);
      if (newSlug !== username) {
        router.push(`/${newSlug}`);
      }
    } catch {
      toast.error('Failed to update profile');
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      email: user?.email || '',
    });
    setIsEditing(false);
  };

  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences(prev => ({ ...prev, [key]: value }));
    toast.success('Preference updated');
  };

  const handleDeleteAccount = () => {
    toast.error('Account deletion is not implemented yet');
  };

  if (!user || !isOwnProfile) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Profile Not Found</h1>
            <p className="text-muted-foreground">The profile you&apos;re looking for doesn&apos;t exist.</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-background">
        <div className="container mx-auto py-8 px-4 max-w-6xl">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold">Profile</h1>
            <p className="text-muted-foreground">Manage your account, favorites, and preferences</p>
          </div>

          <div className="grid gap-6 lg:grid-cols-4">
            {/* Profile Sidebar */}
            <Card className="lg:col-span-1">
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={user.profileImage} alt={user.name} />
                    <AvatarFallback className="text-2xl">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                </div>
                <CardTitle className="text-xl">{user.name}</CardTitle>
                <CardDescription>{user.email}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {new Date(user.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Heart className="h-4 w-4" />
                    <span>{user.favoriteTeams?.length || 0} favorite teams</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Trophy className="h-4 w-4" />
                    <span>{user.favoriteLeagues?.length || 0} favorite leagues</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    <span>{user.favoritePlayers?.length || 0} favorite players</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <Tabs defaultValue="general" className="space-y-6">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="general">General</TabsTrigger>
                  <TabsTrigger value="tips">Tips</TabsTrigger>
                  <TabsTrigger value="favorites">Favorites</TabsTrigger>
                  <TabsTrigger value="preferences">Preferences</TabsTrigger>
                  <TabsTrigger value="account">Account</TabsTrigger>
                </TabsList>

                {/* General Tab */}
                <TabsContent value="general">
                  <Card>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle>Personal Information</CardTitle>
                          <CardDescription>
                            Update your personal details and contact information
                          </CardDescription>
                        </div>
                        {!isEditing && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setIsEditing(true)}
                          >
                            <Edit2 className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">Full Name</Label>
                          {isEditing ? (
                            <Input
                              id="name"
                              value={formData.name}
                              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            />
                          ) : (
                            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg border">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span>{user.name}</span>
                            </div>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address</Label>
                          {isEditing ? (
                            <Input
                              id="email"
                              type="email"
                              value={formData.email}
                              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                            />
                          ) : (
                            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg border">
                              <Mail className="h-4 w-4 text-muted-foreground" />
                              <span>{user.email}</span>
                            </div>
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Account Type</Label>
                          <div className="flex gap-2">
                            {user.providers?.google && (
                              <Badge variant="secondary">Google Account</Badge>
                            )}
                            {user.providers?.email && (
                              <Badge variant="secondary">Email Account</Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      {isEditing && (
                        <div className="flex gap-2 pt-4">
                          <Button onClick={handleSave}>Save Changes</Button>
                          <Button variant="outline" onClick={handleCancel}>
                            Cancel
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Tips Tab */}
                <TabsContent value="tips">
                  <UserTipsSection />
                </TabsContent>

                {/* Favorites Tab */}
                <TabsContent value="favorites">
                  <div className="space-y-6">
                    {/* Quick Stats */}
                    <div className="grid gap-4 md:grid-cols-3">
                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-center gap-4">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <Heart className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                              <p className="text-2xl font-bold">{user.favoriteTeams?.length || 0}</p>
                              <p className="text-sm text-muted-foreground">Teams</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-center gap-4">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <Trophy className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                              <p className="text-2xl font-bold">{user.favoriteLeagues?.length || 0}</p>
                              <p className="text-sm text-muted-foreground">Leagues</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardContent className="p-6">
                          <div className="flex items-center gap-4">
                            <div className="p-2 bg-primary/10 rounded-lg">
                              <Users className="h-6 w-6 text-primary" />
                            </div>
                            <div>
                              <p className="text-2xl font-bold">{user.favoritePlayers?.length || 0}</p>
                              <p className="text-sm text-muted-foreground">Players</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Favorite Teams */}
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <Heart className="h-5 w-5" />
                              Favorite Teams
                            </CardTitle>
                            <CardDescription>
                              Teams you follow for match updates
                            </CardDescription>
                          </div>
                          <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Team
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {user.favoriteTeams?.length ? (
                          <div className="text-sm text-muted-foreground">
                            {user.favoriteTeams.length} teams selected
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <Heart className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                            <h3 className="font-semibold mb-2">No favorite teams yet</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                              Add your favorite teams to get personalized updates
                            </p>
                            <Button size="sm">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Your First Team
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Favorite Leagues */}
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <div>
                            <CardTitle className="flex items-center gap-2">
                              <Trophy className="h-5 w-5" />
                              Favorite Leagues
                            </CardTitle>
                            <CardDescription>
                              Leagues you follow for comprehensive coverage
                            </CardDescription>
                          </div>
                          <Button size="sm">
                            <Plus className="h-4 w-4 mr-2" />
                            Add League
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {user.favoriteLeagues?.length ? (
                          <div className="text-sm text-muted-foreground">
                            {user.favoriteLeagues.length} leagues selected
                          </div>
                        ) : (
                          <div className="text-center py-8">
                            <Trophy className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                            <h3 className="font-semibold mb-2">No favorite leagues yet</h3>
                            <p className="text-sm text-muted-foreground mb-4">
                              Follow leagues to stay updated with matches
                            </p>
                            <Button size="sm">
                              <Plus className="h-4 w-4 mr-2" />
                              Add Your First League
                            </Button>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Preferences Tab */}
                <TabsContent value="preferences">
                  <div className="space-y-6">
                    {/* Appearance */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Palette className="h-5 w-5" />
                          Appearance
                        </CardTitle>
                        <CardDescription>
                          Customize how KickoffScore looks and feels
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div>
                          <Label className="text-sm font-medium mb-3 block">Theme</Label>
                          <div className="grid grid-cols-3 gap-3">
                            <Button
                              variant={theme === 'light' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => setTheme('light')}
                              className="justify-start"
                            >
                              <Sun className="h-4 w-4 mr-2" />
                              Light
                            </Button>
                            <Button
                              variant={theme === 'dark' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => setTheme('dark')}
                              className="justify-start"
                            >
                              <Moon className="h-4 w-4 mr-2" />
                              Dark
                            </Button>
                            <Button
                              variant={theme === 'system' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => setTheme('system')}
                              className="justify-start"
                            >
                              <Monitor className="h-4 w-4 mr-2" />
                              System
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* App Preferences */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Globe className="h-5 w-5" />
                          App Preferences
                        </CardTitle>
                        <CardDescription>
                          Configure your app behavior and display options
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-sm font-medium">Email Notifications</Label>
                            <p className="text-sm text-muted-foreground">
                              Receive match updates and news via email
                            </p>
                          </div>
                          <Switch
                            checked={preferences.emailNotifications}
                            onCheckedChange={(checked) => handlePreferenceChange('emailNotifications', checked)}
                          />
                        </div>

                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-sm font-medium">Auto Refresh</Label>
                            <p className="text-sm text-muted-foreground">
                              Automatically refresh live match data
                            </p>
                          </div>
                          <Switch
                            checked={preferences.autoRefresh}
                            onCheckedChange={(checked) => handlePreferenceChange('autoRefresh', checked)}
                          />
                        </div>

                        <Separator />

                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label className="text-sm font-medium">Compact View</Label>
                            <p className="text-sm text-muted-foreground">
                              Show more matches in less space
                            </p>
                          </div>
                          <Switch
                            checked={preferences.compactView}
                            onCheckedChange={(checked) => handlePreferenceChange('compactView', checked)}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Account Tab */}
                <TabsContent value="account">
                  <div className="space-y-6">
                    {/* Privacy & Security */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Shield className="h-5 w-5" />
                          Privacy & Security
                        </CardTitle>
                        <CardDescription>
                          Manage your privacy settings and account security
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Data Privacy</Label>
                          <p className="text-sm text-muted-foreground">
                            Your personal information is kept private and secure. We don&apos;t share your data with third parties.
                          </p>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Account Security</Label>
                          <p className="text-sm text-muted-foreground">
                            Your account is protected with secure authentication. Change your password regularly for better security.
                          </p>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Danger Zone */}
                    <Card className="border-destructive/50">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-destructive">
                          <AlertTriangle className="h-5 w-5" />
                          Danger Zone
                        </CardTitle>
                        <CardDescription>
                          Irreversible and destructive actions
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Delete Account</Label>
                          <p className="text-sm text-muted-foreground">
                            Once you delete your account, there is no going back. Please be certain.
                          </p>
                          <Button 
                            variant="destructive" 
                            size="sm"
                            onClick={handleDeleteAccount}
                            className="mt-2"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete Account
                          </Button>
                        </div>

                        <Separator />

                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Sign Out</Label>
                          <p className="text-sm text-muted-foreground">
                            Sign out of your account on this device
                          </p>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={logout}
                            className="mt-2"
                          >
                            Sign Out
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
