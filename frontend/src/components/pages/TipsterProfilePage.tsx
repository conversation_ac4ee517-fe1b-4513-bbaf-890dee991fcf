'use client';

import { useQuery } from '@tanstack/react-query';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  TrendingUp,
  Target,
  BarChart3,
  User,
  Calendar,
  Activity
} from 'lucide-react';
import { api } from '@/lib/api';

interface TipsterProfilePageProps {
  username: string;
}

interface TipResult {
  tipType: string;
  odds: number;
  stake: number;
  status: 'WON' | 'LOST' | 'VOID';
}

export function TipsterProfilePage({ username }: TipsterProfilePageProps) {
  // Fetch tipster profile data
  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['tipster-profile', username],
    queryFn: () => api.getTipsterProfile(username),
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 15, // 15 minutes
  });

  // Get user initials for avatar fallback
  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Format profit with proper sign and color
  const formatProfit = (profit: number) => {
    const sign = profit >= 0 ? '+' : '';
    return `${sign}${profit.toFixed(2)}`;
  };

  // Get profit color class
  const getProfitColor = (profit: number) => {
    if (profit > 0) return 'text-green-500';
    if (profit < 0) return 'text-red-500';
    return 'text-muted-foreground';
  };

  if (error) {
    return (
      <div className="min-h-screen flex flex-col">
        <Header />
        <main className="flex-1 bg-background">
          <div className="container mx-auto py-8 px-4 max-w-6xl">
            <div className="text-center py-12">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <p className="text-lg text-red-600">Tipster not found</p>
              <p className="text-sm text-muted-foreground mt-2">
                The tipster profile you&apos;re looking for doesn&apos;t exist or has been removed.
              </p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const displayName = profile?.user.name || username;
  const stats = profile?.stats;

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 bg-background">
        <div className="container mx-auto py-8 px-4 max-w-6xl">
          {/* Profile Header */}
          <div className="mb-8">
            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5">
                <div className="flex items-center gap-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={profile?.user.profileImage} alt={displayName} />
                    <AvatarFallback className="text-lg">
                      {getInitials(displayName)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <CardTitle className="text-2xl mb-1">{displayName}</CardTitle>
                    <CardDescription className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      @{username}
                    </CardDescription>
                  </div>

                  {stats && (
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Total Profit</div>
                      <div className={`text-2xl font-bold ${getProfitColor(stats.profit || 0)}`}>
                        {formatProfit(stats.profit || 0)}
                      </div>
                    </div>
                  )}
                </div>
              </CardHeader>
            </Card>
          </div>

          {/* Statistics Cards */}
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {Array.from({ length: 4 }).map((_, i) => (
                <Card key={i} className="rounded-lg border bg-card text-card-foreground shadow-sm">
                  <CardHeader className="p-3.5 pb-2">
                    <Skeleton className="h-4 w-20" />
                  </CardHeader>
                  <CardContent className="p-3.5 pt-0">
                    <Skeleton className="h-8 w-16 mb-2" />
                    <Skeleton className="h-3 w-12" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : stats ? (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
              {/* Total Tips */}
              <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
                <CardHeader className="p-3.5 pb-2">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-500" />
                    <CardTitle className="text-sm font-medium">Total Tips</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-3.5 pt-0">
                  <div className="text-2xl font-bold">{stats.totalTips}</div>
                  <div className="text-sm text-muted-foreground">
                    {stats.wonTips}W • {stats.lostTips}L • {stats.voidTips}V
                  </div>
                </CardContent>
              </Card>

              {/* Hit Rate */}
              <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
                <CardHeader className="p-3.5 pb-2">
                  <div className="flex items-center gap-2">
                    <Target className="h-5 w-5 text-green-500" />
                    <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-3.5 pt-0">
                  <div className="text-2xl font-bold text-green-500">
                    {(stats.hitRate || 0).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Success rate
                  </div>
                </CardContent>
              </Card>

              {/* Yield */}
              <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
                <CardHeader className="p-3.5 pb-2">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-purple-500" />
                    <CardTitle className="text-sm font-medium">Yield</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-3.5 pt-0">
                  <div className="text-2xl font-bold text-purple-500">
                    {(stats.yield || 0).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">
                    ROI
                  </div>
                </CardContent>
              </Card>

              {/* Average Odds */}
              <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
                <CardHeader className="p-3.5 pb-2">
                  <div className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-orange-500" />
                    <CardTitle className="text-sm font-medium">Avg Odds</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-3.5 pt-0">
                  <div className="text-2xl font-bold text-orange-500">
                    {(stats.averageOdds || 0).toFixed(2)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Average
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : null}

          {/* Recent Tips */}
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardHeader className="p-3.5">
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Recent Tips
              </CardTitle>
              <CardDescription>
                Latest betting tips and results
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3.5 pt-0">
              {isLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-48" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  ))}
                </div>
              ) : profile?.recentResults && profile.recentResults.length > 0 ? (
                <div className="space-y-4">
                  {profile.recentResults.map((result: TipResult, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{result.tipType}</div>
                        <div className="text-sm text-muted-foreground">
                          Odds: {result.odds} • Stake: {result.stake}
                        </div>
                      </div>
                      <Badge 
                        variant={
                          result.status === 'WON' ? 'default' : 
                          result.status === 'LOST' ? 'destructive' : 
                          'secondary'
                        }
                      >
                        {result.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No recent tips found</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
