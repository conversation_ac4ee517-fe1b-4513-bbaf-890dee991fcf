'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { FixtureLayout } from '@/components/layout/FixtureLayout';
import { FixtureHeader } from '@/components/features/FixtureHeader';
import { FixtureStats } from '@/components/features/FixtureStats';
import { FixtureLineups } from '@/components/features/FixtureLineups';
import { WhoWillWinSection } from '@/components/features/WhoWillWinSection';
import { FixtureChat } from '@/components/features/FixtureChat';
import { FixtureOverview } from '@/components/features/FixtureOverview';
import { FixtureTips } from '@/components/features/FixtureTips';
import { FixtureTable } from '@/components/features/FixtureTable';
import { FixtureHeadToHead } from '@/components/features/FixtureHeadToHead';
import { Card } from '@/components/ui/card';
import { useFixture, useLeagueStandings } from '@/hooks/useMatches';
import { useSocket } from '@/hooks/useSocket';
import { useLiveStandings } from '@/hooks/useLiveStandings';
import { generateMatchUrl } from '@/lib/utils';
import { Loader2, AlertCircle } from 'lucide-react';

interface FixtureDetailPageProps {
  fixtureId: number;
}

export function FixtureDetailPage({ fixtureId }: FixtureDetailPageProps) {
  const { data: fixture, isLoading, isError, error } = useFixture(fixtureId);
  const { subscribeToFixture, subscribeToLeague, unsubscribeFromFixture } = useSocket();
  const router = useRouter();
  const pathname = usePathname();
  const [activeTab, setActiveTab] = useState('overview');

  // Pre-load standings data so it's ready when Table tab is clicked
  const { data: standingsData } = useLeagueStandings(
    fixture?.league.id || 0,
    fixture?.league.season || 0,
    { enabled: !!(fixture?.league.id && fixture?.league.season) } // Only enable when fixture data is available
  );

  // Initialize live standings socket immediately when fixture is loaded
  // This ensures live standings data is ready when user clicks Table tab
  const liveStandingsHook = useLiveStandings({
    leagueId: fixture?.league.id || 0,
    season: fixture?.league.season || 0,
    baseStandings: standingsData || [],
    enableLiveUpdates: !!fixture && !isLoading && !isError
  });



  // Initialize socket connections for live updates
  useEffect(() => {
    if (fixture && !isLoading && !isError) {
      // Subscribe to fixture updates for real-time match data
      subscribeToFixture(fixture.fixture.id);

      // Subscribe to league updates for live standings
      subscribeToLeague(fixture.league.id);

      console.log(`Subscribed to fixture ${fixture.fixture.id} and league ${fixture.league.id} for live updates`);

      // Cleanup subscriptions when component unmounts or fixture changes
      return () => {
        unsubscribeFromFixture(fixture.fixture.id);
      };
    }
  }, [fixture, isLoading, isError, subscribeToFixture, subscribeToLeague, unsubscribeFromFixture]);

  // Update URL if the slug doesn't match the actual teams
  useEffect(() => {
    if (fixture && !isLoading && !isError) {
      const correctUrl = generateMatchUrl(
        fixture.teams.home.name,
        fixture.teams.away.name,
        fixture.fixture.id
      );

      // Only update if the current pathname doesn't match the correct URL
      if (pathname !== correctUrl) {
        router.replace(correctUrl);
      }
    }
  }, [fixture, isLoading, isError, pathname, router]);

  if (isLoading) {
    return (
      <FixtureLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-muted-foreground">Loading match details...</p>
          </div>
        </div>
      </FixtureLayout>
    );
  }

  if (isError || !fixture) {
    return (
      <FixtureLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="p-8 text-center max-w-md mx-auto">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Match Not Found</h2>
            <p className="text-muted-foreground mb-4">
              {error?.message || 'The requested match could not be found.'}
            </p>
            <Link
              href="/"
              className="text-primary hover:underline"
            >
              Return to homepage
            </Link>
          </Card>
        </div>
      </FixtureLayout>
    );
  }

  return (
    <FixtureLayout>
      <div className="grid grid-cols-1 lg:grid-cols-10 gap-2 sm:gap-4">
        {/* Main content */}
        <div className="lg:col-span-7 space-y-4">
          {/* Match header with teams, score, status and tabs */}
          <FixtureHeader
            fixture={fixture}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Tab content */}
          <div>
            {activeTab === 'overview' && <FixtureOverview fixture={fixture} />}
            {activeTab === 'tips' && <FixtureTips fixture={fixture} />}
            {activeTab === 'table' && (
              <FixtureTable
                fixture={fixture}
                preloadedStandings={standingsData}
                liveStandingsData={liveStandingsHook}
              />
            )}
            {activeTab === 'h2h' && <FixtureHeadToHead fixture={fixture} />}
            {activeTab === 'stats' && <FixtureStats fixture={fixture} />}
            {activeTab === 'lineup' && <FixtureLineups fixture={fixture} />}
          </div>
        </div>

        {/* Right sidebar with chat and "Who will win" content */}
        <div className="lg:col-span-3 space-y-4">
          {/* Match Chat */}
          <FixtureChat fixtureId={fixture.fixture.id} />

          {/* Who Will Win Section */}
          <WhoWillWinSection fixture={fixture} />
        </div>
      </div>
    </FixtureLayout>
  );
}
