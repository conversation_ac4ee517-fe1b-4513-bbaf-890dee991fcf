'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatMatchTime } from '@/lib/fixturesMockData';

interface UpcomingMatch {
  homeTeam: string;
  awayTeam: string;
  date: string;
  status: 'Today' | 'Tomorrow' | string;
}

interface UpcomingMatchesProps {
  matches: UpcomingMatch[];
}

export function UpcomingMatches({ matches }: UpcomingMatchesProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Today':
        return 'bg-fixtures-accent-green text-white';
      case 'Tomorrow':
        return 'bg-fixtures-accent-yellow text-black';
      default:
        return 'bg-fixtures-hover text-fixtures-text-primary';
    }
  };

  return (
    <Card className="bg-fixtures-card-bg border-fixtures-border">
      <div className="p-4">
        <div className="space-y-3">
          {matches.map((match, index) => (
            <div key={index} className="flex items-center justify-between py-2 hover:bg-fixtures-hover rounded-lg px-2 cursor-pointer transition-colors">
              <div className="flex items-center gap-3 flex-1">
                <div className="w-6 h-6 bg-fixtures-hover rounded-full flex items-center justify-center">
                  <span className="text-xs text-fixtures-text-primary">{match.homeTeam.charAt(0)}</span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="text-fixtures-text-primary text-sm font-medium truncate">
                    {match.homeTeam}
                  </div>
                  <div className="text-fixtures-text-primary text-sm font-medium truncate">
                    {match.awayTeam}
                  </div>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <Badge className={`${getStatusColor(match.status)} text-xs px-2 py-1`}>
                  {match.status}
                </Badge>
                <span className="text-fixtures-text-secondary text-xs">
                  {formatMatchTime(new Date(match.date))}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}