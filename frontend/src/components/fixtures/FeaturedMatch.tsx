'use client';

import { useState, useEffect, useMemo } from 'react';
import { ArrowLeft, Trophy, Shield } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { formatMatchTime, formatMatchDate, formatCountdown } from '@/lib/fixturesMockData';

interface FeaturedMatchProps {
  match: {
    _id: number;
    fixture: {
      id: number;
      date: string;
      timestamp: number;
      referee: string;
      venue: {
        name: string;
        city: string;
      };
      status: {
        short: string;
        long: string;
        elapsed: number | null;
      };
    };
    league: {
      id: number;
      name: string;
      country: string;
      logo: string;
      flag: string;
      season: number;
      round: string;
    };
    teams: {
      home: {
        id: number;
        name: string;
        logo: string;
      };
      away: {
        id: number;
        name: string;
        logo: string;
      };
    };
    goals: {
      home: number | null;
      away: number | null;
    };
  };
}

export function FeaturedMatch({ match }: FeaturedMatchProps) {
  const [countdown, setCountdown] = useState('');
  const matchDate = useMemo(() => new Date(match.fixture.date), [match.fixture.date]);

  useEffect(() => {
    const updateCountdown = () => {
      setCountdown(formatCountdown(matchDate));
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, [matchDate]);

  return (
    <Card className="bg-fixtures-card-bg border-fixtures-border">
      <div className="p-6">
        {/* Header with back button and league info */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Button variant="ghost" size="sm" className="text-fixtures-text-secondary hover:text-fixtures-text-primary">
              <ArrowLeft size={20} />
              <span className="ml-2">Matches</span>
            </Button>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <Trophy size={16} className="text-fixtures-text-secondary" />
              <span className="text-fixtures-text-primary font-medium">{match.league.name} {match.league.round}</span>
            </div>
            <Button variant="outline" size="sm" className="border-fixtures-border text-fixtures-text-primary hover:bg-fixtures-hover">
              Follow
            </Button>
          </div>
        </div>

        {/* Match details */}
        <div className="flex items-center gap-4 mb-6 text-fixtures-text-secondary text-sm">
          <div className="flex items-center gap-1">
            <span>{formatMatchDate(matchDate)}, {formatMatchTime(matchDate)}</span>
          </div>
          <div className="flex items-center gap-1">
            <span>{match.fixture.venue.name}</span>
          </div>
          <div className="flex items-center gap-1">
            <span>{match.fixture.referee}</span>
          </div>
        </div>

        {/* Main match display */}
        <div className="flex items-center justify-between mb-8">
          {/* Home team */}
          <div className="flex items-center gap-4 flex-1">
            <div className="w-12 h-12 bg-fixtures-hover rounded-full flex items-center justify-center">
              <Trophy size={24} className="text-fixtures-text-primary" />
            </div>
            <div>
              <h2 className="text-fixtures-text-primary text-xl font-semibold">{match.teams.home.name}</h2>
            </div>
          </div>

          {/* Score and time */}
          <div className="text-center px-8">
            <div className="text-4xl font-bold text-fixtures-text-primary mb-2">
              15:00
            </div>
            <div className="text-fixtures-text-secondary text-sm">
              {countdown}
            </div>
          </div>

          {/* Away team */}
          <div className="flex items-center gap-4 flex-1 justify-end">
            <div>
              <h2 className="text-fixtures-text-primary text-xl font-semibold text-right">{match.teams.away.name}</h2>
            </div>
            <div className="w-12 h-12 bg-fixtures-hover rounded-full flex items-center justify-center">
              <Shield size={24} className="text-fixtures-text-primary" />
            </div>
          </div>
        </div>

        {/* Navigation tabs */}
        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-fixtures-hover">
            <TabsTrigger value="preview" className="data-[state=active]:bg-fixtures-card-bg data-[state=active]:text-fixtures-text-primary">
              Preview
            </TabsTrigger>
            <TabsTrigger value="table" className="data-[state=active]:bg-fixtures-card-bg data-[state=active]:text-fixtures-text-primary">
              Table
            </TabsTrigger>
            <TabsTrigger value="head-to-head" className="data-[state=active]:bg-fixtures-card-bg data-[state=active]:text-fixtures-text-primary">
              Head-to-Head
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="preview" className="mt-6">
            <div className="text-fixtures-text-secondary">
              Match preview content will be displayed here
            </div>
          </TabsContent>
          
          <TabsContent value="table" className="mt-6">
            <div className="text-fixtures-text-secondary">
              League table will be displayed here
            </div>
          </TabsContent>
          
          <TabsContent value="head-to-head" className="mt-6">
            <div className="text-fixtures-text-secondary">
              Head-to-head statistics will be displayed here
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Card>
  );
}