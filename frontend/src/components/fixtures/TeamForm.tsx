'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatScore } from '@/lib/fixturesMockData';

interface TeamFormMatch {
  team: string;
  homeScore: number;
  awayScore: number;
  opponent: string;
  result: 'W' | 'D' | 'L';
}

interface TeamFormProps {
  formData: TeamFormMatch[];
}

export function TeamForm({ formData }: TeamFormProps) {
  const getResultColor = (result: 'W' | 'D' | 'L') => {
    switch (result) {
      case 'W':
        return 'bg-fixtures-accent-green text-white';
      case 'D':
        return 'bg-fixtures-accent-yellow text-black';
      case 'L':
        return 'bg-fixtures-accent-red text-white';
      default:
        return 'bg-fixtures-hover text-fixtures-text-primary';
    }
  };

  return (
    <Card className="bg-fixtures-card-bg border-fixtures-border">
      <div className="p-6">
        <h3 className="text-fixtures-text-primary text-lg font-semibold mb-6">Team form</h3>
        
        <div className="space-y-4">
          {formData.map((match, index) => (
            <div key={index} className="flex items-center justify-between py-3 border-b border-fixtures-border last:border-b-0">
              <div className="flex items-center gap-4 flex-1">
                <span className="text-fixtures-text-primary font-medium min-w-0 flex-1">
                  {match.team}
                </span>
                <Badge className={`${getResultColor(match.result)} text-xs font-bold px-2 py-1`}>
                  {formatScore(match.homeScore, match.awayScore)}
                </Badge>
                <span className="text-fixtures-text-primary font-medium min-w-0 flex-1 text-right">
                  {match.opponent}
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* About the match section */}
        <div className="mt-8">
          <h4 className="text-fixtures-text-primary text-base font-semibold mb-4">About the match</h4>
          <div className="space-y-3 text-fixtures-text-secondary text-sm">
            <p>
              • Tottenham Hotspur is playing home against Burnley at Tottenham Hotspur Stadium on Sat, Aug 16, 2025, 14:00 UTC. This is 1 of the Premier League.
            </p>
            <p>
              • Predicted lineups are available for the match a few days in advance while the actual lineup will be available about an hour ahead of the match.
            </p>
            <p>
              • The current head to head record for the teams are Tottenham Hotspur 14 win(s), Burnley 2 win(s), and 4 draw(s). Tottenham Hotspur have odds of 1.50 to win, 4.33 for a draw, and 6.50 for Burnley to win. Tottenham Hotspur are the favorites to win the match, according to the bookmakers.
            </p>
          </div>
        </div>
      </div>
    </Card>
  );
}