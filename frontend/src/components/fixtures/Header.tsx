'use client';

import { Search, Settings } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface HeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
}

export function Header({ searchQuery, onSearchChange }: HeaderProps) {
  return (
    <Card className="bg-fixtures-header-bg border-fixtures-border">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Logo */}
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center">
            <span className="text-black font-bold text-sm">K</span>
          </div>
          <span className="text-fixtures-text-primary font-semibold text-lg">KickoffScore</span>
        </div>

        {/* Search Bar */}
        <div className="flex-1 max-w-md mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-fixtures-text-secondary" size={20} />
            <Input
              placeholder="Search fixtures, teams, leagues..."
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10 bg-fixtures-card-bg border-fixtures-border text-fixtures-text-primary placeholder:text-fixtures-text-secondary"
            />
          </div>
        </div>

        {/* Settings */}
        <Button variant="ghost" size="sm" className="text-fixtures-text-secondary hover:text-fixtures-text-primary hover:bg-fixtures-hover">
          <Settings size={20} />
        </Button>
      </div>
    </Card>
  );
}