'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Trophy, Shield } from 'lucide-react';
import { formatOdds } from '@/lib/fixturesMockData';

interface BettingOddsProps {
  odds: {
    homeWin: number;
    draw: number;
    awayWin: number;
  };
}

export function BettingOdds({ odds }: BettingOddsProps) {
  return (
    <Card className="bg-fixtures-card-bg border-fixtures-border">
      <div className="p-4">
        <h3 className="text-fixtures-text-primary text-base font-semibold mb-4">Who will win?</h3>
        
        <div className="grid grid-cols-3 gap-2">
          {/* Home win */}
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 border-fixtures-border hover:bg-fixtures-hover"
          >
            <Trophy size={20} className="text-fixtures-text-secondary" />
            <span className="text-fixtures-text-primary text-lg font-bold">{formatOdds(odds.homeWin)}</span>
          </Button>

          {/* Draw */}
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 border-fixtures-border hover:bg-fixtures-hover"
          >
            <span className="text-fixtures-text-secondary text-lg font-bold">X</span>
            <span className="text-fixtures-text-primary text-lg font-bold">{formatOdds(odds.draw)}</span>
          </Button>

          {/* Away win */}
          <Button 
            variant="outline" 
            className="flex flex-col items-center gap-2 h-auto py-4 border-fixtures-border hover:bg-fixtures-hover"
          >
            <Shield size={20} className="text-fixtures-text-secondary" />
            <span className="text-fixtures-text-primary text-lg font-bold">{formatOdds(odds.awayWin)}</span>
          </Button>
        </div>
      </div>
    </Card>
  );
}