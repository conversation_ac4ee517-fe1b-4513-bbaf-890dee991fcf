'use client';

import { Card } from '@/components/ui/card';
import { Trophy } from 'lucide-react';

interface StandingRow {
  position: number;
  team: string;
  points: number;
  played: number;
}

interface LeagueStandingsProps {
  standings: StandingRow[];
  leagueName: string;
  round: string;
}

export function LeagueStandings({ standings, leagueName, round }: LeagueStandingsProps) {
  return (
    <Card className="bg-fixtures-card-bg border-fixtures-border">
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <Trophy size={16} className="text-fixtures-text-secondary" />
          <div>
            <h3 className="text-fixtures-text-primary text-base font-semibold">{leagueName}</h3>
            <p className="text-fixtures-text-secondary text-sm">{round}</p>
          </div>
        </div>
        
        <div className="space-y-2">
          {standings.map((standing) => (
            <div key={standing.position} className="flex items-center justify-between py-2">
              <div className="flex items-center gap-3">
                <span className="text-fixtures-text-secondary text-sm w-4">{standing.position}</span>
                <div className="w-6 h-6 bg-fixtures-hover rounded-full flex items-center justify-center">
                  <span className="text-xs text-fixtures-text-primary">{standing.team.charAt(0)}</span>
                </div>
                <span className="text-fixtures-text-primary text-sm">{standing.team}</span>
              </div>
              <div className="flex items-center gap-4">
                <span className="text-fixtures-text-secondary text-sm">{standing.played}</span>
                <span className="text-fixtures-text-primary text-sm font-medium w-6 text-right">
                  {standing.points}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
    </Card>
  );
}