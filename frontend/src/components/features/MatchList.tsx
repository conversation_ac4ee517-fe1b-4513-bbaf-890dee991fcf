'use client';

import { useMemo } from 'react';
import { SmartImage } from '@/components/ui/smart-image';
import { MatchCard } from './MatchCard';
import { MatchListSkeleton } from './MatchListSkeleton';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Fixture } from '@/lib/types';
import { getLeagueTier, getLeaguePriority } from '@/lib/leagueTiers';

interface MatchListProps {
  fixtures: (Fixture | (Fixture & { isDateHeader: boolean; dateKey: string }))[];
  onMatchClick?: (fixture: Fixture) => void;
  groupByLeague?: boolean;
  className?: string;
  emptyMessage?: string;
  isLoading?: boolean;
}

export function MatchList({
  fixtures,
  onMatchClick,
  groupByLeague = true,
  className,
  emptyMessage = 'No matches found',
  isLoading = false
}: MatchListProps) {
  // Format date for display
  const formatDateHeader = (dateKey: string) => {
    const date = new Date(dateKey);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }
    
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
    });
  };
  // Group fixtures by league if enabled, or handle time-based grouping
  const groupedFixtures = useMemo(() => {
    if (!groupByLeague) {
      // For time-based view, we'll render fixtures sequentially with date headers
      return { 'All Matches': fixtures };
    }

    // Filter out date headers for league grouping
    const regularFixtures = fixtures.filter(fixture => 
      !('isDateHeader' in fixture && fixture.isDateHeader)
    ) as Fixture[];

    const grouped = regularFixtures.reduce((groups, fixture) => {
      // Create a unique key using league ID and name to avoid conflicts
      // This ensures leagues with same names but different countries are separate
      const leagueKey = `${fixture.league.id}-${fixture.league.name}`;
      if (!groups[leagueKey]) {
        groups[leagueKey] = [];
      }
      groups[leagueKey].push(fixture);
      return groups;
    }, {} as Record<string, Fixture[]>);

    // Sort fixtures within each league by kickoff time (earliest first)
    Object.keys(grouped).forEach(leagueKey => {
      grouped[leagueKey].sort((a, b) => {
        return new Date(a.fixture.date).getTime() - new Date(b.fixture.date).getTime();
      });
    });

    return grouped;
  }, [fixtures, groupByLeague]);

  // Show skeleton loading if loading
  if (isLoading) {
    return <MatchListSkeleton groupByLeague={groupByLeague} className={className} />;
  }

  if (fixtures.length === 0) {
    return (
      <Card className={cn('text-center py-12', className)}>
        <div className="text-muted-foreground">
          <p className="text-lg">{emptyMessage}</p>
        </div>
      </Card>
    );
  }

  // Handle time-based view differently
  if (!groupByLeague) {
    // Group fixtures by date for time-based view
    const dateGroups: { dateKey: string; fixtures: Fixture[] }[] = [];
    let currentGroup: { dateKey: string; fixtures: Fixture[] } | null = null;
    
    fixtures.forEach((fixture) => {
      const typedFixture = fixture as Fixture & { isDateHeader?: boolean; dateKey?: string };
      
      if (typedFixture.isDateHeader && typedFixture.dateKey) {
        // Start a new date group
        currentGroup = { dateKey: typedFixture.dateKey, fixtures: [] };
        dateGroups.push(currentGroup);
      } else if (currentGroup) {
        // Add fixture to current group
        currentGroup.fixtures.push(typedFixture as Fixture);
      }
    });
    
    return (
      <div className={cn('space-y-3', className)}>
        {dateGroups.map((group) => (
          <Card key={group.dateKey} className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
            {/* Date header */}
            <div className="flex items-center gap-3 px-4 py-2 border-b" style={{backgroundColor: '#262626'}}>
              <div>
                <div className="font-semibold text-sm sm:text-base text-white">{formatDateHeader(group.dateKey)}</div>
              </div>
            </div>
            
            {/* Fixtures for this date */}
            {group.fixtures.map((fixture, index) => (
              <MatchCard
                key={fixture._id}
                fixture={fixture}
                onClick={() => onMatchClick?.(fixture)}
                isLast={index === group.fixtures.length - 1}
              />
            ))}
          </Card>
        ))}
      </div>
    );
  }

  // Sort leagues by tier for display order
  const sortedLeagueEntries = Object.entries(groupedFixtures).sort(([, leagueFixturesA], [, leagueFixturesB]) => {
    // Get the first fixture from each league to determine tier
    const leagueA = leagueFixturesA[0];
    const leagueB = leagueFixturesB[0];

    if (!leagueA || !leagueB) return 0;

    const tierA = getLeagueTier(leagueA.league.id);
    const tierB = getLeagueTier(leagueB.league.id);

    // Sort by tier first (lower tier number = higher priority)
    if (tierA !== tierB) {
      return tierA - tierB;
    }

    // If same tier, sort by priority within tier
    const priorityA = getLeaguePriority(leagueA.league.id);
    const priorityB = getLeaguePriority(leagueB.league.id);

    return priorityA - priorityB;
  });

  // Original league-based grouping
  return (
    <div className={cn('space-y-3', className)}>
      {sortedLeagueEntries.map(([leagueKey, leagueFixtures]) => {
        // Extract the actual league name from the first fixture
        const actualLeagueName = leagueFixtures[0]?.league.name || 'Unknown League';

        return (
          <Card key={leagueKey} className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
            {/* League header (only show if grouping by league) */}
            {groupByLeague && (
              <div className="flex items-center gap-3 px-4 py-2 border-b bg-muted">
                {/* Country flag */}
                {leagueFixtures[0]?.league.flag && (
                  <SmartImage
                    src={leagueFixtures[0].league.flag}
                    alt={`${leagueFixtures[0].league.country} flag`}
                    width={18}
                    height={18}
                    className="h-[18px] w-[18px] sm:h-[20px] sm:w-[20px] rounded-full object-cover object-center"
                    fallbackText={leagueFixtures[0].league.country}
                    fallbackIcon="🏴"
                  />
                )}
                <div>
                  <div className="font-semibold text-sm sm:text-base text-foreground">{actualLeagueName}</div>
                </div>
              </div>
            )}

            {/* Matches in this league */}
            <div>
              {leagueFixtures.map((fixture, index) => (
                <MatchCard
                  key={fixture._id}
                  fixture={fixture}
                  onClick={() => onMatchClick?.(fixture)}
                  isLast={index === leagueFixtures.length - 1}
                />
              ))}
            </div>
          </Card>
        );
      })}
    </div>
  );
}
