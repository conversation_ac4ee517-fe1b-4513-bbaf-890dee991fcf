'use client';

import React from 'react';
import { TeamRatings, TeamRatingData } from '@/lib/types';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Users } from 'lucide-react';

interface TeamRatingProps {
  teamRatings: TeamRatings;
  homeTeamName: string;
  awayTeamName: string;
  className?: string;
  showDetails?: boolean;
}

interface TeamRatingDisplayProps {
  teamName: string;
  teamData: TeamRatingData;
  isHome: boolean;
  showDetails?: boolean;
}

const TeamRatingDisplay: React.FC<TeamRatingDisplayProps> = ({
  teamName,
  teamData,
  isHome,
  showDetails = false
}) => {


  const getRatingBadgeVariant = (rating: number): "default" | "secondary" | "destructive" | "outline" => {
    if (rating >= 7.5) return 'default';
    if (rating >= 6.5) return 'secondary';
    if (rating >= 5.5) return 'outline';
    return 'destructive';
  };

  return (
    <div className={`flex flex-col ${isHome ? 'items-start' : 'items-end'} space-y-2`}>
      <div className="flex items-center space-x-2">
        <Star className="h-4 w-4 text-yellow-500" />
        <span className="text-sm font-medium text-muted-foreground">{teamName}</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <Badge variant={getRatingBadgeVariant(teamData.rating)} className="text-lg font-bold px-3 py-1">
          {teamData.rating.toFixed(2)}
        </Badge>
        {showDetails && (
          <div className="flex items-center space-x-1 text-xs text-muted-foreground">
            <Users className="h-3 w-3" />
            <span>{teamData.ratedPlayersCount}/{teamData.playersCount}</span>
          </div>
        )}
      </div>

      {showDetails && teamData.ratedPlayersCount > 0 && (
        <div className="text-xs text-muted-foreground">
          {teamData.ratedPlayersCount} players rated
        </div>
      )}
    </div>
  );
};

const TeamRating: React.FC<TeamRatingProps> = ({
  teamRatings,
  homeTeamName,
  awayTeamName,
  className = '',
  showDetails = false
}) => {
  if (!teamRatings || !teamRatings.home || !teamRatings.away) {
    return null;
  }

  // Don't show if no players have ratings
  if (teamRatings.home.ratedPlayersCount === 0 && teamRatings.away.ratedPlayersCount === 0) {
    return null;
  }

  return (
    <Card className={`p-3.5 ${className}`}>
      <div className="flex items-center justify-between">
        <TeamRatingDisplay
          teamName={homeTeamName}
          teamData={teamRatings.home}
          isHome={true}
          showDetails={showDetails}
        />
        
        <div className="flex flex-col items-center space-y-1">
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
            Team Ratings
          </span>
          <div className="text-xs text-muted-foreground">
            Live Performance
          </div>
        </div>
        
        <TeamRatingDisplay
          teamName={awayTeamName}
          teamData={teamRatings.away}
          isHome={false}
          showDetails={showDetails}
        />
      </div>
    </Card>
  );
};

export default TeamRating;
