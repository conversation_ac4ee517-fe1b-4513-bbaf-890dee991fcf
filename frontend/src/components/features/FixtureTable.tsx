'use client';

import React, { useState } from 'react';
import { useLeagueStandings, useNextFixturesForLeague } from '@/hooks/useMatches';
import { useLiveStandings, UseLiveStandingsReturn, LiveStanding } from '@/hooks/useLiveStandings';
import { Fixture, Standing } from '@/lib/types';
import { SmartImage } from '@/components/ui/smart-image';
import { BulkNextFixtureIndicator } from './BulkNextFixtureIndicator';

interface FixtureTableProps {
  fixture: Fixture;
  preloadedStandings?: Standing[][];
  liveStandingsData?: UseLiveStandingsReturn;
}

interface FormIndicatorProps {
  result: string;
}

const FormIndicator = ({ result }: FormIndicatorProps) => {
  const getFormColors = (result: string) => {
    switch (result) {
      case 'W':
        return 'var(--form-win)';
      case 'D':
        return 'var(--form-draw)';
      case 'L':
        return 'var(--form-loss)';
      default:
        return 'var(--form-default)';
    }
  };

  const backgroundColor = getFormColors(result);
  return (
    <div
      className="w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0 text-white"
      style={{ backgroundColor }}
      title={result}
    >
      {result}
    </div>
  );
};

export function FixtureTable({ fixture, preloadedStandings, liveStandingsData }: FixtureTableProps) {
  // Only fetch standings if not preloaded - use enabled option instead of 0,0 params
  const shouldFetchStandings = !preloadedStandings;
  const { data: standingsData, isLoading, error } = useLeagueStandings(
    fixture.league.id,
    fixture.league.season,
    { enabled: shouldFetchStandings } // Properly disable the query
  );

  // Use preloaded standings or fetched standings
  const baseStandings = preloadedStandings || standingsData || [];

  // Use preloaded live standings data if available, otherwise create new hook
  const fallbackLiveStandings = useLiveStandings({
    leagueId: fixture.league.id,
    season: fixture.league.season,
    baseStandings,
    enableLiveUpdates: !liveStandingsData // Only enable if not preloaded
  });

  const liveStandingsHookResult = liveStandingsData || fallbackLiveStandings;

  const {
    liveStandings,
    liveTeams
  } = liveStandingsHookResult;

  // Fetch bulk next fixtures for the league (more efficient than individual team calls)
  const { data: nextFixtures = [], isLoading: nextFixturesLoading } = useNextFixturesForLeague(
    fixture.league.id,
    fixture.league.season,
    20 // Get next 20 fixtures to cover all teams
  );

  // Use live standings if available, otherwise fall back to base standings
  const standings = liveStandings.length > 0 ? liveStandings[0] || [] : baseStandings[0] || [];
  const [viewType, setViewType] = useState<'all' | 'home' | 'away'>('all');

  if (isLoading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <div className="space-y-4">
          <div className="h-6 bg-muted animate-pulse rounded w-48"></div>
          <div className="space-y-2">
            {Array.from({ length: 10 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-4 py-2">
                <div className="w-6 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-6 h-6 bg-muted animate-pulse rounded-full"></div>
                <div className="h-4 bg-muted animate-pulse rounded flex-1"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
                <div className="w-8 h-4 bg-muted animate-pulse rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <div className="text-center py-8">
          <p className="text-status-error">Error loading standings: {error.message}</p>
        </div>
      </div>
    );
  }

  if (!standings || standings.length === 0) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <div className="text-center py-8">
          <p className="text-muted-foreground">No standings available for this league</p>
        </div>
      </div>
    );
  }

  const homeTeamId = fixture.teams.home.id;
  const awayTeamId = fixture.teams.away.id;

  const isTeamInFixture = (teamId: number) => {
    return teamId === homeTeamId || teamId === awayTeamId;
  };

  // Determine which stat should be highlighted based on live match status
  const getHighlightedStat = (teamId: number) => {
    // First check if this team is in the current fixture
    if (isTeamInFixture(teamId) && fixture.goals) {
      const homeScore = fixture.goals.home || 0;
      const awayScore = fixture.goals.away || 0;

      if (homeScore > awayScore) {
        // Home team is winning
        return teamId === homeTeamId ? 'win' : 'lose';
      } else if (awayScore > homeScore) {
        // Away team is winning
        return teamId === awayTeamId ? 'win' : 'lose';
      } else {
        // It's a draw
        return 'draw';
      }
    }

    // Check if this team is in any other live match using live standings data
    if (liveStandings && liveStandings.length > 0) {
      for (const group of liveStandings) {
        for (const standing of group) {
          if (standing.team.id === teamId && 'liveMatches' in standing) {
            const liveStanding = standing as LiveStanding;
            const liveMatches = liveStanding.liveMatches;

            // Find a live match for this team (excluding the current fixture)
            if (liveMatches) {
              for (const liveMatch of liveMatches) {
              if (liveMatch.fixtureId !== fixture.fixture.id && !liveMatch.isFinished) {
                const homeScore = liveMatch.currentScore.home;
                const awayScore = liveMatch.currentScore.away;

                if (homeScore > awayScore) {
                  // Home team is winning
                  return liveMatch.homeAway === 'home' ? 'win' : 'lose';
                } else if (awayScore > homeScore) {
                  // Away team is winning
                  return liveMatch.homeAway === 'away' ? 'win' : 'lose';
                } else {
                  // It's a draw
                  return 'draw';
                }
              }
            }
            }
          }
        }
      }
    }

    return null;
  };

  const getStandingsData = (team: Standing) => {
    // Add null checks for team data
    if (!team) {
      return {
        played: 0,
        win: 0,
        draw: 0,
        lose: 0,
        goalsFor: 0,
        goalsAgainst: 0,
        points: 0,
        goalDifference: 0,
      };
    }

    switch (viewType) {
      case 'home':
        return {
          played: team.home?.played || 0,
          win: team.home?.win || 0,
          draw: team.home?.draw || 0,
          lose: team.home?.lose || 0,
          goalsFor: team.home?.goals?.for || 0,
          goalsAgainst: team.home?.goals?.against || 0,
          points: (team.home?.win || 0) * 3 + (team.home?.draw || 0),
          goalDifference: (team.home?.goals?.for || 0) - (team.home?.goals?.against || 0),
        };
      case 'away':
        return {
          played: team.away?.played || 0,
          win: team.away?.win || 0,
          draw: team.away?.draw || 0,
          lose: team.away?.lose || 0,
          goalsFor: team.away?.goals?.for || 0,
          goalsAgainst: team.away?.goals?.against || 0,
          points: (team.away?.win || 0) * 3 + (team.away?.draw || 0),
          goalDifference: (team.away?.goals?.for || 0) - (team.away?.goals?.against || 0),
        };
      default:
        return {
          played: team.all?.played || 0,
          win: team.all?.win || 0,
          draw: team.all?.draw || 0,
          lose: team.all?.lose || 0,
          goalsFor: team.all?.goals?.for || 0,
          goalsAgainst: team.all?.goals?.against || 0,
          points: team.points || 0,
          goalDifference: team.goalsDiff || 0,
        };
    }
  };

  // Sort standings based on the current view type
  const getSortedStandings = () => {
    if (viewType === 'all') {
      return standings; // Use original order for 'all' view
    }

    return [...standings].sort((a, b) => {
      const aData = getStandingsData(a);
      const bData = getStandingsData(b);

      // Sort by points first (descending)
      if (aData.points !== bData.points) {
        return bData.points - aData.points;
      }

      // Then by goal difference (descending)
      if (aData.goalDifference !== bData.goalDifference) {
        return bData.goalDifference - aData.goalDifference;
      }

      // Then by goals for (descending)
      return bData.goalsFor - aData.goalsFor;
    });
  };

  // Function to get position colors based on description (only left border, no background)
  const getPositionColors = (description?: string) => {
    if (!description) return '';

    const desc = description.toLowerCase();

    if (desc.includes('champions league')) {
      return 'border-l-4 border-competition-cl';
    } else if (desc.includes('europa league')) {
      return 'border-l-4 border-competition-el';
    } else if (desc.includes('conference league') || desc.includes('europa conference')) {
      return 'border-l-4 border-competition-ecl';
    } else if (desc.includes('relegation playoff')) {
      return 'border-l-4 border-competition-relegation-playoff';
    } else if (desc.includes('relegation')) {
      return 'border-l-4 border-competition-relegation';
    } else if (desc.includes('playoff')) {
      return 'border-l-4 border-competition-playoff';
    }

    return '';
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
      <div>
        {/* View Type Toggle */}
        <div className="bg-muted/30 px-4 py-3 border-b border-custom-gray">
          <div className="flex space-x-2">
            <button
              onClick={() => setViewType('all')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                viewType === 'all'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted/80'
              }`}
            >
              All
            </button>
            <button
              onClick={() => setViewType('home')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                viewType === 'home'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted/80'
              }`}
            >
              Home
            </button>
            <button
              onClick={() => setViewType('away')}
              className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                viewType === 'away'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-muted hover:bg-muted/80'
              }`}
            >
              Away
            </button>
          </div>
        </div>



        {/* Table header */}
        <div className="bg-muted/50 px-4 py-2 border-b border-custom-gray">
          {/* Mobile header */}
          <div className="grid gap-2 items-center text-sm font-medium text-muted-foreground md:hidden" style={{gridTemplateColumns: '30px 1fr 40px 30px 30px'}}>
            <div className="text-center">#</div>
            <div>Team</div>
            <div className="text-center">PL</div>
            <div className="text-center">+/-</div>
            <div className="text-center">PTS</div>
          </div>
          {/* Desktop header */}
          <div className="hidden md:grid gap-2 items-center text-sm font-medium text-muted-foreground" style={{gridTemplateColumns: '30px 1fr 40px 40px 40px 30px 40px 30px 30px 120px 30px'}}>
            <div className="text-center">#</div>
            <div>Team</div>
            <div className="text-center">PL</div>
            <div className="text-center">W</div>
            <div className="text-center">D</div>
            <div className="text-center">L</div>
            <div className="text-center">G</div>
            <div className="text-center">+/-</div>
            <div className="text-center">PTS</div>
            <div className="text-center">Form</div>
            <div className="text-center">Next</div>
          </div>
        </div>

        {/* Table rows */}
        <div className="divide-y divide-custom-gray">
          {getSortedStandings().map((team, index) => {
            // Add null safety checks
            if (!team || !team.team) {
              return null;
            }

            const teamData = getStandingsData(team);
            const positionColors = getPositionColors(team.description);
            const isFixtureTeam = team.team?.id && isTeamInFixture(team.team.id);
            const isLiveTeam = team.team?.id && liveTeams.has(team.team.id);

            return (
              <div
                key={team.team?.id || index}
                className={`relative px-4 py-2 hover:bg-muted/30 transition-colors ${
                  isFixtureTeam ? 'bg-active-team-row' : ''
                }`}
              >
                {/* Position indicator border */}
                {positionColors && (
                  <div className={`absolute left-0 top-0 bottom-0 w-1 ${positionColors.replace('border-l-4 border-l-', 'bg-')}`} />
                )}
                {/* Mobile layout */}
                <div className="grid gap-2 items-center text-sm font-medium md:hidden" style={{gridTemplateColumns: '30px 1fr 40px 30px 30px'}}>
                  {/* Position */}
                  <div className="font-medium text-center">
                    {team.rank || '-'}
                  </div>

                  {/* Team */}
                  <div className="flex items-center space-x-2 min-w-0">
                    <div className="w-[18px] h-[18px] relative flex-shrink-0">
                      <SmartImage
                        src={team.team?.logo || '/placeholder-team.png'}
                        alt={team.team?.name || 'Team'}
                        width={18}
                        height={18}
                        className="object-contain w-full h-full"
                      />
                    </div>
                    <div className="flex items-center gap-1 min-w-0 relative">
                      <span className={`truncate font-medium text-xs ${
                        isLiveTeam ? 'text-red-500' : ''
                      }`}>
                        {team.team?.name || 'Unknown Team'}
                      </span>
                      {isLiveTeam && (
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full flex-shrink-0 ml-1 animate-pulse" />
                      )}
                    </div>
                  </div>

                  {/* Played */}
                  <div className="text-center text-xs">
                    <span className={isLiveTeam ? 'text-red-500' : ''}>
                      {teamData.played}
                    </span>
                  </div>

                  {/* Goal Difference */}
                  <div className="text-center text-xs">
                    {teamData.goalDifference > 0 ? '+' : ''}{teamData.goalDifference}
                  </div>

                  {/* Points */}
                  <div className="text-center font-semibold text-xs">
                    <span className={isLiveTeam ? 'text-red-500' : ''}>
                      {teamData.points}
                    </span>
                  </div>
                </div>

                {/* Desktop layout */}
                <div className="hidden md:grid gap-2 items-center text-sm font-medium" style={{gridTemplateColumns: '30px 1fr 40px 40px 40px 30px 40px 30px 30px 120px 30px'}}>
                  {/* Position */}
                  <div className="font-medium text-center">
                    {team.rank || '-'}
                  </div>

                  {/* Team */}
                  <div className="flex items-center space-x-2 min-w-0">
                    <div className="w-[18px] h-[18px] relative flex-shrink-0">
                      <SmartImage
                        src={team.team?.logo || '/placeholder-team.png'}
                        alt={team.team?.name || 'Team'}
                        width={18}
                        height={18}
                        className="object-contain w-full h-full"
                      />
                    </div>
                    <div className="flex items-center gap-1 min-w-0 relative">
                      <span className={`truncate font-medium ${
                        isLiveTeam ? 'text-red-500' : ''
                      }`}>
                        {team.team?.name || 'Unknown Team'}
                      </span>
                      {isLiveTeam && (
                        <div className="w-1.5 h-1.5 bg-red-500 rounded-full flex-shrink-0 ml-1 animate-pulse" />
                      )}
                    </div>
                  </div>

                  {/* Played */}
                  <div className="text-center">
                    <span>
                      {teamData.played}
                    </span>
                  </div>

                  {/* Won */}
                  <div className="text-center">
                    <span className={
                      isLiveTeam && getHighlightedStat(team.team?.id || 0) === 'win'
                        ? 'text-red-500'
                        : ''
                    }>
                      {teamData.win}
                    </span>
                  </div>

                  {/* Drawn */}
                  <div className="text-center">
                    <span className={
                      isLiveTeam && getHighlightedStat(team.team?.id || 0) === 'draw'
                        ? 'text-red-500'
                        : ''
                    }>
                      {teamData.draw}
                    </span>
                  </div>

                  {/* Lost */}
                  <div className="text-center">
                    <span className={
                      isLiveTeam && getHighlightedStat(team.team?.id || 0) === 'lose'
                        ? 'text-red-500'
                        : ''
                    }>
                      {teamData.lose}
                    </span>
                  </div>

                  {/* Goals */}
                  <div className="text-center">
                    {teamData.goalsFor}:{teamData.goalsAgainst}
                  </div>

                  {/* Goal Difference */}
                  <div className="text-center">
                    {teamData.goalDifference > 0 ? '+' : ''}{teamData.goalDifference}
                  </div>

                  {/* Points */}
                  <div className="text-center font-semibold">
                    <span className={isLiveTeam ? 'text-red-500' : ''}>
                      {teamData.points}
                    </span>
                  </div>

                  {/* Form */}
                  <div className="flex justify-center items-center gap-1 min-w-[110px]">
                    {team.form ? (
                      team.form.slice(-5).split('').map((result, index) => (
                        <FormIndicator key={index} result={result} />
                      ))
                    ) : (
                      <span className="text-xs text-muted-foreground">-</span>
                    )}
                  </div>

                  {/* Next */}
                  <div className="text-center">
                    <BulkNextFixtureIndicator
                      teamId={team.team?.id || 0}
                      nextFixtures={nextFixtures}
                      isLoading={nextFixturesLoading}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Position Legend - Dynamic based on actual standings data */}
      <div className="px-4 py-2 border-t border-custom-gray bg-muted/20">
        <div className="flex flex-wrap gap-3 text-xs">
          {(() => {
            // Get all unique position descriptions from standings
            const positionTypes = new Set<string>();
            standings.forEach(team => {
              if (team.description) {
                const desc = team.description.toLowerCase();
                if (desc.includes('champions league')) {
                  positionTypes.add('champions-league');
                } else if (desc.includes('europa league')) {
                  positionTypes.add('europa-league');
                } else if (desc.includes('conference league') || desc.includes('europa conference')) {
                  positionTypes.add('conference-league');
                } else if (desc.includes('relegation playoff')) {
                  positionTypes.add('relegation-playoff');
                } else if (desc.includes('relegation')) {
                  positionTypes.add('relegation');
                } else if (desc.includes('playoff')) {
                  positionTypes.add('playoff');
                }
              }
            });

            // Define legend items with their display order
            const legendItems = [
              { key: 'champions-league', label: 'Champions League', class: 'border-competition-cl' },
              { key: 'europa-league', label: 'Europa League', class: 'border-competition-el' },
              { key: 'conference-league', label: 'Conference League', class: 'border-competition-ecl' },
              { key: 'playoff', label: 'Playoff', class: 'border-competition-playoff' },
              { key: 'relegation-playoff', label: 'Relegation Playoff', class: 'border-competition-relegation-playoff' },
              { key: 'relegation', label: 'Relegation', class: 'border-competition-relegation' },
            ];

            // Return only the legend items that exist in the current standings
            return legendItems
              .filter(item => positionTypes.has(item.key))
              .map(item => (
                <div key={item.key} className="flex items-center gap-1">
                  <div className={`w-3 h-3 border-l-4 ${item.class}`}></div>
                  <span className="text-muted-foreground">{item.label}</span>
                </div>
              ));
          })()}
        </div>
      </div>
    </div>
  );
}
