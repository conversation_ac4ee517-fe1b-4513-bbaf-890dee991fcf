'use client';

import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useFixtureOdds } from '@/hooks/useMatches';
import { Fixture, BetValue } from '@/lib/types';
import { DollarSign, TrendingUp, Target } from 'lucide-react';
import { SmartImage } from '@/components/ui/smart-image';

interface FixtureOddsProps {
  fixture: Fixture;
}

export function FixtureOdds({ fixture }: FixtureOddsProps) {
  const { data: odds, isLoading } = useFixtureOdds(fixture.fixture.id);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Betting Odds
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!odds || odds.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Betting Odds
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            No betting odds available for this match
          </div>
        </CardContent>
      </Card>
    );
  }

  // Get all unique bet types
  const allBetTypes = new Set<string>();
  odds.forEach(odd => {
    odd.bookmakers.forEach(bookmaker => {
      bookmaker.bets.forEach(bet => {
        allBetTypes.add(bet.name);
      });
    });
  });

  const betTypesArray = Array.from(allBetTypes);

  // Helper function to get odds for a specific bet type
  const getOddsForBetType = (betType: string) => {
    const result: { [bookmaker: string]: BetValue[] } = {};
    
    odds.forEach(odd => {
      odd.bookmakers.forEach(bookmaker => {
        const bet = bookmaker.bets.find(b => b.name === betType);
        if (bet) {
          result[bookmaker.name] = bet.values;
        }
      });
    });
    
    return result;
  };

  // Get match winner odds (most common bet type)
  const matchWinnerOdds = getOddsForBetType('Match Winner') || getOddsForBetType('1X2') || {};

  return (
    <div className="space-y-6">
      {/* Quick Odds Overview */}
      {Object.keys(matchWinnerOdds).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Match Winner - Best Odds
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4">
              {/* Home Win */}
              <div className="text-center p-4 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <SmartImage
                    src={fixture.teams.home.logo}
                    alt={fixture.teams.home.name}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="font-medium text-sm">{fixture.teams.home.name}</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(matchWinnerOdds)[0]?.[0]?.odd || 'N/A'}
                </div>
              </div>

              {/* Draw */}
              <div className="text-center p-4 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <span className="font-medium text-sm">Draw</span>
                </div>
                <div className="text-2xl font-bold text-yellow-600">
                  {Object.values(matchWinnerOdds)[0]?.[1]?.odd || 'N/A'}
                </div>
              </div>

              {/* Away Win */}
              <div className="text-center p-4 border rounded-lg">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <SmartImage
                    src={fixture.teams.away.logo}
                    alt={fixture.teams.away.name}
                    width={24}
                    height={24}
                    className="rounded-full"
                  />
                  <span className="font-medium text-sm">{fixture.teams.away.name}</span>
                </div>
                <div className="text-2xl font-bold text-green-600">
                  {Object.values(matchWinnerOdds)[0]?.[2]?.odd || 'N/A'}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Odds by Bet Type */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            All Betting Markets
          </CardTitle>
        </CardHeader>
        <CardContent>
          {betTypesArray.length > 0 ? (
            <Tabs defaultValue={betTypesArray[0]} className="w-full">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-4">
                {betTypesArray.slice(0, 4).map((betType) => (
                  <TabsTrigger key={betType} value={betType} className="text-xs">
                    {betType.replace('Match Winner', '1X2').replace('Goals Over/Under', 'O/U')}
                  </TabsTrigger>
                ))}
              </TabsList>

              {betTypesArray.map((betType) => {
                const betOdds = getOddsForBetType(betType);
                
                return (
                  <TabsContent key={betType} value={betType} className="mt-4">
                    <div className="space-y-4">
                      <h4 className="font-semibold">{betType}</h4>
                      
                      {Object.keys(betOdds).length > 0 ? (
                        <div className="overflow-x-auto">
                          <table className="w-full text-sm">
                            <thead>
                              <tr className="border-b">
                                <th className="text-left p-2">Bookmaker</th>
                                {betOdds[Object.keys(betOdds)[0]]?.map((value: BetValue, index: number) => (
                                  <th key={index} className="text-center p-2">
                                    {value.value}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {Object.entries(betOdds).map(([bookmaker, values]: [string, BetValue[]]) => (
                                <tr key={bookmaker} className="border-b hover:bg-muted/50">
                                  <td className="p-2 font-medium">{bookmaker}</td>
                                  {values.map((value: BetValue, index: number) => (
                                    <td key={index} className="text-center p-2">
                                      <Badge variant="outline" className="font-mono">
                                        {value.odd}
                                      </Badge>
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                      ) : (
                        <div className="text-center py-4 text-muted-foreground">
                          No odds available for this market
                        </div>
                      )}
                    </div>
                  </TabsContent>
                );
              })}
            </Tabs>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              No betting markets available
            </div>
          )}
        </CardContent>
      </Card>

      {/* Odds Comparison */}
      {Object.keys(matchWinnerOdds).length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Bookmaker Comparison
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground mb-4">
              Compare odds across different bookmakers for the best value
            </div>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Bookmaker</th>
                    <th className="text-center p-2">{fixture.teams.home.name}</th>
                    <th className="text-center p-2">Draw</th>
                    <th className="text-center p-2">{fixture.teams.away.name}</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(matchWinnerOdds).map(([bookmaker, values]: [string, BetValue[]]) => (
                    <tr key={bookmaker} className="border-b hover:bg-muted/50">
                      <td className="p-2 font-medium">{bookmaker}</td>
                      <td className="text-center p-2">
                        <Badge variant="outline" className="font-mono">
                          {values[0]?.odd || 'N/A'}
                        </Badge>
                      </td>
                      <td className="text-center p-2">
                        <Badge variant="outline" className="font-mono">
                          {values[1]?.odd || 'N/A'}
                        </Badge>
                      </td>
                      <td className="text-center p-2">
                        <Badge variant="outline" className="font-mono">
                          {values[2]?.odd || 'N/A'}
                        </Badge>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
