'use client';

import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Heart,
  MessageCircle,
  Trash2,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { TipWithDetails, Fixture, TipType, TipStatus } from '@/lib/types';
import { api } from '@/lib/api';
import { formatDistanceToNow } from 'date-fns';

interface TipCardProps {
  tip: TipWithDetails;
  fixture: Fixture;
  onDelete: (tipId: string) => void;
  onLike: (tipId: string) => void;
  currentUserId?: string;
}

export function TipCard({ tip, fixture, onDelete, onLike, currentUserId }: TipCardProps) {
  const [isLiking, setIsLiking] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Safety checks for tip object
  if (!tip || !fixture) {
    return null;
  }

  const isOwnTip = currentUserId === tip.userId;
  const canDelete = isOwnTip && tip.status === TipStatus.PENDING;

  // Format tip type for display
  const formatTipType = (tipType: TipType): string => {
    const typeMap: Record<TipType, string> = {
      [TipType.BTTS]: 'Both Teams To Score',
      [TipType.OVER_UNDER_GOALS]: 'Over/Under Goals',
      [TipType.OVER_UNDER_CORNERS]: 'Over/Under Corners',
      [TipType.OVER_UNDER_CARDS]: 'Over/Under Cards',
      [TipType.MATCH_RESULT]: 'Match Result',
      [TipType.DOUBLE_CHANCE]: 'Double Chance',
      [TipType.FIRST_GOAL]: 'First Goal',
      [TipType.HANDICAP]: 'Handicap',
      [TipType.CORRECT_SCORE]: 'Correct Score',
      [TipType.HALF_TIME_RESULT]: 'Half Time Result'
    };
    return typeMap[tipType] || tipType;
  };

  // Format tip details for display
  const formatTipDetails = (): string => {
    const { details } = tip;
    
    switch (tip.tipType) {
      case TipType.BTTS:
        return `BTTS ${details.bttsValue === 'yes' ? 'Yes' : 'No'}`;
      
      case TipType.OVER_UNDER_GOALS:
        return `${details.overUnder === 'over' ? 'Over' : 'Under'} ${details.line} Goals`;
      
      case TipType.OVER_UNDER_CORNERS:
        return `${details.overUnder === 'over' ? 'Over' : 'Under'} ${details.line} Corners`;
      
      case TipType.OVER_UNDER_CARDS:
        return `${details.overUnder === 'over' ? 'Over' : 'Under'} ${details.line} Cards`;
      
      case TipType.MATCH_RESULT:
        const resultMap = { home: fixture.teams.home.name, draw: 'Draw', away: fixture.teams.away.name };
        return resultMap[details.result as keyof typeof resultMap] || details.result || '';
      
      case TipType.DOUBLE_CHANCE:
        return `Double Chance ${details.doubleChance}`;

      case TipType.FIRST_GOAL:
        const firstGoalTeam = details.firstGoalTeam === 'home' ? fixture.teams.home.name : fixture.teams.away.name;
        return `${firstGoalTeam} First Goal`;
      
      case TipType.HANDICAP:
        const handicapTeam = details.handicapTeam === 'home' ? fixture.teams.home.name : fixture.teams.away.name;
        const handicapValue = details.handicapValue ?? 0;
        return `${handicapTeam} ${handicapValue > 0 ? '+' : ''}${handicapValue}`;
      
      case TipType.CORRECT_SCORE:
        return `${details.homeScore}-${details.awayScore}`;
      
      case TipType.HALF_TIME_RESULT:
        const htResultMap = { home: fixture.teams.home.name, draw: 'Draw', away: fixture.teams.away.name };
        return `HT: ${htResultMap[details.halfTimeResult as keyof typeof htResultMap] || details.halfTimeResult}`;

      default:
        return 'Unknown tip type';
    }
  };

  // Get status icon and color
  const getStatusDisplay = () => {
    switch (tip.status) {
      case TipStatus.PENDING:
        return { icon: Clock, color: 'text-yellow-500', bg: 'bg-yellow-500/10', label: 'Pending' };
      case TipStatus.WON:
        return { icon: CheckCircle, color: 'text-green-500', bg: 'bg-green-500/10', label: 'Won' };
      case TipStatus.LOST:
        return { icon: XCircle, color: 'text-red-500', bg: 'bg-red-500/10', label: 'Lost' };
      case TipStatus.VOID:
        return { icon: AlertCircle, color: 'text-gray-500', bg: 'bg-gray-500/10', label: 'Void' };
      case TipStatus.PUSH:
        return { icon: AlertCircle, color: 'text-blue-500', bg: 'bg-blue-500/10', label: 'Push' };
      default:
        return { icon: Clock, color: 'text-gray-500', bg: 'bg-gray-500/10', label: 'Unknown' };
    }
  };

  // Handle like/unlike
  const handleLike = async () => {
    if (isLiking || !currentUserId) return;

    try {
      setIsLiking(true);
      await api.likeTip(tip._id!, 'like');
      onLike(tip._id!);
    } catch (error) {
      console.error('Error liking tip:', error);
    } finally {
      setIsLiking(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (isDeleting || !canDelete) return;
    
    try {
      setIsDeleting(true);
      await api.deleteTip(tip._id!);
      onDelete(tip._id!);
    } catch (error) {
      console.error('Error deleting tip:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const statusDisplay = getStatusDisplay();
  const StatusIcon = statusDisplay.icon;

  return (
    <div className="space-y-3">
      {/* User Info and Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={tip.user.profileImage} />
            <AvatarFallback>
              {tip.user.name?.charAt(0).toUpperCase() || 'U'}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="font-medium text-sm">{tip.user.name || 'Anonymous'}</div>
            <div className="text-xs text-muted-foreground">
              {(() => {
                try {
                  const createdDate = new Date(tip.createdAt);
                  if (isNaN(createdDate.getTime())) {
                    return 'Recently';
                  }
                  return formatDistanceToNow(createdDate, { addSuffix: true });
                } catch {
                  return 'Recently';
                }
              })()}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className={`${statusDisplay.bg} ${statusDisplay.color} border-current`}>
            <StatusIcon className="h-3 w-3 mr-1" />
            {statusDisplay.label}
          </Badge>
        </div>
      </div>

      {/* Tip Details */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div>
            <div className="text-sm text-muted-foreground">{formatTipType(tip.tipType)}</div>
            <div className="font-semibold">{formatTipDetails()}</div>
          </div>
          <div className="text-right">
            <div className="text-sm text-muted-foreground">Odds</div>
            <div className="font-bold text-lg">{tip.odds ? tip.odds.toFixed(2) : '0.00'}</div>
          </div>
        </div>
        
        {tip.description && (
          <div className="text-sm text-muted-foreground bg-muted/50 rounded-md p-2">
            {tip.description}
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex items-center justify-between pt-2">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLike}
            disabled={isLiking || !currentUserId}
            className="flex items-center gap-1 text-muted-foreground hover:text-red-500"
          >
            <Heart className="h-4 w-4" />
            <span>{typeof tip.likes === 'number' ? tip.likes : 0}</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            disabled
            className="flex items-center gap-1 text-muted-foreground"
          >
            <MessageCircle className="h-4 w-4" />
            <span>{typeof tip.comments === 'number' ? tip.comments : 0}</span>
          </Button>
        </div>
        
        {canDelete && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDelete}
            disabled={isDeleting}
            className="text-muted-foreground hover:text-red-500"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}
