'use client';

import React from 'react';
import { Fixture } from '@/lib/types';
import { SmartImage } from '@/components/ui/smart-image';

interface BulkNextFixtureIndicatorProps {
  teamId: number;
  nextFixtures: Fixture[];
  isLoading?: boolean;
}

export const BulkNextFixtureIndicator = ({ 
  teamId, 
  nextFixtures, 
  isLoading 
}: BulkNextFixtureIndicatorProps) => {
  if (isLoading) {
    return (
      <div className="w-6 h-6 mx-auto bg-muted animate-pulse rounded-full" />
    );
  }

  // Find the next fixture for this team
  const nextFixture = nextFixtures.find(fixture => 
    fixture.teams.home.id === teamId || fixture.teams.away.id === teamId
  );

  if (!nextFixture) {
    return (
      <div className="w-6 h-6 mx-auto bg-muted rounded-full flex items-center justify-center">
        <span className="text-xs text-muted-foreground">?</span>
      </div>
    );
  }

  const opponentTeam = nextFixture.teams.home.id === teamId 
    ? nextFixture.teams.away 
    : nextFixture.teams.home;

  return (
    <div className="w-6 h-6 relative flex-shrink-0 mx-auto" title={`Next: vs ${opponentTeam.name}`}>
      <SmartImage
        src={opponentTeam.logo || '/placeholder-team.png'}
        alt={opponentTeam.name || 'Opponent'}
        width={24}
        height={24}
        className="object-contain w-full h-full"
      />
    </div>
  );
};
