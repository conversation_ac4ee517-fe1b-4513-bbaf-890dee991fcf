'use client';


import { Fixture, isLiveMatch, isFinishedMatch } from '@/lib/types';
import { MatchEvents } from '@/components/features/MatchEvents';
import { RecentForm } from '@/components/features/RecentForm';
import { MatchInformation } from '@/components/features/MatchInformation';

interface FixtureOverviewProps {
  fixture: Fixture;
}

export function FixtureOverview({ fixture }: FixtureOverviewProps) {
  const isLive = isLiveMatch(fixture.fixture.status.short);
  const isFinished = isFinishedMatch(fixture.fixture.status.short);

  return (
    <div className="space-y-6">
      {/* Recent Form (for all match types) */}
      <RecentForm fixture={fixture} />

      {/* Match Events (only for live and finished matches) */}
      {(isLive || isFinished) && (
        <MatchEvents fixture={fixture} />
      )}

      {/* Match Information (for all match types) */}
      <MatchInformation fixture={fixture} />

    </div>
  );
}
