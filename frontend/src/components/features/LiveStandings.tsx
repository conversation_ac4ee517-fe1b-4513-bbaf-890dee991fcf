'use client';

import React from 'react';
import Image from 'next/image';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useLiveStandings, LiveStanding } from '@/hooks/useLiveStandings';
import { Standing } from '@/lib/types';
import { ArrowUp, ArrowDown, Minus, Wifi, WifiOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LiveStandingsProps {
  leagueId: number;
  season: number;
  baseStandings: Standing[][];
  leagueName: string;
  className?: string;
  enableLiveUpdates?: boolean;
}

interface StandingRowProps {
  standing: LiveStanding;
  isLive: boolean;
}

/**
 * Component for individual standing row
 */
const StandingRow: React.FC<StandingRowProps> = ({ standing, isLive }) => {
  const positionChange = (standing as LiveStanding).positionChange || 0;
  
  // Get position change indicator
  const getPositionChangeIcon = () => {
    if (positionChange > 0) {
      return <ArrowUp className="h-3 w-3 text-green-500" />;
    } else if (positionChange < 0) {
      return <ArrowDown className="h-3 w-3 text-red-500" />;
    }
    return <Minus className="h-3 w-3 text-gray-400" />;
  };

  // Get live match info
  const liveMatch = standing.liveMatches?.[0];
  const liveScore = liveMatch ? 
    `${liveMatch.homeAway === 'home' ? liveMatch.currentScore.home : liveMatch.currentScore.away}-${liveMatch.homeAway === 'home' ? liveMatch.currentScore.away : liveMatch.currentScore.home}` 
    : null;

  return (
    <tr className={cn(
      "border-b border-border hover:bg-muted/50 transition-colors",
      isLive && "bg-blue-50 dark:bg-blue-950/20"
    )}>
      {/* Position */}
      <td className="p-3 text-center font-medium">
        <div className="flex items-center justify-center gap-1">
          <span>{standing.rank}</span>
          {positionChange !== 0 && getPositionChangeIcon()}
        </div>
      </td>

      {/* Team */}
      <td className="p-3">
        <div className="flex items-center gap-2">
          {standing.team.logo && (
            <Image
              src={standing.team.logo}
              alt={standing.team.name}
              width={20}
              height={20}
              className="w-5 h-5 object-contain"
            />
          )}
          <span className="font-medium">{standing.team.name}</span>
          {isLive && (
            <div className="w-1.5 h-1.5 bg-red-500 rounded-full flex-shrink-0" />
          )}
          {isLive && liveScore && (
            <Badge variant="secondary" className="text-xs px-1 py-0 ml-1">
              {liveScore}
            </Badge>
          )}
        </div>
      </td>

      {/* Played */}
      <td className="p-3 text-center">
        <span className={cn(isLive && "text-red-500")}>
          {standing.all.played}
        </span>
      </td>

      {/* Won */}
      <td className="p-3 text-center">
        <span className={cn(isLive && "text-red-500")}>
          {standing.all.win}
        </span>
      </td>

      {/* Drawn */}
      <td className="p-3 text-center">
        <span className={cn(isLive && "text-red-500")}>
          {standing.all.draw}
        </span>
      </td>

      {/* Lost */}
      <td className="p-3 text-center">
        <span className={cn(isLive && "text-red-500")}>
          {standing.all.lose}
        </span>
      </td>

      {/* Goals For */}
      <td className="p-3 text-center">{standing.all.goals.for}</td>

      {/* Goals Against */}
      <td className="p-3 text-center">{standing.all.goals.against}</td>

      {/* Goal Difference */}
      <td className="p-3 text-center">
        <span className={cn(
          "font-medium",
          standing.goalsDiff > 0 && "text-green-600",
          standing.goalsDiff < 0 && "text-red-600"
        )}>
          {standing.goalsDiff > 0 ? '+' : ''}{standing.goalsDiff}
        </span>
      </td>

      {/* Points */}
      <td className="p-3 text-center">
        <span className={cn("font-bold", isLive && "text-red-500")}>
          {standing.points}
        </span>
      </td>

      {/* Form */}
      <td className="p-3">
        <div className="flex gap-1">
          {standing.form?.split('').slice(0, 5).map((result, i) => (
            <div
              key={i}
              className={cn(
                "w-5 h-5 rounded-sm flex items-center justify-center text-xs font-bold text-white",
                result === 'W' && "bg-green-500",
                result === 'D' && "bg-yellow-500",
                result === 'L' && "bg-red-500"
              )}
            >
              {result}
            </div>
          ))}
        </div>
      </td>
    </tr>
  );
};

/**
 * Live Standings Component
 */
export const LiveStandings: React.FC<LiveStandingsProps> = ({
  leagueId,
  season,
  baseStandings,
  leagueName,
  className,
  enableLiveUpdates = true
}) => {
  const {
    liveStandings,
    isConnected,
    lastUpdated,
    liveTeams,
    error,
    reconnect
  } = useLiveStandings({
    leagueId,
    season,
    baseStandings,
    enableLiveUpdates
  });

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {leagueName} Standings
            {enableLiveUpdates && (
              <div className="flex items-center gap-1">
                {isConnected ? (
                  <Wifi className="h-4 w-4 text-green-500" />
                ) : (
                  <WifiOff className="h-4 w-4 text-red-500" />
                )}
                {liveTeams.size > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {liveTeams.size} live
                  </Badge>
                )}
              </div>
            )}
          </CardTitle>
          
          {error && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-red-500">{error}</span>
              <button
                onClick={reconnect}
                className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
              >
                Retry
              </button>
            </div>
          )}
        </div>
        
        {lastUpdated && (
          <p className="text-sm text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        )}
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border bg-muted/50">
                <th className="p-3 text-center font-semibold">#</th>
                <th className="p-3 text-left font-semibold">Team</th>
                <th className="p-3 text-center font-semibold">P</th>
                <th className="p-3 text-center font-semibold">W</th>
                <th className="p-3 text-center font-semibold">D</th>
                <th className="p-3 text-center font-semibold">L</th>
                <th className="p-3 text-center font-semibold">GF</th>
                <th className="p-3 text-center font-semibold">GA</th>
                <th className="p-3 text-center font-semibold">GD</th>
                <th className="p-3 text-center font-semibold">Pts</th>
                <th className="p-3 text-center font-semibold">Form</th>
              </tr>
            </thead>
            <tbody>
              {liveStandings.map((group, groupIndex) => (
                <React.Fragment key={groupIndex}>
                  {group.map((standing) => (
                    <StandingRow
                      key={standing.team.id}
                      standing={standing as LiveStanding}
                      isLive={liveTeams.has(standing.team.id)}
                    />
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>

        {/* Legend */}
        {liveTeams.size > 0 && (
          <div className="p-3 border-t border-border bg-muted/25">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                <span>Live match</span>
              </div>
              <div className="flex items-center gap-1">
                <ArrowUp className="h-3 w-3 text-green-500" />
                <span>Position up</span>
              </div>
              <div className="flex items-center gap-1">
                <ArrowDown className="h-3 w-3 text-red-500" />
                <span>Position down</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
