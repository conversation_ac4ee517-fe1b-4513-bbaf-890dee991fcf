'use client';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import {
  TrendingUp,
  Target,
  DollarSign,
  Medal,
  Crown
} from 'lucide-react';
import { TipsterStats } from '@/lib/types';
import Link from 'next/link';

interface TipsterCardProps {
  tipster: TipsterStats;
  rank: number;
  sortBy: 'profit' | 'hitRate' | 'yield';
}

export function TipsterCard({ tipster, rank, sortBy }: TipsterCardProps) {
  // Get user initials for avatar fallback
  const getInitials = (name?: string) => {
    if (!name) return 'U';
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Format profit with proper sign and color
  const formatProfit = (profit: number) => {
    const sign = profit >= 0 ? '+' : '';
    return `${sign}${profit.toFixed(2)}`;
  };

  // Get profit color class
  const getProfitColor = (profit: number) => {
    if (profit > 0) return 'text-green-500';
    if (profit < 0) return 'text-red-500';
    return 'text-muted-foreground';
  };

  // Get rank icon based on position
  const getRankIcon = (position: number) => {
    switch (position) {
      case 1:
        return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2:
        return <Medal className="h-5 w-5 text-gray-400" />;
      case 3:
        return <Medal className="h-5 w-5 text-amber-600" />;
      default:
        return <span className="text-lg font-bold text-muted-foreground">#{position}</span>;
    }
  };

  // Get highlighted metric based on sort criteria
  const getHighlightedMetric = () => {
    switch (sortBy) {
      case 'profit':
        return {
          value: formatProfit(tipster.profit || 0),
          label: 'Profit',
          color: getProfitColor(tipster.profit || 0),
          icon: <DollarSign className="h-4 w-4" />
        };
      case 'hitRate':
        return {
          value: `${(tipster.hitRate || 0).toFixed(1)}%`,
          label: 'Hit Rate',
          color: 'text-blue-500',
          icon: <Target className="h-4 w-4" />
        };
      case 'yield':
        return {
          value: `${(tipster.yield || 0).toFixed(1)}%`,
          label: 'Yield',
          color: 'text-purple-500',
          icon: <TrendingUp className="h-4 w-4" />
        };
      default:
        return {
          value: formatProfit(tipster.profit || 0),
          label: 'Profit',
          color: getProfitColor(tipster.profit || 0),
          icon: <DollarSign className="h-4 w-4" />
        };
    }
  };

  const highlightedMetric = getHighlightedMetric();

  // Generate username slug for profile link
  const displayName = tipster.userName || `User ${tipster.userId.slice(-6)}`;
  const usernameSlug = tipster.username || `user-${tipster.userId.slice(-6)}`;

  return (
    <Card className="rounded-lg border bg-card text-card-foreground shadow-sm hover:shadow-md transition-shadow">
      <CardContent className="p-3.5">
        <div className="flex items-center gap-4">
          {/* Rank */}
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-muted">
            {getRankIcon(rank)}
          </div>

          {/* Avatar and User Info */}
          <div className="flex items-center gap-3 flex-1">
            <Avatar className="h-10 w-10">
              <AvatarImage src={undefined} alt={displayName} />
              <AvatarFallback className="text-sm">
                {getInitials(displayName)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 min-w-0">
              <Link
                href={`/tipster/${usernameSlug}`}
                className="font-semibold hover:underline truncate block"
              >
                {displayName}
              </Link>
              <div className="text-sm text-muted-foreground">
                {tipster.totalTips} tips • Avg odds {(tipster.averageOdds || 0).toFixed(2)}
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-3 gap-4 text-center">
            {/* Highlighted Metric */}
            <div className="space-y-1">
              <div className="flex items-center justify-center gap-1">
                {highlightedMetric.icon}
                <span className="text-xs text-muted-foreground">{highlightedMetric.label}</span>
              </div>
              <div className={`font-bold ${highlightedMetric.color}`}>
                {highlightedMetric.value}
              </div>
            </div>

            {/* Secondary Metrics */}
            {sortBy !== 'hitRate' && (
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <Target className="h-3 w-3" />
                  <span className="text-xs text-muted-foreground">Hit Rate</span>
                </div>
                <div className="font-semibold text-blue-500">
                  {(tipster.hitRate || 0).toFixed(1)}%
                </div>
              </div>
            )}

            {sortBy !== 'yield' && (
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <TrendingUp className="h-3 w-3" />
                  <span className="text-xs text-muted-foreground">Yield</span>
                </div>
                <div className="font-semibold text-purple-500">
                  {(tipster.yield || 0).toFixed(1)}%
                </div>
              </div>
            )}

            {sortBy !== 'profit' && (
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <DollarSign className="h-3 w-3" />
                  <span className="text-xs text-muted-foreground">Profit</span>
                </div>
                <div className={`font-semibold ${getProfitColor(tipster.profit || 0)}`}>
                  {formatProfit(tipster.profit || 0)}
                </div>
              </div>
            )}
          </div>

          {/* Streak Badge */}
          {tipster.currentStreak && tipster.currentStreak > 0 && (
            <Badge 
              variant={tipster.currentStreakType === 'win' ? 'default' : 'destructive'}
              className="ml-2"
            >
              {tipster.currentStreakType === 'win' ? '🔥' : '❄️'} {tipster.currentStreak}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
