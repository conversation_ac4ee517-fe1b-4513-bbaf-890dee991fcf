'use client';

import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Target, AlertCircle } from 'lucide-react';
import { Fixture, TipType, TipDetails, TipWithDetails, BestOddsResult, Odds, Bet, BetValue } from '@/lib/types';
import { api } from '@/lib/api';

interface AddTipModalProps {
  fixture: Fixture;
  onClose: () => void;
  onTipCreated: (tip: TipWithDetails) => void;
}

export function AddTipModal({ fixture, onClose, onTipCreated }: AddTipModalProps) {
  const [tipType, setTipType] = useState<TipType | ''>('');
  const [details, setDetails] = useState<TipDetails>({ market: 'fulltime' });
  const [odds, setOdds] = useState('');
  const [stake, setStake] = useState('10'); // Default stake to 10
  const [description, setDescription] = useState('');
  const [isPublic, setIsPublic] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fetchingOdds, setFetchingOdds] = useState(false);
  const [availableOdds, setAvailableOdds] = useState<BestOddsResult | null>(null);
  const [fixtureOdds, setFixtureOdds] = useState<Odds[]>([]);

  // Tip type options
  const tipTypeOptions = [
    { value: TipType.MATCH_RESULT, label: 'Match Result (1X2)' },
    { value: TipType.BTTS, label: 'Both Teams To Score' },
    { value: TipType.OVER_UNDER_GOALS, label: 'Over/Under Goals' },
    { value: TipType.OVER_UNDER_CORNERS, label: 'Over/Under Corners' },
    { value: TipType.OVER_UNDER_CARDS, label: 'Over/Under Cards' },
    { value: TipType.DOUBLE_CHANCE, label: 'Double Chance' },
    { value: TipType.FIRST_GOAL, label: 'First Goal' },
    { value: TipType.HANDICAP, label: 'Handicap' },
    { value: TipType.CORRECT_SCORE, label: 'Correct Score' },
    { value: TipType.HALF_TIME_RESULT, label: 'Half Time Result' }
  ];

  // Define which markets are available for each tip type
  const getAvailableMarkets = (tipType: TipType): Array<{ value: string; label: string }> => {
    switch (tipType) {
      case TipType.MATCH_RESULT:
      case TipType.BTTS:
      case TipType.OVER_UNDER_GOALS:
      case TipType.OVER_UNDER_CORNERS:
      case TipType.OVER_UNDER_CARDS:
      case TipType.DOUBLE_CHANCE:
      case TipType.HANDICAP:
        return [
          { value: 'fulltime', label: 'Full Time' },
          { value: 'firsthalf', label: 'First Half' },
          { value: 'secondhalf', label: 'Second Half' }
        ];
      case TipType.CORRECT_SCORE:
        return [
          { value: 'fulltime', label: 'Full Time' }
        ];
      case TipType.HALF_TIME_RESULT:
        return [
          { value: 'firsthalf', label: 'First Half' }
        ];
      case TipType.FIRST_GOAL:
        return [
          { value: 'fulltime', label: 'Full Time' }
        ];
      default:
        return [
          { value: 'fulltime', label: 'Full Time' }
        ];
    }
  };

  // Validate tip details based on type
  const validateTipDetails = useCallback((): boolean => {
    switch (tipType) {
      case TipType.BTTS:
        return !!details.bttsValue;
      case TipType.OVER_UNDER_GOALS:
      case TipType.OVER_UNDER_CORNERS:
      case TipType.OVER_UNDER_CARDS:
        return !!details.overUnder && details.line !== undefined;
      case TipType.MATCH_RESULT:
        return !!details.result;
      case TipType.HALF_TIME_RESULT:
        return !!details.halfTimeResult;
      case TipType.DOUBLE_CHANCE:
        return !!details.doubleChance;
      case TipType.FIRST_GOAL:
        return !!details.firstGoalTeam;
      case TipType.CORRECT_SCORE:
        return details.homeScore !== undefined && details.awayScore !== undefined;
      case TipType.HANDICAP:
        return !!details.handicapTeam && details.handicapValue !== undefined;
      default:
        return false;
    }
  }, [tipType, details]);



  // Fetch all fixture odds for enhanced UI
  const fetchFixtureOdds = useCallback(async () => {
    try {
      const response = await fetch(`https://api.kickoffpredictions.com/api/odds?fixture=${fixture.fixture.id}`, {
        headers: {
          'X-API-Key': '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFixtureOdds(data.response || []);
      }
    } catch (error) {
      console.error('Failed to fetch fixture odds:', error);
    }
  }, [fixture.fixture.id]);

  // Fetch odds when tip type and details are complete
  const fetchOdds = useCallback(async () => {
    if (!tipType || !validateTipDetails()) return;

    setFetchingOdds(true);
    setError(null);

    try {
      const result = await api.getBestOdds(
        fixture.fixture.id,
        tipType as TipType,
        details,
        false // Use pre-match odds for now
      );

      setAvailableOdds(result);

      // Auto-populate odds if available
      if (result.bestOdds) {
        setOdds(result.bestOdds.decimal.toString());
      }
    } catch (error) {
      console.error('Error fetching odds:', error);
      setError('Failed to fetch odds. You can enter odds manually.');
    } finally {
      setFetchingOdds(false);
    }
  }, [tipType, details, validateTipDetails, fixture.fixture.id]);

  // Effect to fetch fixture odds when modal opens
  useEffect(() => {
    fetchFixtureOdds();
  }, [fetchFixtureOdds]);

  // Effect to fetch odds when tip details change
  useEffect(() => {
    if (tipType && validateTipDetails()) {
      const timeoutId = setTimeout(() => {
        fetchOdds();
      }, 500); // Debounce to avoid too many API calls

      return () => clearTimeout(timeoutId);
    } else {
      setAvailableOdds(null);
      setOdds('');
    }
  }, [tipType, details, fetchOdds, validateTipDetails]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tipType || !odds) {
      setError('Please fill in all required fields');
      return;
    }

    if (!validateTipDetails()) {
      setError('Please complete all tip details');
      return;
    }

    const oddsValue = parseFloat(odds);
    if (isNaN(oddsValue) || oddsValue < 1.01) {
      setError('Odds must be a valid number greater than 1.0');
      return;
    }

    const stakeValue = stake ? parseFloat(stake) : undefined;
    if (stake && (isNaN(stakeValue!) || stakeValue! < 0)) {
      setError('Stake must be a valid positive number');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);

      const tipData = {
        fixtureId: fixture.fixture.id,
        tipType: tipType as TipType,
        details,
        odds: oddsValue,
        stake: stakeValue,
        description: description.trim() || undefined,
        isPublic
      };

      const newTip = await api.createTip(tipData);

      // Ensure the tip has all required fields with fallbacks
      const safeTip = {
        _id: newTip._id || 'temp-' + Date.now(),
        userId: newTip.userId || '',
        fixtureId: newTip.fixtureId || fixture.fixture.id,
        tipType: newTip.tipType || tipData.tipType,
        details: newTip.details || tipData.details,
        odds: newTip.odds || tipData.odds,
        stake: newTip.stake || tipData.stake,
        description: newTip.description || tipData.description,
        status: newTip.status || 'pending',
        createdAt: newTip.createdAt || new Date(),
        updatedAt: newTip.updatedAt || new Date(),
        isPublic: newTip.isPublic !== undefined ? newTip.isPublic : tipData.isPublic,
        likes: newTip.likes || 0,
        comments: newTip.comments || 0,
      };

      // Create a TipWithDetails object for the callback
      const tipWithDetails: TipWithDetails = {
        ...safeTip,
        user: {
          _id: safeTip.userId,
          name: 'You', // This will be updated by the parent component
        },
        fixture: {
          _id: fixture.fixture.id,
          teams: fixture.teams,
          league: fixture.league,
          fixture: {
            date: fixture.fixture.date,
            status: fixture.fixture.status
          }
        }
      };

      onTipCreated(tipWithDetails);
    } catch (err) {
      console.error('Error creating tip:', err);
      if (err instanceof Error) {
        // Try to extract more specific error message
        const errorMessage = err.message.includes('API Error:')
          ? err.message.replace('API Error:', '').trim()
          : err.message;
        setError(errorMessage || 'Failed to create tip. Please try again.');
      } else {
        setError('Failed to create tip. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get available lines for Over/Under markets
  const getAvailableLines = (betType: TipType) => {
    if (!fixtureOdds.length) return [];

    let targetBetId: number;
    switch (betType) {
      case TipType.OVER_UNDER_GOALS:
        targetBetId = 5; // Goals Over/Under
        break;
      case TipType.OVER_UNDER_CORNERS:
        targetBetId = 45; // Corners Over Under
        break;
      case TipType.OVER_UNDER_CARDS:
        targetBetId = 80; // Cards Over/Under
        break;
      default:
        return [];
    }

    const lines: Array<{ line: number; overOdds: string; underOdds: string }> = [];

    fixtureOdds.forEach(odds => {
      odds.bookmakers.forEach(bookmaker => {
        const bet = bookmaker.bets?.find((b: Bet) => b.id === targetBetId);
        if (bet?.values) {
          bet.values.forEach((value: BetValue) => {
          const overMatch = value.value?.match(/^Over\s+(\d+\.?\d*)$/);
          const underMatch = value.value?.match(/^Under\s+(\d+\.?\d*)$/);

          if (overMatch) {
            const line = parseFloat(overMatch[1]);
            const existing = lines.find(l => l.line === line);
            if (existing) {
              // Keep the best odds
              if (parseFloat(value.odd) > parseFloat(existing.overOdds)) {
                existing.overOdds = value.odd;
              }
            } else {
              lines.push({ line, overOdds: value.odd, underOdds: '0' });
            }
          } else if (underMatch) {
            const line = parseFloat(underMatch[1]);
            const existing = lines.find(l => l.line === line);
            if (existing) {
              if (parseFloat(value.odd) > parseFloat(existing.underOdds)) {
                existing.underOdds = value.odd;
              }
            } else {
              lines.push({ line, overOdds: '0', underOdds: value.odd });
            }
          }
          });
        }
      });
    });

    return lines.filter(l => l.overOdds !== '0' && l.underOdds !== '0').sort((a, b) => a.line - b.line);
  };

  // Get available handicap values
  const getAvailableHandicaps = () => {
    if (!fixtureOdds.length) return [];

    const handicaps: Array<{ value: number; team: 'home' | 'away'; odds: string }> = [];

    fixtureOdds.forEach(odds => {
      odds.bookmakers.forEach(bookmaker => {
        const bet = bookmaker.bets?.find((b: Bet) => b.id === 4); // Asian Handicap
        if (bet?.values) {
          bet.values.forEach((value: BetValue) => {
          const homeMatch = value.value?.match(/^Home\s*([+-]?\d+\.?\d*)$/);
          const awayMatch = value.value?.match(/^Away\s*([+-]?\d+\.?\d*)$/);

          if (homeMatch) {
            const handicapValue = parseFloat(homeMatch[1]);
            handicaps.push({ value: handicapValue, team: 'home', odds: value.odd });
          } else if (awayMatch) {
            const handicapValue = parseFloat(awayMatch[1]);
            handicaps.push({ value: handicapValue, team: 'away', odds: value.odd });
          }
          });
        }
      });
    });

    return handicaps.sort((a, b) => a.value - b.value);
  };

  // Render tip-specific form fields
  const renderTipSpecificFields = () => {
    switch (tipType) {
      case TipType.BTTS:
        return (
          <div className="space-y-2">
            <Label>Both Teams To Score</Label>
            <Select 
              value={details.bttsValue || ''} 
              onValueChange={(value) => setDetails({ ...details, bttsValue: value as 'yes' | 'no' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case TipType.MATCH_RESULT:
      case TipType.HALF_TIME_RESULT:
        return (
          <div className="space-y-2">
            <Label>{tipType === TipType.HALF_TIME_RESULT ? 'Half Time Result' : 'Match Result'}</Label>
            <Select 
              value={details.result || details.halfTimeResult || ''} 
              onValueChange={(value) => {
                if (tipType === TipType.HALF_TIME_RESULT) {
                  setDetails({ ...details, halfTimeResult: value as 'home' | 'draw' | 'away' });
                } else {
                  setDetails({ ...details, result: value as 'home' | 'draw' | 'away' });
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select result" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="home">{fixture.teams.home.name} Win</SelectItem>
                <SelectItem value="draw">Draw</SelectItem>
                <SelectItem value="away">{fixture.teams.away.name} Win</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case TipType.OVER_UNDER_GOALS:
      case TipType.OVER_UNDER_CORNERS:
      case TipType.OVER_UNDER_CARDS:
        const availableLines = getAvailableLines(tipType);

        return (
          <div className="space-y-4">
            {availableLines.length > 0 ? (
              <div className="space-y-3">
                <Label>Select Line & Over/Under</Label>
                <div className="grid gap-2">
                  {availableLines.map((lineData) => (
                    <div key={lineData.line} className="grid grid-cols-3 gap-2 items-center">
                      <div className="text-sm font-medium text-center">
                        {lineData.line}
                      </div>
                      <button
                        type="button"
                        className={`p-2 text-sm rounded border transition-colors ${
                          details.line === lineData.line && details.overUnder === 'under'
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-background hover:bg-muted border-border'
                        }`}
                        onClick={() => {
                          setDetails({ ...details, line: lineData.line, overUnder: 'under' });
                          setOdds(lineData.underOdds);
                        }}
                      >
                        Under {lineData.underOdds}
                      </button>
                      <button
                        type="button"
                        className={`p-2 text-sm rounded border transition-colors ${
                          details.line === lineData.line && details.overUnder === 'over'
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-background hover:bg-muted border-border'
                        }`}
                        onClick={() => {
                          setDetails({ ...details, line: lineData.line, overUnder: 'over' });
                          setOdds(lineData.overOdds);
                        }}
                      >
                        Over {lineData.overOdds}
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Over/Under</Label>
                  <Select
                    value={details.overUnder || ''}
                    onValueChange={(value) => setDetails({ ...details, overUnder: value as 'over' | 'under' })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="over">Over</SelectItem>
                      <SelectItem value="under">Under</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Line</Label>
                  <Input
                    type="number"
                    step="0.5"
                    placeholder="e.g., 2.5"
                    value={details.line || ''}
                    onChange={(e) => setDetails({ ...details, line: parseFloat(e.target.value) || undefined })}
                  />
                </div>
              </div>
            )}
          </div>
        );

      case TipType.DOUBLE_CHANCE:
        return (
          <div className="space-y-2">
            <Label>Double Chance</Label>
            <Select 
              value={details.doubleChance || ''} 
              onValueChange={(value) => setDetails({ ...details, doubleChance: value as '1X' | 'X2' | '12' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1X">{fixture.teams.home.name} or Draw</SelectItem>
                <SelectItem value="X2">Draw or {fixture.teams.away.name}</SelectItem>
                <SelectItem value="12">{fixture.teams.home.name} or {fixture.teams.away.name}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case TipType.FIRST_GOAL:
        return (
          <div className="space-y-2">
            <Label>First Goal Team</Label>
            <Select 
              value={details.firstGoalTeam || ''} 
              onValueChange={(value) => setDetails({ ...details, firstGoalTeam: value as 'home' | 'away' })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select team" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="home">{fixture.teams.home.name}</SelectItem>
                <SelectItem value="away">{fixture.teams.away.name}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );

      case TipType.CORRECT_SCORE:
        return (
          <div className="space-y-2">
            <Label>Correct Score</Label>
            <div className="grid grid-cols-3 gap-2 items-center">
              <Input
                type="number"
                min="0"
                placeholder="Home"
                value={details.homeScore !== undefined ? details.homeScore.toString() : ''}
                onChange={(e) => {
                  const value = e.target.value;
                  setDetails({
                    ...details,
                    homeScore: value === '' ? undefined : parseInt(value)
                  });
                }}
              />
              <div className="text-center font-bold">-</div>
              <Input
                type="number"
                min="0"
                placeholder="Away"
                value={details.awayScore !== undefined ? details.awayScore.toString() : ''}
                onChange={(e) => {
                  const value = e.target.value;
                  setDetails({
                    ...details,
                    awayScore: value === '' ? undefined : parseInt(value)
                  });
                }}
              />
            </div>
          </div>
        );

      case TipType.HANDICAP:
        const availableHandicaps = getAvailableHandicaps();
        const homeHandicaps = availableHandicaps.filter(h => h.team === 'home');
        const awayHandicaps = availableHandicaps.filter(h => h.team === 'away');

        return (
          <div className="space-y-4">
            {availableHandicaps.length > 0 ? (
              <div className="space-y-3">
                <Label>Select Team & Handicap</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-center">{fixture.teams.home.name}</div>
                    <div className="space-y-1">
                      {homeHandicaps.map((handicap) => (
                        <button
                          key={`home-${handicap.value}`}
                          type="button"
                          className={`w-full p-2 text-sm rounded border transition-colors ${
                            details.handicapTeam === 'home' && details.handicapValue === handicap.value
                              ? 'bg-primary text-primary-foreground border-primary'
                              : 'bg-background hover:bg-muted border-border'
                          }`}
                          onClick={() => {
                            setDetails({ ...details, handicapTeam: 'home', handicapValue: handicap.value });
                            setOdds(handicap.odds);
                          }}
                        >
                          {handicap.value >= 0 ? '+' : ''}{handicap.value} ({handicap.odds})
                        </button>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="text-sm font-medium text-center">{fixture.teams.away.name}</div>
                    <div className="space-y-1">
                      {awayHandicaps.map((handicap) => (
                        <button
                          key={`away-${handicap.value}`}
                          type="button"
                          className={`w-full p-2 text-sm rounded border transition-colors ${
                            details.handicapTeam === 'away' && details.handicapValue === handicap.value
                              ? 'bg-primary text-primary-foreground border-primary'
                              : 'bg-background hover:bg-muted border-border'
                          }`}
                          onClick={() => {
                            setDetails({ ...details, handicapTeam: 'away', handicapValue: handicap.value });
                            setOdds(handicap.odds);
                          }}
                        >
                          {handicap.value >= 0 ? '+' : ''}{handicap.value} ({handicap.odds})
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Team</Label>
                  <Select
                    value={details.handicapTeam || ''}
                    onValueChange={(value) => setDetails({ ...details, handicapTeam: value as 'home' | 'away' })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select team" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="home">{fixture.teams.home.name}</SelectItem>
                      <SelectItem value="away">{fixture.teams.away.name}</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label>Handicap</Label>
                  <Input
                    type="number"
                    step="0.5"
                    placeholder="e.g., -1.5"
                    value={details.handicapValue || ''}
                    onChange={(e) => setDetails({ ...details, handicapValue: parseFloat(e.target.value) || undefined })}
                  />
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Add New Tip
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Match Info */}
          <Card>
            <CardContent className="p-3">
              <div className="text-center">
                <div className="font-semibold">
                  {fixture.teams.home.name} vs {fixture.teams.away.name}
                </div>
                <div className="text-sm text-muted-foreground">
                  {fixture.league.name}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tip Type Selection */}
          <div className="space-y-2">
            <Label>Tip Type *</Label>
            <Select value={tipType} onValueChange={(value) => {
              setTipType(value as TipType);
              setDetails({});
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Select tip type" />
              </SelectTrigger>
              <SelectContent>
                {tipTypeOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Market Selection */}
          {tipType && (
            <div className="space-y-2">
              <Label>Market *</Label>
              <Select
                value={details.market || 'fulltime'}
                onValueChange={(value) => setDetails({ ...details, market: value as 'fulltime' | 'firsthalf' | 'secondhalf' })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select market" />
                </SelectTrigger>
                <SelectContent>
                  {getAvailableMarkets(tipType as TipType).map((market) => (
                    <SelectItem key={market.value} value={market.value}>
                      {market.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Tip-specific fields */}
          {tipType && renderTipSpecificFields()}

          <Separator />

          {/* Odds */}
          <div className="space-y-2">
            <Label>Odds *</Label>
            {fetchingOdds ? (
              <div className="flex items-center gap-2 p-2 border rounded-md bg-muted/50">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">Fetching best odds...</span>
              </div>
            ) : availableOdds && availableOdds.available ? (
              <div className="space-y-2">
                <div className="p-3 border rounded-md bg-green-50 dark:bg-green-950/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-green-700 dark:text-green-300">
                        Best Odds: {availableOdds.bestOdds?.decimal}
                      </div>
                      <div className="text-sm text-green-600 dark:text-green-400">
                        from {availableOdds.bestOdds?.bookmaker.name}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {availableOdds.allOdds.length} bookmaker{availableOdds.allOdds.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
                <Input
                  type="number"
                  step="0.01"
                  min="1.01"
                  placeholder="e.g., 2.50"
                  value={odds}
                  onChange={(e) => setOdds(e.target.value)}
                  required
                />
              </div>
            ) : (
              <div className="space-y-2">
                <Input
                  type="number"
                  step="0.01"
                  min="1.01"
                  placeholder="e.g., 2.50"
                  value={odds}
                  onChange={(e) => setOdds(e.target.value)}
                  required
                />
                {tipType && validateTipDetails() && (
                  <div className="text-sm text-muted-foreground">
                    No odds available for this selection. Please enter manually.
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Stake (optional) */}
          <div className="space-y-2">
            <Label>Stake (optional)</Label>
            <Input
              type="number"
              step="0.01"
              min="0"
              placeholder="e.g., 10.00"
              value={stake}
              onChange={(e) => setStake(e.target.value)}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label>Description (optional)</Label>
            <Textarea
              placeholder="Share your reasoning for this tip..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              maxLength={500}
              rows={3}
            />
          </div>

          {/* Public/Private toggle */}
          <div className="flex items-center justify-between">
            <Label>Make tip public</Label>
            <Switch
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>

          {/* Error message */}
          {error && (
            <div className="flex items-center gap-2 text-sm text-red-500 bg-red-50 dark:bg-red-950/20 p-2 rounded">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          )}

          {/* Submit buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={isSubmitting || !tipType || !odds}
            >
              {isSubmitting ? 'Creating...' : 'Create Tip'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
