"use client"

import * as React from "react"
import { DatePicker } from "@/components/ui/date-picker"

export function DatePickerExample() {
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(new Date())

  const handleDateChange = (date: Date | undefined) => {
    setSelectedDate(date)
    console.log("Selected date:", date)
  }

  return (
    <div className="p-6 space-y-6">
      <div className="space-y-2">
        <h2 className="text-lg font-semibold">Date Picker Examples</h2>
        <p className="text-sm text-muted-foreground">
          Click on any date picker button to open the calendar popover with navigation and Today button.
        </p>
      </div>

      <div className="space-y-4">
        {/* Default Date Picker */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Default Date Picker</label>
          <DatePicker
            date={selectedDate}
            onDateChange={handleDateChange}
            placeholder="Select a date"
          />
        </div>

        {/* Date Picker without Today button */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Without Today Button</label>
          <DatePicker
            date={selectedDate}
            onDateChange={handleDateChange}
            placeholder="Select a date"
            showTodayButton={false}
          />
        </div>

        {/* Date Picker with different variant */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Secondary Variant</label>
          <DatePicker
            date={selectedDate}
            onDateChange={handleDateChange}
            placeholder="Select a date"
            buttonVariant="secondary"
          />
        </div>

        {/* Date Picker with custom format */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Custom Date Format (MM/dd/yyyy)</label>
          <DatePicker
            date={selectedDate}
            onDateChange={handleDateChange}
            placeholder="Select a date"
            dateFormat="MM/dd/yyyy"
          />
        </div>

        {/* Multiple Date Pickers */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Multiple Date Pickers</label>
          <div className="flex gap-2 flex-wrap">
            <DatePicker
              date={selectedDate}
              onDateChange={handleDateChange}
              placeholder="Start Date"
            />
            <DatePicker
              date={selectedDate}
              onDateChange={handleDateChange}
              placeholder="End Date"
            />
          </div>
        </div>
      </div>

      {/* Display selected date */}
      {selectedDate && (
        <div className="p-4 bg-muted rounded-lg">
          <p className="text-sm">
            <strong>Selected Date:</strong> {selectedDate.toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      )}
    </div>
  )
}
