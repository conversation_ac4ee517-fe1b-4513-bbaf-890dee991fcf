'use client';

import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { FootballPitch } from './FootballPitch';
import { Fixture, isLiveMatch } from '@/lib/types';
import { useTeamRatings } from '@/hooks/useTeamRatings';

interface FixtureLineupsProps {
  fixture: Fixture;
}

export function FixtureLineups({ fixture }: FixtureLineupsProps) {
  const isLive = isLiveMatch(fixture.fixture.status.short);

  // Use team ratings hook for real-time updates
  const { teamRatings } = useTeamRatings({
    fixtureId: fixture.fixture.id,
    initialTeamRatings: fixture.teamRatings,
    enableRealTimeUpdates: isLive
  });

  // Create enhanced fixture with updated team ratings
  const enhancedFixture = {
    ...fixture,
    teamRatings: teamRatings || fixture.teamRatings
  };

  // Get lineups from fixture data
  const homeLineup = fixture.lineups?.find(lineup => lineup.team.id === fixture.teams.home.id);
  const awayLineup = fixture.lineups?.find(lineup => lineup.team.id === fixture.teams.away.id);

  // Check if we have lineup data with grid positions for pitch view
  const hasGridData = homeLineup?.startXI?.some(player => player.player.grid) &&
                      awayLineup?.startXI?.some(player => player.player.grid);

  const renderLineupList = (lineup: typeof homeLineup, teamName: string) => {
    if (!lineup) {
      return (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="font-semibold">{teamName}</h4>
            <Badge variant="outline">TBD</Badge>
          </div>
          <div className="text-center py-8 text-muted-foreground">
            <p>Lineup not available</p>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="font-semibold">{teamName}</h4>
          <Badge variant="outline">{lineup.formation || 'TBD'}</Badge>
        </div>

        <div className="space-y-2">
          {lineup.startXI?.map((playerData, index) => (
            <div key={index} className="flex items-center gap-3 p-2 bg-muted/30 rounded">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                {playerData.player.number || '?'}
              </div>
              <div className="flex-1">
                <div className="font-medium">{playerData.player.name}</div>
                <div className="text-sm text-muted-foreground">{playerData.player.pos || 'Unknown'}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Starting Lineups</h3>

      {hasGridData ? (
        <Tabs defaultValue="pitch" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="pitch">Pitch View</TabsTrigger>
            <TabsTrigger value="list">List View</TabsTrigger>
          </TabsList>

          <TabsContent value="pitch" className="mt-6">
            <FootballPitch fixture={enhancedFixture} />
          </TabsContent>

          <TabsContent value="list" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderLineupList(homeLineup, fixture.teams.home.name)}
              {renderLineupList(awayLineup, fixture.teams.away.name)}
            </div>
          </TabsContent>
        </Tabs>
      ) : (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {renderLineupList(homeLineup, fixture.teams.home.name)}
            {renderLineupList(awayLineup, fixture.teams.away.name)}
          </div>

          <div className="text-center text-sm text-muted-foreground">
            * Lineups are typically available 1-2 hours before kickoff
          </div>
        </div>
      )}
    </div>
  );
}
