'use client';

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Fixture } from '@/lib/types';

import { format } from 'date-fns';
import Image from 'next/image';
import { useQuery } from '@tanstack/react-query';
import { CalendarIcon, FootballFieldIcon, RefereeIcon } from '@/components/ui/icons';

interface MatchInformationProps {
  fixture: Fixture;
}

export function MatchInformation({ fixture }: MatchInformationProps) {
  const venue = fixture.fixture.venue;
  const fixtureDate = new Date(fixture.fixture.date);

  // Format the date and time
  const formattedDate = format(fixtureDate, 'dd MMM yyyy');
  const formattedTime = format(fixtureDate, 'HH:mm');

  // Fetch venue details if venue ID is available
  const { data: venueDetails } = useQuery({
    queryKey: ['venue', venue?.id],
    queryFn: async () => {
      if (!venue?.id) return null;
      try {
        const response = await fetch(`https://api.kickoffpredictions.com/api/venues?id=${venue.id}`);
        if (!response.ok) return null;
        return await response.json();
      } catch (error) {
        console.error('Failed to fetch venue details:', error);
        return null;
      }
    },
    enabled: !!venue?.id,
    staleTime: 24 * 60 * 60 * 1000, // Cache for 24 hours
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Match Information
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* League Information */}
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 flex items-center justify-center">
              {fixture.league.logo ? (
                <Image
                  src={fixture.league.logo}
                  alt={fixture.league.name}
                  width={24}
                  height={24}
                  className="object-contain"
                />
              ) : (
                <div className="w-6 h-6 bg-muted rounded"></div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm">{fixture.league.name}</div>
              <div className="text-sm text-muted-foreground">
                {fixture.league.round} - {fixture.league.season}
              </div>
            </div>
          </div>

          {/* Date & Time */}
          <div className="flex items-start gap-3">
            <div className="w-8 h-8 flex items-center justify-center">
              <CalendarIcon className="text-muted-foreground" width={20} height={20} />
            </div>
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm">Date & Time</div>
              <div className="text-sm text-muted-foreground">
                {formattedDate} at {formattedTime}
              </div>
            </div>
          </div>

          {/* Venue Information */}
          {venue && (venue.name || venue.city) && (
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 flex items-center justify-center">
                <FootballFieldIcon className="text-muted-foreground" width={20} height={20} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">Venue</div>
                <div className="text-sm text-muted-foreground">
                  {venue.name && (
                    <div>{venue.name}</div>
                  )}
                  {venue.city && (
                    <div>{venue.city}</div>
                  )}
                  {venueDetails?.capacity && (
                    <div>Capacity: {venueDetails.capacity.toLocaleString()}</div>
                  )}
                  {venueDetails?.surface && (
                    <div>Surface: {venueDetails.surface}</div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Referee Information */}
          {fixture.fixture.referee && (
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 flex items-center justify-center">
                <RefereeIcon className="text-muted-foreground" width={20} height={20} />
              </div>
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm">Referee</div>
                <div className="text-sm text-muted-foreground">
                  {fixture.fixture.referee}
                </div>
              </div>
            </div>
          )}


        </div>
      </CardContent>
    </Card>
  );
}
