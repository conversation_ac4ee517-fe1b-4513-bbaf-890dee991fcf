'use client';

import { Fixture } from '@/lib/types';
import { RadarChart } from './RadarChart';
import { InlineVoting } from './InlineVoting';
import { useFixturePredictions } from '@/hooks/useMatches';

interface WhoWillWinSectionProps {
  fixture: Fixture;
}

export function WhoWillWinSection({ fixture }: WhoWillWinSectionProps) {
  const { data: predictions } = useFixturePredictions(fixture.fixture.id);

  return (
    <div className="space-y-4">
      {/* Inline Voting Component */}
      <InlineVoting fixture={fixture} />

      {/* Team Attributes Radar Chart */}
      {predictions && (
        <RadarChart fixture={fixture} predictions={predictions} />
      )}
    </div>
  );
}
