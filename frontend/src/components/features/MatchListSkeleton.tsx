'use client';

import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface MatchListSkeletonProps {
  groupByLeague?: boolean;
  className?: string;
}

// Skeleton for individual match card
function MatchCardSkeleton({ isLast = false }: { isLast?: boolean }) {
  return (
    <div 
      className={cn(
        'flex items-center justify-between px-3.5 py-1.5',
        !isLast && 'border-b'
      )}
      style={{
        borderBottomColor: !isLast ? 'var(--match-card-border)' : 'transparent'
      }}
    >
      {/* Status with star icon skeleton */}
      <div className="flex flex-col justify-between mr-4 w-8 h-14">
        <div className="flex justify-center items-center h-6">
          <Skeleton className="h-3.5 w-3.5 rounded-full" />
        </div>
        <div className="flex justify-center items-center h-6">
          <Skeleton className="h-3 w-8 rounded" />
        </div>
      </div>

      {/* Teams and score skeleton */}
      <div className="flex-1 space-y-2">
        {/* Home team skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Team logo skeleton */}
            <Skeleton className="rounded-full w-[22px] h-[22px]" />
            {/* Team name skeleton */}
            <Skeleton className="h-4 w-24 rounded" />
          </div>
          {/* Score skeleton */}
          <Skeleton className="h-4 w-4 rounded" />
        </div>

        {/* Away team skeleton */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Team logo skeleton */}
            <Skeleton className="rounded-full w-[22px] h-[22px]" />
            {/* Team name skeleton */}
            <Skeleton className="h-4 w-28 rounded" />
          </div>
          {/* Score skeleton */}
          <Skeleton className="h-4 w-4 rounded" />
        </div>
      </div>
    </div>
  );
}

// Skeleton for league container
function LeagueContainerSkeleton() {
  return (
    <Card className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
      {/* League header skeleton */}
      <div className="flex items-center gap-3 px-4 py-2 border-b bg-muted">
        {/* Country flag skeleton */}
        <Skeleton className="h-[18px] w-[18px] sm:h-[20px] sm:w-[20px] rounded-full" />
        <div>
          {/* League name skeleton */}
          <Skeleton className="h-4 w-32 rounded bg-muted/30" />
        </div>
      </div>

      {/* Match cards skeleton */}
      <div>
        <MatchCardSkeleton isLast={false} />
        <MatchCardSkeleton isLast={false} />
        <MatchCardSkeleton isLast={true} />
      </div>
    </Card>
  );
}

// Skeleton for time-based container
function TimeContainerSkeleton() {
  return (
    <Card className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
      {/* Date header skeleton */}
      <div className="flex items-center gap-3 px-4 py-2 border-b" style={{backgroundColor: '#262626'}}>
        <div>
          {/* Date name skeleton */}
          <Skeleton className="h-4 w-20 rounded bg-muted/30" />
        </div>
      </div>

      {/* Match cards skeleton */}
      <div>
        <MatchCardSkeleton isLast={false} />
        <MatchCardSkeleton isLast={false} />
        <MatchCardSkeleton isLast={true} />
      </div>
    </Card>
  );
}

export function MatchListSkeleton({ 
  groupByLeague = true,
  className 
}: MatchListSkeletonProps) {
  if (groupByLeague) {
    // Show multiple league containers
    return (
      <div className={cn('space-y-3', className)}>
        <LeagueContainerSkeleton />
        <LeagueContainerSkeleton />
        <LeagueContainerSkeleton />
      </div>
    );
  } else {
    // Show single time-based container
    return (
      <div className={cn('space-y-3', className)}>
        <TimeContainerSkeleton />
      </div>
    );
  }
}
