'use client';

import { useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CircularProgress } from '@/components/ui/circular-progress';
import { Fixture, VoteOption, VoteCategory } from '@/lib/types';
import { getTeamLogoUrl } from '@/lib/utils';
import { useVoting, useVotePercentages, useHasUserVoted } from '@/hooks/useVoting';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InlineVotingProps {
  fixture: Fixture;
}

type VotingScreen = 'match_outcome' | 'btts' | 'over_under';

const VOTING_SCREENS: { key: VotingScreen; category: VoteCategory; title: string }[] = [
  { key: 'match_outcome', category: VoteCategory.MATCH_OUTCOME, title: 'Who will win?' },
  { key: 'btts', category: VoteCategory.BTTS, title: 'Both teams to score?' },
  { key: 'over_under', category: VoteCategory.OVER_UNDER, title: 'Total goals' },
];

export function InlineVoting({ fixture }: InlineVotingProps) {
  const [currentScreen, setCurrentScreen] = useState<VotingScreen>('match_outcome');
  const homeTeam = fixture.teams.home;
  const awayTeam = fixture.teams.away;

  // Session-based vote tracking to prevent double voting
  const sessionVotesRef = useRef<Map<string, VoteOption>>(new Map());
  const lastVoteTimeRef = useRef<Map<string, number>>(new Map());
  
  const currentCategory = VOTING_SCREENS.find(s => s.key === currentScreen)?.category || VoteCategory.MATCH_OUTCOME;
  const currentTitle = VOTING_SCREENS.find(s => s.key === currentScreen)?.title || 'Who will win?';

  // Check if voting should be disabled (fixture finished or in progress)
  const fixtureStatus = fixture.fixture.status.short;
  const isVotingClosed = ['FT', 'AET', 'PEN', '1H', '2H', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(fixtureStatus);

  const { mutate: castVote, isPending } = useVoting(fixture.fixture.id);
  const { percentages, totalVotes } = useVotePercentages(fixture.fixture.id, currentCategory);
  const { hasVoted, userVote } = useHasUserVoted(fixture.fixture.id, currentCategory);

  // Check if user has voted in current session for this category
  const sessionVoteKey = `${fixture.fixture.id}-${currentCategory}`;
  const hasVotedInSession = sessionVotesRef.current.has(sessionVoteKey);
  const sessionVote = sessionVotesRef.current.get(sessionVoteKey);

  // Debounced vote handler with additional safeguards
  const handleVote = useCallback((vote: VoteOption) => {
    const now = Date.now();
    const lastVoteTime = lastVoteTimeRef.current.get(sessionVoteKey) || 0;
    const timeSinceLastVote = now - lastVoteTime;
    
    // Prevent rapid clicking (debounce)
    if (timeSinceLastVote < 1000) {
      console.log('Vote blocked: Too soon after last vote');
      return;
    }
    
    // Prevent voting when disabled or closed
    if (isPending || isVotingClosed) {
      console.log('Vote blocked: Voting is disabled or closed');
      return;
    }
    
    // Prevent double voting in same session (unless changing vote)
    if (hasVotedInSession && sessionVote === vote) {
      console.log('Vote blocked: Same vote already cast in session');
      return;
    }
    
    // Record vote in session
    sessionVotesRef.current.set(sessionVoteKey, vote);
    lastVoteTimeRef.current.set(sessionVoteKey, now);
    
    // Cast the vote
    castVote({ vote, category: currentCategory });
  }, [isPending, isVotingClosed, hasVotedInSession, sessionVote, sessionVoteKey, currentCategory, castVote]);

  // Determine if button should be disabled
  const isButtonDisabled = useCallback((vote: VoteOption) => {
    const now = Date.now();
    const lastVoteTime = lastVoteTimeRef.current.get(sessionVoteKey) || 0;
    const timeSinceLastVote = now - lastVoteTime;
    
    return (
      isPending || 
      isVotingClosed || 
      timeSinceLastVote < 1000 || // Debounce protection
      (hasVotedInSession && sessionVote === vote) // Prevent same vote
    );
  }, [isPending, isVotingClosed, hasVotedInSession, sessionVote, sessionVoteKey]);

  // Determine which vote is currently selected (prioritize session vote over server vote)
  const getCurrentVote = useCallback(() => {
    return hasVotedInSession ? sessionVote : userVote;
  }, [hasVotedInSession, sessionVote, userVote]);

  const navigateScreen = (direction: 'prev' | 'next') => {
    const currentIndex = VOTING_SCREENS.findIndex(s => s.key === currentScreen);
    let newIndex;
    
    if (direction === 'prev') {
      newIndex = currentIndex > 0 ? currentIndex - 1 : VOTING_SCREENS.length - 1;
    } else {
      newIndex = currentIndex < VOTING_SCREENS.length - 1 ? currentIndex + 1 : 0;
    }
    
    setCurrentScreen(VOTING_SCREENS[newIndex].key);
  };

  const renderMatchOutcomeVoting = () => (
    <div className="flex items-center justify-center gap-6">
      {/* Home Team */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.home || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--primary))"
        >
          <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
                getCurrentVote() === VoteOption.HOME_WIN
                  ? "border-primary bg-primary/10 text-primary"
                  : "border-input hover:bg-accent hover:text-accent-foreground"
              )}
              onClick={() => handleVote(VoteOption.HOME_WIN)}
              disabled={isButtonDisabled(VoteOption.HOME_WIN)}
            >
            <div className="flex h-10 w-10 items-center justify-center overflow-hidden">
              <Image
                src={homeTeam.logo || getTeamLogoUrl(homeTeam.id)}
                alt={`${homeTeam.name} logo`}
                width={34}
                height={34}
                className="h-8 w-8 object-contain"
              />
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.home || 0}%</span>
        )}
      </div>

      {/* Draw */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.draw || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--muted-foreground))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.DRAW
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.DRAW)}
            disabled={isButtonDisabled(VoteOption.DRAW)}
          >
            <div className="flex h-10 w-10 items-center justify-center">
              <X className="h-6 w-6 text-muted-foreground" />
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.draw || 0}%</span>
        )}
      </div>

      {/* Away Team */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.away || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--destructive))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.AWAY_WIN
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.AWAY_WIN)}
            disabled={isButtonDisabled(VoteOption.AWAY_WIN)}
          >
            <div className="flex h-12 w-12 items-center justify-center overflow-hidden">
              <Image
                src={awayTeam.logo || getTeamLogoUrl(awayTeam.id)}
                alt={`${awayTeam.name} logo`}
                width={34}
                height={34}
                className="h-8 w-8 object-contain"
              />
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.away || 0}%</span>
        )}
      </div>
    </div>
  );

  const renderBTTSVoting = () => (
    <div className="flex items-center justify-center gap-8">
      {/* Yes */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.yes || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--primary))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.BTTS_YES
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.BTTS_YES)}
            disabled={isButtonDisabled(VoteOption.BTTS_YES)}
          >
            <div className="flex h-10 w-10 items-center justify-center">
              <span className="text-sm font-bold">YES</span>
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.yes || 0}%</span>
        )}
      </div>

      {/* No */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.no || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--destructive))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.BTTS_NO
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.BTTS_NO)}
            disabled={isButtonDisabled(VoteOption.BTTS_NO)}
          >
            <div className="flex h-10 w-10 items-center justify-center">
              <span className="text-sm font-bold">NO</span>
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.no || 0}%</span>
        )}
      </div>
    </div>
  );

  const renderOverUnderVoting = () => (
    <div className="flex items-center justify-center gap-8">
      {/* Under 2.5 */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.under || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--primary))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.UNDER_25
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.UNDER_25)}
            disabled={isButtonDisabled(VoteOption.UNDER_25)}
          >
            <div className="flex h-10 w-10 items-center justify-center">
              <span className="text-sm font-bold">-2.5</span>
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.under || 0}%</span>
        )}
      </div>

      {/* Over 2.5 */}
      <div className="flex flex-col items-center gap-2">
        <CircularProgress
          percentage={percentages.over || 0}
          size={70}
          strokeWidth={4}
          color="hsl(var(--destructive))"
        >
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-14 w-14 rounded-full p-0 border transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
              getCurrentVote() === VoteOption.OVER_25
                ? "border-primary bg-primary/10 text-primary"
                : "border-input hover:bg-accent hover:text-accent-foreground"
            )}
            onClick={() => handleVote(VoteOption.OVER_25)}
            disabled={isButtonDisabled(VoteOption.OVER_25)}
          >
            <div className="flex h-10 w-10 items-center justify-center">
              <span className="text-sm font-bold">+2.5</span>
            </div>
          </Button>
        </CircularProgress>
        {totalVotes > 0 && (
          <span className="text-xs font-medium text-center">{percentages.over || 0}%</span>
        )}
      </div>
    </div>
  );

  const renderCurrentVoting = () => {
    switch (currentScreen) {
      case 'match_outcome':
        return renderMatchOutcomeVoting();
      case 'btts':
        return renderBTTSVoting();
      case 'over_under':
        return renderOverUnderVoting();
      default:
        return renderMatchOutcomeVoting();
    }
  };

  return (
    <Card className="p-4">
      {/* Header with navigation */}
      <div className="flex items-center justify-between mb-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigateScreen('prev')}
          className="h-8 w-8"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold">{currentTitle}</h2>
        </div>
        
        <Button
          variant="ghost"
          size="icon"
          onClick={() => navigateScreen('next')}
          className="h-8 w-8"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      {/* Voting Interface */}
      <div className="py-6">
        {renderCurrentVoting()}
      </div>

      {/* Footer */}
      <div className="flex flex-col items-center justify-center text-xs text-muted-foreground gap-1">
        <span>{totalVotes.toLocaleString()} fans have voted</span>
        {isVotingClosed ? (
          <span className="text-xs text-muted-foreground/70">Voting closed</span>
        ) : hasVoted ? (
          <span className="text-xs text-primary">You can change your vote by selecting a different option</span>
        ) : null}
      </div>

      {/* Screen indicators */}
      <div className="flex justify-center gap-2 mt-3">
        {VOTING_SCREENS.map((screen) => (
          <div
            key={screen.key}
            className={cn(
              "h-1.5 w-6 rounded-full transition-colors",
              currentScreen === screen.key ? "bg-primary" : "bg-muted"
            )}
          />
        ))}
      </div>
    </Card>
  );
}
