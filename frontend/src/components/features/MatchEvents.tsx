'use client';

import React from 'react';
import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import { Fixture, MatchEvent } from '@/lib/types';
import {
  GoalIcon,
  PenaltyIcon,
  YellowCardIcon,
  RedCardIcon,
  SubstitutionIcon,
  VarIcon,
  PenaltyMissIcon,
  DoubleYellowIcon
} from '@/components/ui/icons';

interface MatchEventsProps {
  fixture: Fixture;
}

interface EventIconProps {
  type: string;
  detail: string;
  className?: string;
}

const EventIcon = ({ type, detail, className = "w-5 h-5" }: EventIconProps) => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  const lowerType = type.toLowerCase();
  const lowerDetail = detail.toLowerCase();
  const isDark = mounted && theme === 'dark';

  // <PERSON><PERSON> missed penalties FIRST (before checking for regular goals)
  if (lowerDetail.includes('missed penalty')) {
    return <PenaltyMissIcon className={className} width={20} height={20} />;
  }

  if (lowerType === 'goal') {
    // Handle penalty goals (but not missed penalties - those are handled above)
    if (lowerDetail.includes('penalty')) {
      return <PenaltyIcon className={className} width={20} height={20} />;
    }
    // Use same GoalIcon for both regular and own goals, just different colors
    const goalColor = lowerDetail.includes('own') ? 'text-red-500' : (isDark ? 'text-white' : 'text-black');
    return <GoalIcon className={`${className} ${goalColor}`} width={16} height={16} />;
  }

  // Handle other penalty events (if type is penalty)
  if (lowerType === 'penalty') {
    return <PenaltyIcon className={`${className} opacity-50`} width={20} height={20} />;
  }

  if (lowerType === 'card') {
    // Handle double yellow cards (second yellow = red)
    if (lowerDetail.includes('second yellow') || lowerDetail.includes('double yellow')) {
      return <DoubleYellowIcon className={className} width={20} height={20} />;
    }
    if (lowerDetail.includes('yellow')) {
      return <YellowCardIcon className={className} width={20} height={20} />;
    }
    if (lowerDetail.includes('red')) {
      return <RedCardIcon className={className} width={20} height={20} />;
    }
  }

  if (lowerType === 'subst') {
    return <SubstitutionIcon className={className} width={16} height={16} />;
  }

  if (lowerType === 'var') {
    // For VAR events, use the dedicated VAR icon - white in dark mode, black in light mode
    return <VarIcon className={`${className} ${isDark ? 'text-white' : 'text-black'}`} width={24} height={24} />;
  }

  // Default fallback - use regular goal styling
  return <GoalIcon className={`${className} ${isDark ? 'text-white' : 'text-black'}`} width={16} height={16} />;
};

export function MatchEvents({ fixture }: MatchEventsProps) {
  const events = fixture.events || [];

  if (events.length === 0) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <h3 className="text-lg font-semibold mb-4">Match Events</h3>
        <div className="text-center py-8 text-muted-foreground">
          No events recorded yet
        </div>
      </div>
    );
  }

  // Helper function to detect double yellow cards
  const getPlayerCardEvents = (playerId: number | undefined, playerName: string | undefined) => {
    if (!playerId && !playerName) return [];

    return events.filter(event =>
      event.type.toLowerCase() === 'card' &&
      (event.player?.id === playerId || event.player?.name === playerName)
    );
  };

  // Helper function to check if this is a second yellow card (should be shown as double yellow)
  const isSecondYellowCard = (event: MatchEvent) => {
    if (event.type.toLowerCase() !== 'card' || !event.detail.toLowerCase().includes('red')) {
      return false;
    }

    const playerCards = getPlayerCardEvents(event.player?.id, event.player?.name);
    const yellowCards = playerCards.filter(card =>
      card.detail.toLowerCase().includes('yellow') &&
      card.time.elapsed < event.time.elapsed
    );

    return yellowCards.length > 0;
  };

  // Filter out first yellow cards when player gets second yellow (double yellow)
  const filterEventsForDisplay = (events: MatchEvent[]) => {
    const filteredEvents: MatchEvent[] = [];

    // Track players who got double yellows
    const doubleYellowPlayers = new Set<string>();

    // First pass: identify players who got double yellows
    events.forEach(event => {
      if (isSecondYellowCard(event)) {
        const playerKey = `${event.player?.id || event.player?.name}`;
        doubleYellowPlayers.add(playerKey);
      }
    });

    // Second pass: filter out first yellow cards for players who got double yellows
    events.forEach(event => {
      const playerKey = `${event.player?.id || event.player?.name}`;

      // Skip first yellow cards for players who got double yellows
      if (event.type.toLowerCase() === 'card' &&
          event.detail.toLowerCase().includes('yellow') &&
          !event.detail.toLowerCase().includes('red') &&
          doubleYellowPlayers.has(playerKey)) {
        return; // Skip this first yellow card
      }

      filteredEvents.push(event);
    });

    return filteredEvents;
  };

  // Group events by period
  const groupEventsByPeriod = (events: MatchEvent[]) => {
    const periods: { [key: string]: MatchEvent[] } = {
      'FT': [],
      'HT': []
    };

    // First filter events to handle double yellows
    const filteredEvents = filterEventsForDisplay(events);

    filteredEvents.forEach(event => {
      const elapsed = event.time.elapsed;
      if (elapsed <= 45) {
        periods['HT'].push(event);
      } else {
        periods['FT'].push(event);
      }
    });

    return periods;
  };

  const eventsByPeriod = groupEventsByPeriod(events);
  const homeTeamId = fixture.teams.home.id;

  const renderEvent = (event: MatchEvent, index: number) => {
    const isHomeTeam = event.team.id === homeTeamId;
    const displayTime = event.time.extra
      ? `${event.time.elapsed}+${event.time.extra}'`
      : `${event.time.elapsed}'`;

    // Check if this is a second yellow card that should be shown as double yellow
    const isDoubleYellow = isSecondYellowCard(event);

    // Helper function to render event details based on type
    const renderEventDetails = (textAlign: 'left' | 'right') => {
      const eventType = event.type.toLowerCase();
      const eventDetail = event.detail.toLowerCase();

      // Handle missed penalties FIRST (before checking for regular goals)
      if (eventDetail.includes('missed penalty')) {
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
            <div className="text-xs text-muted-foreground">
              Missed Penalty
            </div>
          </div>
        );
      }

      if (eventType === 'goal') {
        // Handle penalty goals - don't show assist for penalties (but not missed penalties)
        if (eventDetail.includes('penalty')) {
          return (
            <div className={`text-${textAlign}`}>
              <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
              <div className="text-xs text-muted-foreground">
                Penalty
              </div>
            </div>
          );
        }

        // For regular goals: show player name and assist (if available)
        // For own goals: show 'own goal' comment
        if (eventDetail.includes('own')) {
          return (
            <div className={`text-${textAlign}`}>
              <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
              <div className="text-xs text-muted-foreground">
                Own Goal
              </div>
            </div>
          );
        }
        
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
            {event.assist?.name && (
              <div className="text-xs text-muted-foreground">
                Assist: {event.assist.name}
              </div>
            )}
          </div>
        );
      } else if (eventType === 'penalty') {
        // Handle other penalty events (if type is penalty)
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
            <div className="text-xs text-muted-foreground">
              Missed Penalty
            </div>
          </div>
        );
      } else if (eventType === 'var') {
        // Handle VAR events - show player name if available, otherwise VAR Review
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'VAR Review'}</div>
            <div className="text-xs text-muted-foreground">
              {event.detail || 'Video Assistant Referee'}
            </div>
          </div>
        );
      } else if (eventType === 'subst') {
        // For substitutions: show assist player name first, then "for" original player
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.assist?.name || 'Unknown'}</div>
            <div className="text-xs text-muted-foreground">
              for {event.player?.name || 'Unknown'}
            </div>
          </div>
        );
      } else if (eventType === 'card') {
        // Handle double yellow cards
        if (isDoubleYellow) {
          return (
            <div className={`text-${textAlign}`}>
              <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
              <div className="text-xs text-muted-foreground">
                Second Yellow Card
              </div>
            </div>
          );
        }

        // For regular cards: show player name and comments (not detail)
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
            {event.comments && (
              <div className="text-xs text-muted-foreground">
                for {event.comments}
              </div>
            )}
          </div>
        );
      } else if (eventType === 'var') {
        // For VAR events: show detail (like "Goal cancelled") and player if available
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">
              {event.player?.name ? event.player.name : 'VAR Review'}
            </div>
            <div className="text-xs text-muted-foreground">
              {event.detail}
            </div>
          </div>
        );
      } else {
        // For other events: show player name and comments (not detail)
        return (
          <div className={`text-${textAlign}`}>
            <div className="font-medium text-sm">{event.player?.name || 'Unknown'}</div>
            {event.comments && (
              <div className="text-xs text-muted-foreground">
                for {event.comments}
              </div>
            )}
          </div>
        );
      }
    };

    // Determine the correct detail for the icon
    const iconDetail = isDoubleYellow ? 'double yellow' : event.detail;

    return (
      <div key={index} className="flex items-center gap-3 py-2">
        {/* Home team events (left side) */}
        <div className="flex-1 flex justify-end">
          {isHomeTeam && (
            <div className="flex items-center gap-2">
              {renderEventDetails('right')}
              <EventIcon type={event.type} detail={iconDetail} />
            </div>
          )}
        </div>

        {/* Time in center */}
        <div className="flex-shrink-0 w-12 text-center">
          <span className="text-xs font-medium bg-muted px-2 py-1 rounded">
            {displayTime}
          </span>
        </div>

        {/* Away team events (right side) */}
        <div className="flex-1 flex justify-start">
          {!isHomeTeam && (
            <div className="flex items-center gap-2">
              <EventIcon type={event.type} detail={iconDetail} />
              {renderEventDetails('left')}
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderPeriodSection = (periodKey: string, periodEvents: MatchEvent[]) => {
    if (periodEvents.length === 0) return null;

    // Sort events by time
    const sortedEvents = [...periodEvents].sort((a, b) => {
      const timeA = a.time.elapsed + (a.time.extra || 0);
      const timeB = b.time.elapsed + (b.time.extra || 0);
      return timeA - timeB;
    });

    return (
      <div key={periodKey} className="space-y-2">
        {/* Period header */}
        <div className="flex justify-center py-2">
          <div className="bg-muted px-3 py-1 rounded-full">
            <span className="text-sm font-semibold">{periodKey}</span>
            {periodKey === 'HT' && (
              <span className="text-xs text-muted-foreground ml-1">
                {fixture.score.halftime.home}-{fixture.score.halftime.away}
              </span>
            )}
            {periodKey === 'FT' && (
              <span className="text-xs text-muted-foreground ml-1">
                {fixture.score.fulltime.home}-{fixture.score.fulltime.away}
              </span>
            )}
          </div>
        </div>

        {/* Events */}
        <div className="space-y-1">
          {sortedEvents.map((event, index) => renderEvent(event, index))}
        </div>
      </div>
    );
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
      <h3 className="text-lg font-semibold mb-4">Match Events</h3>
      
      <div className="space-y-4">
        {/* Render HT events first, then FT events */}
        {renderPeriodSection('HT', eventsByPeriod['HT'])}
        {renderPeriodSection('FT', eventsByPeriod['FT'])}
      </div>
    </div>
  );
}
