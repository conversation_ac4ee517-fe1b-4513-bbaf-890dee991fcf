'use client';

import { useFixtureStatistics } from '@/hooks/useMatches';
import { Fixture } from '@/lib/types';

interface FixtureStatsProps {
  fixture: Fixture;
}

export function FixtureStats({ fixture }: FixtureStatsProps) {
  const { data: statistics, isLoading } = useFixtureStatistics(fixture.fixture.id);

  if (isLoading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!statistics || statistics.length === 0) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics not available for this match.</p>
      </div>
    );
  }

  // Parse statistics data
  const homeStats = statistics.find((stat) => stat.team.id === fixture.teams.home.id);
  const awayStats = statistics.find((stat) => stat.team.id === fixture.teams.away.id);

  if (!homeStats || !awayStats) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <h3 className="text-lg font-semibold mb-4">Match Statistics</h3>
        <p className="text-muted-foreground">Statistics data incomplete.</p>
      </div>
    );
  }

  // Helper function to get stat value
  const getStatValue = (stats: { type: string; value: number | string | null }[], type: string): number => {
    const stat = stats.find(s => s.type === type);
    if (!stat || stat.value === null) return 0;

    // Handle percentage values
    if (typeof stat.value === 'string' && stat.value.includes('%')) {
      return parseInt(stat.value.replace('%', ''));
    }

    return typeof stat.value === 'number' ? stat.value : parseInt(stat.value.toString()) || 0;
  };

  // Simple color scheme for home/away stats
  const getTeamColors = (teamId: number) => {
    const colors = {
      home: { background: 'var(--team-home-primary)', text: 'var(--ui-text-white)' },
      away: { background: 'var(--team-away-primary)', text: 'var(--ui-text-white)' }
    };
    return teamId === fixture.teams.home.id ? colors.home : colors.away;
  };

  // Helper function to render Ball possession bar with percentages inside
  const renderBallPossessionBar = (homeValue: number, awayValue: number) => {
    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    return (
      <div className="mb-6">
        {/* Label */}
        <div className="text-center text-sm text-muted-foreground mb-2">
          Ball possession
        </div>

        {/* Progress Bar with percentages inside */}
        <div className="flex h-8 rounded-full overflow-hidden">
          {/* Home team bar */}
          <div
            className="flex items-center justify-start pl-3 text-sm font-medium text-white"
            style={{
              width: `${homeValue}%`,
              backgroundColor: homeColors.background
            }}
          >
            {homeValue >= 15 && <span>{homeValue}%</span>}
          </div>
          {/* Away team bar */}
          <div
            className="flex items-center justify-end pr-3 text-sm font-medium text-white"
            style={{
              width: `${awayValue}%`,
              backgroundColor: awayColors.background
            }}
          >
            {awayValue >= 15 && <span>{awayValue}%</span>}
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render simple stat row (value - label - value)
  const renderSimpleStatRow = (homeValue: number, awayValue: number, label: string, isPercentage = false) => {
    // Determine which value is higher for highlighting
    const homeIsHigher = homeValue > awayValue;
    const awayIsHigher = awayValue > homeValue;

    const homeColors = getTeamColors(fixture.teams.home.id);
    const awayColors = getTeamColors(fixture.teams.away.id);

    // Format display value - show decimals for xG
    const formatValue = (value: number) => {
      if (label.toLowerCase().includes('xg') || label.toLowerCase().includes('expected goals')) {
        return value.toFixed(2);
      }
      return isPercentage ? `${value}%` : value.toString();
    };

    return (
      <div key={label} className="mb-4">
        <div className="flex items-center justify-between mx-auto">
          {/* Home Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[40px] text-center"
            style={homeIsHigher ? {
              backgroundColor: homeColors.background,
              color: homeColors.text
            } : {}}
          >
            <span className={homeIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(homeValue)}
            </span>
          </div>

          {/* Label */}
          <div className="text-center text-sm text-muted-foreground flex-1 mx-4">
            {label}
          </div>

          {/* Away Value */}
          <div
            className="px-2 py-1 rounded-full text-sm font-medium min-w-[40px] text-center"
            style={awayIsHigher ? {
              backgroundColor: awayColors.background,
              color: awayColors.text
            } : {}}
          >
            <span className={awayIsHigher ? '' : 'text-foreground dark:text-white'}>
              {formatValue(awayValue)}
            </span>
          </div>
        </div>
      </div>
    );
  };

  // Define categorized stats
  const statCategories = {
    general: [
      { key: 'Ball Possession', label: 'Ball possession', isPercentage: true },
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
    ],
    attack: [
      { key: 'Total Shots', label: 'Total shots', isPercentage: false },
      { key: 'Shots on Goal', label: 'Shots on target', isPercentage: false },
      { key: 'Shots off Goal', label: 'Shots off target', isPercentage: false },
      { key: 'Corner Kicks', label: 'Corner kicks', isPercentage: false },
      { key: 'Shots insidebox', label: 'Shots inside box', isPercentage: false },
      { key: 'Shots outsidebox', label: 'Shots outside box', isPercentage: false },
      { key: 'expected_goals', label: 'Expected goals (xG)', isPercentage: false },
    ],
    defence: [
      { key: 'Offsides', label: 'Offsides', isPercentage: false },
      { key: 'Goalkeeper Saves', label: 'Goalkeeper saves', isPercentage: false },
      { key: 'Blocked Shots', label: 'Blocked shots', isPercentage: false },
      { key: 'Fouls', label: 'Fouls', isPercentage: false },
      { key: 'Yellow Cards', label: 'Yellow cards', isPercentage: false },
      { key: 'Red Cards', label: 'Red cards', isPercentage: false },
    ]
  };

  const renderStatCategory = (categoryName: string, stats: { key: string; label: string; isPercentage: boolean }[]) => {
    const hasStats = stats.some(statConfig => {
      const homeValue = getStatValue(homeStats.statistics, statConfig.key);
      const awayValue = getStatValue(awayStats.statistics, statConfig.key);
      return homeValue > 0 || awayValue > 0;
    });

    if (!hasStats) return null;

    return (
      <div className="mb-8">
        <div className="text-center text-sm font-bold text-foreground dark:text-white tracking-wide mb-6">
          {categoryName}
        </div>
        <div className="space-y-4">
          {stats.map((statConfig) => {
            let homeValue = getStatValue(homeStats.statistics, statConfig.key);
            let awayValue = getStatValue(awayStats.statistics, statConfig.key);

            // Special handling for "Shots off Goal" - calculate from Total Shots - Shots on Goal
            if (statConfig.key === 'Shots off Goal') {
              const homeTotalShots = getStatValue(homeStats.statistics, 'Total Shots');
              const homeOnTarget = getStatValue(homeStats.statistics, 'Shots on Goal');
              const awayTotalShots = getStatValue(awayStats.statistics, 'Total Shots');
              const awayOnTarget = getStatValue(awayStats.statistics, 'Shots on Goal');

              homeValue = homeTotalShots - homeOnTarget;
              awayValue = awayTotalShots - awayOnTarget;
            }

            // Skip if both values are 0 or null
            if (homeValue === 0 && awayValue === 0) return null;

            // Use special Ball possession bar for possession stats, simple row for others
            if (statConfig.label === 'Ball possession') {
              return (
                <div key={statConfig.key}>
                  {renderBallPossessionBar(homeValue, awayValue)}
                </div>
              );
            }

            return (
              <div key={statConfig.key}>
                {renderSimpleStatRow(homeValue, awayValue, statConfig.label, statConfig.isPercentage)}
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
      <div className="space-y-4">
        {renderStatCategory('General Stats', statCategories.general)}
        {renderStatCategory('Attack', statCategories.attack)}
        {renderStatCategory('Defence', statCategories.defence)}
      </div>
    </div>
  );
}
