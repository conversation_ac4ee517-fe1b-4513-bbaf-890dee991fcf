'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { useTheme } from 'next-themes';
import { Fixture, LineupPlayer, MatchEvent } from '@/lib/types';
import { getTeamLogoUrl } from '@/lib/utils';
import {
  GoalIcon,
  YellowCardIcon,
  RedCardIcon,
  AssistIcon
} from '@/components/ui/icons';

interface FootballPitchProps {
  fixture: Fixture;
}

export function FootballPitch({ fixture }: FootballPitchProps) {
  const [isMobile, setIsMobile] = useState(false);
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Helper function to get rating badge colors based on SofaScore style
  const getRatingColors = (rating: number): { bg: string; text: string } => {
    if (rating >= 9.0) return { bg: 'bg-blue-600', text: 'text-black' }; // 9.0-10 - Blue
    if (rating >= 8.0) return { bg: 'bg-cyan-500', text: 'text-black' }; // 8.0-8.9 - Cyan
    if (rating >= 7.0) return { bg: 'bg-green-500', text: 'text-black' }; // 7.0-7.9 - Green
    if (rating >= 6.5) return { bg: 'bg-yellow-500', text: 'text-black' }; // 6.5-6.9 - Yellow
    if (rating >= 6.0) return { bg: 'bg-orange-500', text: 'text-black' }; // 6.0-6.4 - Orange
    return { bg: 'bg-red-500', text: 'text-black' }; // <6.0 - Red
  };

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkIsMobile();
    window.addEventListener('resize', checkIsMobile);

    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Get lineups from fixture
  const homeLineup = fixture.lineups?.find(lineup => lineup.team.id === fixture.teams.home.id);
  const awayLineup = fixture.lineups?.find(lineup => lineup.team.id === fixture.teams.away.id);

  if (!homeLineup || !awayLineup) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>Lineups not available yet</p>
        <p className="text-sm mt-2">Lineups are typically released 1-2 hours before kickoff</p>
      </div>
    );
  }

  // Get player goals from events (excluding penalty shootout goals)
  const getPlayerGoals = (playerId: number, teamId: number): number => {
    if (!fixture.events) return 0;

    return fixture.events.filter(event =>
      event.player?.id === playerId &&
      event.team.id === teamId &&
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own') &&
      // Exclude penalty shootout goals
      !isPenaltyShootoutGoal(event)
    ).length;
  };

  // Helper function to identify penalty shootout goals
  const isPenaltyShootoutGoal = (event: MatchEvent): boolean => {
    // Check if the goal occurred during penalty shootout time
    // Penalty shootouts typically happen after 120 minutes (90 + 30 extra time)
    // or after 90 minutes if no extra time was played
    if (event.time?.elapsed > 120) {
      return true;
    }

    // Check if the detail field indicates it's a penalty shootout goal
    const detail = event.detail?.toLowerCase() || '';
    if (detail.includes('penalty shootout') ||
        detail.includes('shootout') ||
        (detail.includes('penalty') && event.time?.elapsed > 90)) {
      return true;
    }

    return false;
  };

  // Get player cards from events
  const getPlayerCards = (playerId: number, teamId: number): { yellow: number; red: number } => {
    if (!fixture.events) return { yellow: 0, red: 0 };

    const cardEvents = fixture.events.filter(event =>
      event.player?.id === playerId &&
      event.team.id === teamId &&
      event.type === 'Card'
    );

    const yellow = cardEvents.filter(event =>
      event.detail.toLowerCase().includes('yellow')
    ).length;

    const red = cardEvents.filter(event =>
      event.detail.toLowerCase().includes('red')
    ).length;

    return { yellow, red };
  };

  // Get player assists from events (excluding penalty shootout assists)
  const getPlayerAssists = (playerId: number, teamId: number): number => {
    if (!fixture.events) return 0;

    return fixture.events.filter(event =>
      event.assist?.id === playerId &&
      event.team.id === teamId &&
      event.type === 'Goal' &&
      !event.detail.toLowerCase().includes('own') &&
      // Exclude penalty shootout assists
      !isPenaltyShootoutGoal(event)
    ).length;
  };

  const convertGridToPosition = (
    apiGrid: string,
    isHome: boolean,
    lineup: LineupPlayer[],
    isMobile: boolean = false
  ) => {
    const [apiColumn, apiRow] = apiGrid.split(':').map(Number);

    const totalPlayersInLine = lineup.filter(p =>
      p.player.grid?.startsWith(`${apiColumn}:`)
    ).length;

    const goalkeeperColumn = 1;

    const outfieldColumns = [...new Set(
        lineup
            .filter(p => p.player.grid && !p.player.grid.startsWith(`${goalkeeperColumn}:`))
            .map(p => Number(p.player.grid!.split(':')[0]))
    )].sort((a, b) => a - b);

    // --- Vertical layout for mobile ---
    if (isMobile) {
      const baseXPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;
      const xPositionPercentage = isHome ? baseXPositionPercentage : (100 - baseXPositionPercentage);
      
      // Home team is on top, Away is on the bottom.
      if (apiColumn === goalkeeperColumn) {
        return { x: 50, y: isHome ? 5 : 95 };
      }
      
      const topPlayableArea = { start: 15, end: 45 };    // Home team's area
      const bottomPlayableArea = { start: 85, end: 55 }; // Away team's area (reversed for correct order)

      const playableArea = isHome ? topPlayableArea : bottomPlayableArea;
      const areaHeight = playableArea.end - playableArea.start;

      const columnIndex = outfieldColumns.indexOf(apiColumn);
      const numOutfieldColumns = outfieldColumns.length;

      let yPositionPercentage;

      if (numOutfieldColumns <= 1) {
        yPositionPercentage = playableArea.start + areaHeight / 2;
      } else {
        yPositionPercentage = playableArea.start + (columnIndex / (numOutfieldColumns - 1)) * areaHeight;
      }

      return { x: xPositionPercentage, y: yPositionPercentage };

    // --- Horizontal layout for desktop ---
    } else {
      const baseYPositionPercentage = (apiRow - 0.5) / totalPlayersInLine * 100;
      // Flip Y position for away team to match FotMob layout
      const yPositionPercentage = isHome ? baseYPositionPercentage : (100 - baseYPositionPercentage);

      if (apiColumn === goalkeeperColumn) {
        return { x: isHome ? 5 : 95, y: 50 };
      }

      const homePlayableArea = { start: 18, end: 45 };
      const awayPlayableArea = { start: 82, end: 55 };

      const playableArea = isHome ? homePlayableArea : awayPlayableArea;
      const areaWidth = playableArea.end - playableArea.start;

      const columnIndex = outfieldColumns.indexOf(apiColumn);
      const numOutfieldColumns = outfieldColumns.length;

      let xPositionPercentage;

      if (numOutfieldColumns <= 1) {
        xPositionPercentage = playableArea.start + areaWidth / 2;
      } else {
        xPositionPercentage = playableArea.start + (columnIndex / (numOutfieldColumns - 1)) * areaWidth;
      }

      return { x: xPositionPercentage, y: yPositionPercentage };
    }
  };

  // Get player rating from team ratings
  const getPlayerRating = (playerId: number, isHome: boolean): number | null => {
    if (!fixture.teamRatings) return null;

    const teamRatings = isHome ? fixture.teamRatings.home : fixture.teamRatings.away;
    const playerRating = teamRatings.playerRatings.find(pr => pr.playerId === playerId);

    return playerRating ? playerRating.rating : null;
  };

  // Render a player
  const renderPlayer = (player: LineupPlayer['player'], isHome: boolean) => {
    const goals = getPlayerGoals(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const cards = getPlayerCards(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const assists = getPlayerAssists(player.id, isHome ? fixture.teams.home.id : fixture.teams.away.id);
    const playerRating = getPlayerRating(player.id, isHome);

    // Get team colors from lineup data
    const teamLineup = isHome ? homeLineup : awayLineup;
    const teamColors = teamLineup?.team.colors;

    let playerColor: string;
    let textColor: string;
    let borderColor: string;

    if (player.pos === 'G') {
      // Goalkeeper colors
      const gkColors = teamColors?.goalkeeper;
      playerColor = gkColors?.primary ? `#${gkColors.primary}` : (isHome ? '#59db00' : '#ff4444');
      textColor = gkColors?.number ? `#${gkColors.number}` : '#000000';
      borderColor = gkColors?.border ? `#${gkColors.border}` : playerColor;
    } else {
      // Outfield player colors
      const playerColors = teamColors?.player;
      playerColor = playerColors?.primary ? `#${playerColors.primary}` : (isHome ? '#0066cc' : '#cc0000');
      textColor = playerColors?.number ? `#${playerColors.number}` : '#ffffff';
      borderColor = playerColors?.border ? `#${playerColors.border}` : playerColor;
    }

    return (
      <div key={player.id} className="flex flex-col items-center">
        <div className="relative w-12 h-12">
          <svg viewBox="0 0 36 36" className="w-full h-full drop-shadow-lg">
            <path
              fill={playerColor}
              stroke={borderColor}
              strokeWidth="1.5"
              d="M 6 5 L 12 5 C 14 3, 22 3, 24 5 L 30 5 L 34 10 L 30 14 L 30 31 L 6 31 L 6 14 L 2 10 L 6 5 Z"
            />
          </svg>
          <div
            className="absolute inset-0 flex items-center justify-center font-bold text-sm"
            style={{ color: textColor }}
          >
            <span>{player.number || '?'}</span>
          </div>
          {playerRating && (
            <div
              className={`absolute -top-1 px-1.5 text-xs font-semibold ${getRatingColors(playerRating).bg} ${getRatingColors(playerRating).text}`}
              style={{ borderRadius: '12px', right: '-8px' }}
            >
              {playerRating.toFixed(1)}
            </div>
          )}
          {goals > 0 && (
            <div className="absolute -bottom-1 -right-1">
              {Array.from({ length: Math.min(goals, 3) }, (_, index) => {
                const isDark = mounted && theme === 'dark';
                const backgroundColor = isDark ? '#262626' : '#ffffff';
                const borderColor = isDark ? '#525252' : 'transparent';

                return (
                  <div
                    key={index}
                    className="absolute w-[18px] h-[18px] rounded-full flex items-center justify-center"
                    style={{
                      right: `${index * 6}px`,
                      bottom: '0px',
                      zIndex: 10 - index,
                      backgroundColor,
                      border: `1px solid ${borderColor}`
                    }}
                  >
                    <GoalIcon
                      className={`w-[14px] h-[14px] ${isDark ? 'text-white' : 'text-black'}`}
                      width={14}
                      height={14}
                    />
                  </div>
                );
              })}
            </div>
          )}
          {cards.red > 0 ? (
            <div className="absolute -top-1.5 -left-1.5 w-5 h-5">
              <RedCardIcon
                className="w-5 h-5"
                width={18}
                height={18}
              />
            </div>
          ) : cards.yellow > 0 && (
            <div className="absolute -top-1.5 -left-1.5 w-5 h-5">
              <YellowCardIcon
                className="w-5 h-5"
                width={18}
                height={18}
              />
            </div>
          )}
          {assists > 0 && (
            <div className="absolute -bottom-1 -left-1">
              {Array.from({ length: Math.min(assists, 3) }, (_, index) => {
                const isDark = mounted && theme === 'dark';
                const backgroundColor = isDark ? '#262626' : '#ffffff';
                const borderColor = isDark ? '#525252' : 'transparent';

                return (
                  <div
                    key={index}
                    className="absolute w-[18px] h-[18px] rounded-full flex items-center justify-center"
                    style={{
                      left: `${index * 6}px`,
                      bottom: '0px',
                      zIndex: index + 1,
                      backgroundColor,
                      border: `1px solid ${borderColor}`
                    }}
                  >
                    <AssistIcon
                      className={`w-[14px] h-[14px] ${isDark ? 'text-white' : 'text-black'}`}
                      width={14}
                      height={14}
                    />
                  </div>
                );
              })}
            </div>
          )}
        </div>
        <div
          className="mt-1 text-xs text-center text-white font-medium max-w-20 truncate"
          style={{ textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)' }}
        >
          {player.name.split(' ').pop()}
        </div>
      </div>
    );
  };
  
  // Team headers
  const HomeTeamHeader = (
    <div className="flex items-center space-x-3">
      <div className="flex h-8 w-8 items-center justify-center rounded overflow-hidden bg-muted">
        <Image
          src={homeLineup.team.logo || getTeamLogoUrl(homeLineup.team.id)}
          alt={`${homeLineup.team.name} logo`}
          width={32}
          height={32}
          className="h-8 w-8 object-contain"
          onError={(e) => {
            e.currentTarget.style.display = 'none';
            const parent = e.currentTarget.parentElement;
            if (parent) {
              parent.innerHTML = homeLineup.team.name.charAt(0);
              parent.className = 'flex h-8 w-8 items-center justify-center rounded bg-muted text-sm font-bold';
            }
          }}
        />
      </div>
      <div>
        <div className="flex items-center space-x-2">
          <h4 className="font-semibold">{homeLineup.team.name}</h4>
          {fixture.teamRatings?.home && (
            <div
              className={`px-2 py-1 text-xs font-semibold ${getRatingColors(fixture.teamRatings.home.rating).bg} ${getRatingColors(fixture.teamRatings.home.rating).text}`}
              style={{ borderRadius: '12px' }}
            >
              {fixture.teamRatings.home.rating.toFixed(1)}
            </div>
          )}
        </div>
        {homeLineup.formation && <p className="text-sm text-muted-foreground">{homeLineup.formation}</p>}
      </div>
    </div>
  );

  const AwayTeamHeader = (
    <div className="flex items-center space-x-3">
      <div className="text-right">
        <div className="flex items-center justify-end space-x-2">
          {fixture.teamRatings?.away && (
            <div
              className={`px-2 py-1 text-xs font-semibold ${getRatingColors(fixture.teamRatings.away.rating).bg} ${getRatingColors(fixture.teamRatings.away.rating).text}`}
              style={{ borderRadius: '12px' }}
            >
              {fixture.teamRatings.away.rating.toFixed(1)}
            </div>
          )}
          <h4 className="font-semibold">{awayLineup.team.name}</h4>
        </div>
        {awayLineup.formation && <p className="text-sm text-muted-foreground">{awayLineup.formation}</p>}
      </div>
      <div className="flex h-8 w-8 items-center justify-center rounded overflow-hidden bg-muted">
        <Image
          src={awayLineup.team.logo || getTeamLogoUrl(awayLineup.team.id)}
          alt={`${awayLineup.team.name} logo`}
          width={32}
          height={32}
          className="h-8 w-8 object-contain"
          onError={(e) => {
            e.currentTarget.style.display = 'none';
            const parent = e.currentTarget.parentElement;
            if (parent) {
              parent.innerHTML = awayLineup.team.name.charAt(0);
              parent.className = 'flex h-8 w-8 items-center justify-center rounded bg-muted text-sm font-bold';
            }
          }}
        />
      </div>
    </div>
  );

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm">

      {/* --- DESKTOP HEADERS --- */}
      <div className="hidden md:flex md:justify-between md:items-center p-3.5 rounded-t-lg border-b">
        {HomeTeamHeader}
        {AwayTeamHeader}
      </div>

      {/* --- MOBILE HOME HEADER --- */}
      <div className="block md:hidden p-3.5">
        {HomeTeamHeader}
      </div>

      {/* --- FOOTBALL PITCH --- */}
      <div className="w-full">
        <div
          className="relative w-full shadow-lg overflow-hidden"
          style={{
            paddingBottom: isMobile ? '240%' : '64.7%',
            backgroundColor: 'var(--pitch-bg)',
          }}
        >
          {/* Pitch Markings SVG */}
          <svg
            className="absolute inset-0 w-full h-full"
            fill="none"
            stroke="var(--pitch-lines)"
            strokeWidth="2"
          >
            {/* Horizontal layout for desktop */}
            <g className="hidden md:block">
              <svg viewBox="0 0 1050 680" strokeWidth="2">
                <rect x="1" y="1" width="1048" height="678" />
                <line x1="525" y1="1" x2="525" y2="679" />
                <circle cx="525" cy="340" r="91.5" />
                <circle cx="525" cy="340" r="3" fill="rgba(255, 255, 255, 0.8)" />
                <rect x="1" y="138.5" width="165" height="403" />
                <rect x="1" y="248.5" width="55" height="183" />
                <circle cx="110" cy="340" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 166,268 A 91.5,91.5 0 0 1 166,412" />
                <rect x="884" y="138.5" width="165" height="403" />
                <rect x="994" y="248.5" width="55" height="183" />
                <circle cx="940" cy="340" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 884,268 A 91.5,91.5 0 0 0 884,412" />
              </svg>
            </g>

            {/* Vertical layout for mobile */}
            <g className="block md:hidden">
              <svg viewBox="0 0 680 1632" strokeWidth="2">
                <rect x="1" y="1" width="678" height="1630" />
                <line x1="1" y1="816" x2="679" y2="816" />
                <circle cx="340" cy="816" r="91.5" />
                <circle cx="340" cy="816" r="3" fill="rgba(255, 255, 255, 0.5)" />
                {/* Top Side (Home) */}
                <rect x="138.5" y="1" width="403" height="180" />
                <rect x="248.5" y="1" width="183" height="86" />
                <circle cx="340" cy="172" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 268,181 A 91.5,91.5 0 0 0 412,181" />
                {/* Bottom Side (Away) */}
                <rect x="138.5" y="1451" width="403" height="180" />
                <rect x="248.5" y="1545" width="183" height="86" />
                <circle cx="340" cy="1460" r="3" fill="rgba(255, 255, 255, 0.5)" />
                <path d="M 268,1451 A 91.5,91.5 0 0 1 412,1451" />
              </svg>
            </g>
          </svg>

          {/* Players Positioned Using Grid System */}
          <div className="absolute inset-0">
            {homeLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !homeLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, true, homeLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{ left: `${position.x}%`, top: `${position.y}%`, transform: 'translate(-50%, -50%)' }}
                >
                  {renderPlayer(player, true)}
                </div>
              );
            })}

            {awayLineup.startXI?.map((playerData) => {
              const player = playerData.player;
              if (!player.grid || !awayLineup.startXI) return null;
              const position = convertGridToPosition(player.grid, false, awayLineup.startXI, isMobile);
              return (
                <div
                  key={player.id}
                  className="absolute"
                  style={{ left: `${position.x}%`, top: `${position.y}%`, transform: 'translate(-50%, -50%)' }}
                >
                  {renderPlayer(player, false)}
                </div>
              );
            })}
          </div>
        </div>
      </div>
      
      {/* --- MOBILE AWAY HEADER --- */}
      <div className="md:hidden mt-6 flex justify-end p-3.5">
        {AwayTeamHeader}
      </div>

      {/* --- COACH & BENCH SECTIONS --- */}
      <div className="rounded-b-lg">
        {/* --- COACH SECTION --- */}
        <div className="border-t p-3.5">
        <div className="flex justify-between items-center">
          {/* Home Coach */}
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-sm font-bold">
              {homeLineup.coach?.name?.charAt(0) || 'C'}
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Coach</p>
              <p className="font-medium">{homeLineup.coach?.name || 'Unknown'}</p>
            </div>
          </div>

          {/* Away Coach */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm text-muted-foreground">Coach</p>
              <p className="font-medium">{awayLineup.coach?.name || 'Unknown'}</p>
            </div>
            <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center text-sm font-bold">
              {awayLineup.coach?.name?.charAt(0) || 'C'}
            </div>
          </div>
        </div>
      </div>

        {/* --- BENCH SECTION --- */}
        <div className="border-t p-3.5">
          <h3 className="text-center font-semibold mb-4">Bench</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Home Substitutes */}
          <div>
            <h4 className="font-medium mb-3 text-sm text-muted-foreground">{homeLineup.team.name}</h4>
            <div className="space-y-2">
              {homeLineup.substitutes?.map((playerData) => (
                <div key={playerData.player.id} className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs font-bold">
                    {playerData.player.number || '?'}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium text-sm">{playerData.player.name}</p>
                    <p className="text-xs text-muted-foreground">{playerData.player.pos}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Away Substitutes */}
          <div>
            <h4 className="font-medium mb-3 text-sm text-muted-foreground text-right md:text-left">{awayLineup.team.name}</h4>
            <div className="space-y-2">
              {awayLineup.substitutes?.map((playerData) => (
                <div key={playerData.player.id} className="flex items-center space-x-3 md:flex-row-reverse md:space-x-reverse">
                  <div className="w-8 h-8 rounded-full bg-muted flex items-center justify-center text-xs font-bold">
                    {playerData.player.number || '?'}
                  </div>
                  <div className="flex-1 md:text-right">
                    <p className="font-medium text-sm">{playerData.player.name}</p>
                    <p className="text-xs text-muted-foreground">{playerData.player.pos}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
      </div>

    </div>
  );
}
