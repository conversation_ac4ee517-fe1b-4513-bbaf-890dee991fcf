'use client';

import React from 'react';
import { useNextFixtureForTeam } from '@/hooks/useMatches';
import { SmartImage } from '@/components/ui/smart-image';

interface NextFixtureIndicatorProps {
  teamId: number;
}

export const NextFixtureIndicator = ({ teamId }: NextFixtureIndicatorProps) => {
  const { data: nextFixtures, isLoading, error } = useNextFixtureForTeam(teamId);

  if (isLoading) {
    return (
      <div className="w-6 h-6 mx-auto bg-muted animate-pulse rounded-full" />
    );
  }

  if (error || !nextFixtures || nextFixtures.length === 0) {
    return (
      <div className="w-6 h-6 mx-auto bg-muted rounded-full flex items-center justify-center">
        <span className="text-xs text-muted-foreground">?</span>
      </div>
    );
  }

  const nextFixture = nextFixtures[0];
  const opponentTeam = nextFixture.teams.home.id === teamId 
    ? nextFixture.teams.away 
    : nextFixture.teams.home;

  return (
    <div className="w-6 h-6 relative flex-shrink-0 mx-auto" title={`Next: vs ${opponentTeam.name}`}>
      <SmartImage
        src={opponentTeam.logo || '/placeholder-team.png'}
        alt={opponentTeam.name || 'Opponent'}
        width={24}
        height={24}
        className="object-contain w-full h-full"
      />
    </div>
  );
};