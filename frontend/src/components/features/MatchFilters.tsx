'use client';


import { Search, ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DatePicker } from '@/components/ui/date-picker';
import { cn, formatDateSafe } from '@/lib/utils';
import { MATCH_FILTERS } from '@/lib/constants';
import { MatchFilter } from '@/lib/types';

interface MatchFiltersProps {
  activeFilter: MatchFilter;
  onFilterChange: (filter: MatchFilter) => void;
  byTimeEnabled?: boolean;
  onByTimeToggle?: (enabled: boolean) => void;
  selectedDate?: string;
  onDateChange: (date: string) => void;
  searchQuery?: string;
  onSearchChange: (query: string) => void;
  className?: string;
}

export function MatchFilters({
  activeFilter,
  onFilterChange,
  byTimeEnabled = false,
  onByTimeToggle,
  selectedDate,
  onDateChange,
  searchQuery = '',
  onSearchChange,
  className,
}: MatchFiltersProps) {

  const navigateDate = (direction: 'prev' | 'next') => {
    if (!selectedDate) return;

    const currentDate = new Date(selectedDate);
    const newDate = new Date(currentDate);

    if (direction === 'prev') {
      newDate.setDate(currentDate.getDate() - 1);
    } else {
      newDate.setDate(currentDate.getDate() + 1);
    }

    // Use timezone-safe date formatting
    const formattedDate = formatDateSafe(newDate);
    onDateChange(formattedDate);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Search functionality will be handled by parent component
  };

  const handleDatePickerChange = (date: Date | undefined) => {
    if (!date) return;
    // Use timezone-safe date formatting
    const formattedDate = formatDateSafe(date);
    onDateChange(formattedDate);
  };

  // Check if the selected date is today
  const isToday = selectedDate ? new Date(selectedDate).toDateString() === new Date().toDateString() : true;

  return (
    <div className={cn('space-y-4', className)}>
      {/* Filter tabs */}
      <div className="flex items-center gap-2 overflow-x-auto justify-between">
        <div className="flex items-center gap-2">
          {/* Only show Live, Finished, Upcoming filters when viewing today's matches */}
          {isToday && MATCH_FILTERS.filter(filter => filter.key !== 'by-time').map((filter) => (
            <Button
              key={filter.key}
              variant={activeFilter === filter.key ? 'default' : 'outline'}
              size="sm"
              onClick={() => {
                // Toggle functionality: if clicking on active filter, go to 'all', otherwise set the filter
                if (activeFilter === filter.key) {
                  onFilterChange('all');
                } else {
                  onFilterChange(filter.key as MatchFilter);
                }
              }}
              className={cn(
                'whitespace-nowrap h-8 text-xs sm:text-sm',
                activeFilter === filter.key && 'bg-primary text-primary-foreground'
              )}
            >
              {filter.label}
            </Button>
          ))}
          
          {/* By Time toggle button */}
          {onByTimeToggle && (
            <Button
              variant={byTimeEnabled ? 'default' : 'outline'}
              size="sm"
              onClick={() => onByTimeToggle(!byTimeEnabled)}
              className={cn(
                'whitespace-nowrap h-8 text-xs sm:text-sm',
                byTimeEnabled && 'bg-primary text-primary-foreground'
              )}
            >
              By Time
            </Button>
          )}
        </div>
        
        {/* Date selector with navigation - Hidden on mobile */}
        <div className="hidden sm:flex items-center gap-1">
          <Button
              variant="ghost"
              size="icon"
              onClick={() => navigateDate('prev')}
              disabled={!selectedDate}
              className="h-8 w-8 hover:bg-accent hover:text-accent-foreground"
            >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          
          <DatePicker
            date={selectedDate ? new Date(selectedDate) : undefined}
            onDateChange={handleDatePickerChange}
            placeholder="Select Date"
            buttonVariant="outline"
            buttonSize="sm"
            showTodayButton={true}
            className="min-w-[140px]"
          />
          
          <Button
              variant="ghost"
              size="icon"
              onClick={() => navigateDate('next')}
              disabled={!selectedDate}
              className="h-8 w-8 hover:bg-accent hover:text-accent-foreground"
            >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Mobile date navigation - Replaces search on mobile */}
      <div className="flex sm:hidden items-center justify-between gap-2">
        <Button
             variant="ghost"
             size="icon"
             onClick={() => navigateDate('prev')}
             disabled={!selectedDate}
             className="h-8 w-8 hover:bg-accent hover:text-accent-foreground flex-shrink-0"
           >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <DatePicker
          date={selectedDate ? new Date(selectedDate) : undefined}
          onDateChange={handleDatePickerChange}
          placeholder="Select Date"
          buttonVariant="outline"
          buttonSize="sm"
          showTodayButton={true}
          className="flex-1 px-4"
        />
        
        <Button
              variant="ghost"
              size="icon"
              onClick={() => navigateDate('next')}
              disabled={!selectedDate}
              className="h-8 w-8 hover:bg-accent hover:text-accent-foreground flex-shrink-0"
            >
           <ChevronRight className="h-4 w-4" />
         </Button>
      </div>

      {/* Search - Hidden on mobile */}
      <div className="hidden sm:flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <form onSubmit={handleSearchSubmit} className="relative w-full">
          <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search fixtures, teams, leagues..."
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 pr-4"
          />
        </form>
      </div>
    </div>
  );
}
