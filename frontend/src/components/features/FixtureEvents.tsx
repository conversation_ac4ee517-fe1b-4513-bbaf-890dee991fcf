'use client';

import { Badge } from '@/components/ui/badge';
import { Fixture } from '@/lib/types';

interface FixtureEventsProps {
  fixture: Fixture;
}

export function FixtureEvents({ fixture }: FixtureEventsProps) {
  // Mock events data - in a real app, this would come from the fixture data
  const events = [
    {
      time: 15,
      type: 'goal',
      team: 'home',
      player: '<PERSON>',
      description: 'Goal',
    },
    {
      time: 23,
      type: 'yellow_card',
      team: 'away',
      player: '<PERSON>',
      description: 'Yellow Card',
    },
    {
      time: 45,
      type: 'substitution',
      team: 'home',
      player: '<PERSON>',
      description: 'Substitution',
    },
  ];

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'goal':
        return '⚽';
      case 'yellow_card':
        return '🟨';
      case 'red_card':
        return '🟥';
      case 'substitution':
        return '🔄';
      default:
        return '📝';
    }
  };

  const getEventColor = (type: string) => {
    switch (type) {
      case 'goal':
        return 'bg-green-100 text-green-800';
      case 'yellow_card':
        return 'bg-yellow-100 text-yellow-800';
      case 'red_card':
        return 'bg-red-100 text-red-800';
      case 'substitution':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (events.length === 0) {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Match Events</h3>
        <div className="text-center py-8 text-muted-foreground">
          No events recorded yet
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Match Events</h3>
      
      <div className="space-y-3">
        {events.map((event, index) => (
          <div key={index} className="flex items-center gap-4 p-3 bg-muted/50 rounded-lg">
            <Badge variant="outline" className="min-w-[3rem] justify-center">
              {event.time}&apos;
            </Badge>
            
            <div className="text-2xl">
              {getEventIcon(event.type)}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-2">
                <span className="font-medium">{event.player}</span>
                <Badge className={getEventColor(event.type)}>
                  {event.description}
                </Badge>
              </div>
              <div className="text-sm text-muted-foreground">
                {event.team === 'home' ? fixture.teams.home.name : fixture.teams.away.name}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
