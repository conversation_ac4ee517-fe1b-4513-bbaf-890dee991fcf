'use client';

import { Fixture } from '@/lib/types';
import { formatDateTime } from '@/lib/api';

interface FixtureInfoProps {
  fixture: Fixture;
}

export function FixtureInfo({ fixture }: FixtureInfoProps) {
  const matchInfo = [
    {
      label: 'Date & Time',
      value: formatDateTime(fixture.fixture.date),
    },
    {
      label: 'Referee',
      value: fixture.fixture.referee || 'TBD',
    },
    {
      label: 'Venue',
      value: fixture.fixture.venue 
        ? `${fixture.fixture.venue.name}, ${fixture.fixture.venue.city}`
        : 'TBD',
    },
    {
      label: 'Round',
      value: fixture.league.round || 'Regular Season',
    },
    {
      label: 'Season',
      value: fixture.league.season?.toString() || 'Current',
    },
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Match Information</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {matchInfo.map((info, index) => (
          <div key={index} className="flex justify-between items-center p-3 bg-muted/50 rounded-lg">
            <span className="text-sm text-muted-foreground">{info.label}</span>
            <span className="text-sm font-medium">{info.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
