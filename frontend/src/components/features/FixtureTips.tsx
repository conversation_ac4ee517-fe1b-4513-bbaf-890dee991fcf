'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Plus, TrendingUp, Users, Target } from 'lucide-react';
import { Fixture, TipWithDetails } from '@/lib/types';
import { TipCard } from './TipCard';
import { AddTipModal } from './AddTipModal';
import { useAuth } from '@/contexts/AuthContext';
import { useTipsManager, useTipsSocket } from '@/hooks/useTips';

interface FixtureTipsProps {
  fixture: Fixture;
}

export function FixtureTips({ fixture }: FixtureTipsProps) {
  const [showAddTipModal, setShowAddTipModal] = useState(false);
  const { user, isAuthenticated } = useAuth();

  const isUpcoming = fixture.fixture.status.short === 'NS' || fixture.fixture.status.short === 'TBD';
  const isLive = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(fixture.fixture.status.short);
  const isFinished = ['FT', 'AET', 'PEN'].includes(fixture.fixture.status.short);

  // Allow tip creation for upcoming and live matches only
  const canCreateTips = isUpcoming || isLive;

  // Use the tips manager hook
  const {
    tips,
    loading,
    error,
    stats,
    handleTipCreated,
    handleTipDeleted,
    handleTipLiked,
  } = useTipsManager(fixture.fixture.id);

  // Enable real-time updates
  useTipsSocket(fixture.fixture.id);

  // Handle new tip creation with modal close
  const handleTipCreatedWithClose = (newTip: TipWithDetails) => {
    handleTipCreated(newTip);
    setShowAddTipModal(false);
  };

  // Handle tip like action
  const handleTipLikeAction = (tipId: string) => {
    handleTipLiked(tipId, 'like');
  };

  if (loading) {
    return (
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardContent className="p-3.5">
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardContent className="p-3.5">
          <div className="text-center py-8 text-muted-foreground">
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Tips Header with Stats */}
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardHeader className="p-3.5 pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <Target className="h-5 w-5" />
                Community Tips
              </CardTitle>
              {isFinished && (
                <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                  Final Results
                </span>
              )}
            </div>
            {isAuthenticated && canCreateTips && (
              <Button
                onClick={() => setShowAddTipModal(true)}
                size="sm"
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Tip
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="p-3.5 pt-0">
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                <Users className="h-4 w-4" />
                Total Tips
              </div>
              <div className="text-2xl font-bold">{stats.totalTips}</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center gap-1 text-sm text-muted-foreground mb-1">
                <TrendingUp className="h-4 w-4" />
                Active
              </div>
              <div className="text-2xl font-bold text-primary">{stats.activeTips}</div>
            </div>
            <div className="text-center">
              <div className="text-sm text-muted-foreground mb-1">Avg Odds</div>
              <div className="text-2xl font-bold">{stats.avgOdds}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tips List */}
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardContent className="p-3.5">
          {tips.length === 0 ? (
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">
                {isFinished ? 'No Tips Were Made' : 'No Tips Yet'}
              </h3>
              <p className="text-muted-foreground mb-4">
                {isFinished
                  ? 'No community predictions were made for this match.'
                  : 'Be the first to share your prediction for this match!'
                }
              </p>
              {isAuthenticated && canCreateTips && (
                <Button
                  onClick={() => setShowAddTipModal(true)}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add First Tip
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {tips.map((tip, index) => (
                <div key={`tip-${tip._id || index}`}>
                  <TipCard
                    tip={tip}
                    fixture={fixture}
                    onDelete={handleTipDeleted}
                    onLike={handleTipLikeAction}
                    currentUserId={user?._id}
                  />
                  {index < tips.length - 1 && <Separator key={`separator-${index}`} className="my-4" />}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Tip Modal */}
      {showAddTipModal && (
        <AddTipModal
          fixture={fixture}
          onClose={() => setShowAddTipModal(false)}
          onTipCreated={handleTipCreatedWithClose}
        />
      )}
    </div>
  );
}
