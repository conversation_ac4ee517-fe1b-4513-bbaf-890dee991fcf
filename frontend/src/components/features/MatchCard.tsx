'use client';

import Image from 'next/image';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Fixture, isLiveMatch, isFinishedMatch } from '@/lib/types';
import { formatTime } from '@/lib/api';

interface MatchCardProps {
  fixture: Fixture;
  onClick?: () => void;
  className?: string;
  isLast?: boolean;
}

export function MatchCard({ fixture, onClick, className, isLast = false }: MatchCardProps) {
  const isLive = isLiveMatch(fixture.fixture.status.short);
  const isFinished = isFinishedMatch(fixture.fixture.status.short);

  const getStatusColor = () => {
    if (isLive) return 'text-status-live';
    if (isFinished) return 'text-muted-foreground';
    return 'text-muted-foreground';
  };

  const getMatchStatusDisplay = () => {
    const status = fixture.fixture.status.short;
    const elapsed = fixture.fixture.status.elapsed;
    const extra = fixture.fixture.status.extra;

    // <PERSON>le not started matches
    if (status === 'NS') return formatTime(fixture.fixture.date);

    // Handle finished/break statuses - only show status when actually finished/at break
    // These should only show when the period has actually ended
    if (status === 'HT') return 'HT';
    if (status === 'FT') return 'FT';
    if (status === 'AET') return 'AET';
    if (status === 'PEN') return 'PEN';
    if (status === 'BT') return 'Break';
    if (status === 'ET') return 'ET';
    if (status === 'P') return 'Penalties';

    // Handle live matches with elapsed time
    if ((status === '1H' || status === '2H') && elapsed) {
      // If there's extra/stoppage time being played, show it as "45+'" or "90+'"
      if (extra && extra > 0) {
        return (
          <span className="font-semibold">
            {elapsed}+
            <span className="animate-pulse">&apos;</span>
          </span>
        );
      }
      // Regular time, show normal elapsed time with blinking apostrophe
      return (
        <span className="font-semibold">
          {elapsed}
          <span className="animate-pulse">&apos;</span>
        </span>
      );
    }

    // For other statuses, show the status code
    return status;
  };

  return (
    <div 
      className={cn(
        'flex items-center justify-between px-3.5 py-1.5 cursor-pointer transition-colors hover:bg-muted/50',
        !isLast && 'border-b',
        className
      )}
      style={{
        borderBottomColor: !isLast ? 'var(--match-card-border)' : 'transparent'
      }}
      onClick={onClick}
    >
        {/* Status with star icon */}
        <div className="flex flex-col justify-between mr-4 w-8 h-14">
          <div className="flex justify-center items-center h-6">
            <Star className="h-3.5 w-3.5" style={{color: 'var(--ui-icon-muted)'}} />
          </div>
          <div className={cn('text-center flex justify-center items-center h-6', getStatusColor())} style={{fontSize: '12px'}}>
            {getMatchStatusDisplay()}
          </div>
        </div>

        {/* Teams and score */}
        <div className="flex-1 space-y-2">
          {/* Home team */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Home team logo */}
              {fixture.teams.home.logo ? (
                <Image
                  src={fixture.teams.home.logo}
                  alt={`${fixture.teams.home.name} logo`}
                  width={22}
                  height={22}
                  className="rounded-full w-[22px] h-[22px]"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={cn(
                "rounded-full bg-muted flex items-center justify-center text-xs w-[22px] h-[22px]",
                fixture.teams.home.logo ? "hidden" : ""
              )}>
                {fixture.teams.home.name.charAt(0)}
              </div>
              <span className="font-medium text-sm sm:text-sm text-xs">
                {fixture.teams.home.name}
              </span>
            </div>
            <span className={cn('font-bold text-right text-sm sm:text-base', isLive ? 'text-status-live' : '')} style={{width: '18px', height: '17px', display: 'inline-block'}}>
              {fixture.fixture.status.short !== 'NS' ? (fixture.goals.home ?? '-') : ''}
            </span>
          </div>

          {/* Away team */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {/* Away team logo */}
              {fixture.teams.away.logo ? (
                <Image
                  src={fixture.teams.away.logo}
                  alt={`${fixture.teams.away.name} logo`}
                  width={22}
                  height={22}
                  className="rounded-full w-[22px] h-[22px]"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={cn(
                "rounded-full bg-muted flex items-center justify-center text-xs w-[22px] h-[22px]",
                fixture.teams.away.logo ? "hidden" : ""
              )}>
                {fixture.teams.away.name.charAt(0)}
              </div>
              <span className="font-medium text-sm sm:text-sm text-xs">
                {fixture.teams.away.name}
              </span>
            </div>
            <span className={cn('font-bold text-right text-sm sm:text-base', isLive ? 'text-status-live' : '')} style={{width: '18px', height: '17px', display: 'inline-block'}}>
              {fixture.fixture.status.short !== 'NS' ? (fixture.goals.away ?? '-') : ''}
            </span>
          </div>
        </div>


    </div>
  );
}
