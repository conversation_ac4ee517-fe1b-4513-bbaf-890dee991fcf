'use client';

import { useState } from 'react';
import { useHeadToHead } from '@/hooks/useMatches';
import { Fixture } from '@/lib/types';
import { SmartImage } from '@/components/ui/smart-image';
import { format } from 'date-fns';

interface FixtureHeadToHeadProps {
  fixture: Fixture;
}

export function FixtureHeadToHead({ fixture }: FixtureHeadToHeadProps) {
  const { data: h2hMatches, isLoading } = useHeadToHead(
    fixture.teams.home.id,
    fixture.teams.away.id,
    15
  );
  const [homeFilter, setHomeFilter] = useState(false);
  const [tournamentFilter, setTournamentFilter] = useState(false);
  const [showAll, setShowAll] = useState(false);

  // Helper function to get bar color based on percentage
  const getBarColor = (percentage: number) => {
    if (percentage >= 70) {
      return 'var(--performance-excellent)'; // Green for high percentages
    } else if (percentage <= 50) {
      return 'var(--performance-average)'; // Orange for medium percentages
    } else if (percentage < 30) {
      return 'var(--performance-poor)'; // Red for low percentages
    } else {
      return 'var(--performance-average)'; // Default to orange for 51-69%
    }
  };

  // Simple color scheme for home/away stats
  const getTeamColors = () => {
    return {
      home: { background: 'var(--team-home-primary)', text: 'var(--ui-text-white)' },
      away: { background: 'var(--team-away-primary)', text: 'var(--ui-text-white)' }
    };
  };



  if (isLoading) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!h2hMatches || h2hMatches.length === 0) {
    return (
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
        <h3 className="text-lg font-semibold mb-4">Head-to-Head</h3>
        <p className="text-muted-foreground">No head-to-head data available.</p>
      </div>
    );
  }

  // Separate finished and upcoming matches
  const finishedMatches = h2hMatches
    .filter(match => match.goals.home !== null && match.goals.away !== null)
    .sort((a, b) => new Date(b.fixture.date).getTime() - new Date(a.fixture.date).getTime());

  const upcomingMatches = h2hMatches
    .filter(match => match.goals.home === null && match.goals.away === null)
    .sort((a, b) => new Date(a.fixture.date).getTime() - new Date(b.fixture.date).getTime());

  // Apply filters to all matches (finished + upcoming)
  const getFilteredMatches = () => {
    let filteredFinished = finishedMatches;
    let filteredUpcoming = upcomingMatches;

    // Apply home filter
    if (homeFilter) {
      filteredFinished = filteredFinished.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
      filteredUpcoming = filteredUpcoming.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
    }

    // Apply tournament filter
    if (tournamentFilter) {
      filteredFinished = filteredFinished.filter(match =>
        match.league?.name === fixture.league.name
      );
      filteredUpcoming = filteredUpcoming.filter(match =>
        match.league?.name === fixture.league.name
      );
    }

    // Combine upcoming matches first, then finished matches
    return [...filteredUpcoming, ...filteredFinished];
  };

  // Get filtered matches for statistics (only finished matches)
  const getFilteredFinishedMatches = () => {
    let filtered = finishedMatches;

    if (homeFilter) {
      filtered = filtered.filter(match =>
        match.teams.home.id === fixture.teams.home.id
      );
    }

    if (tournamentFilter) {
      filtered = filtered.filter(match =>
        match.league?.name === fixture.league.name
      );
    }

    return filtered;
  };

  const filteredMatches = getFilteredMatches();
  const filteredFinishedMatches = getFilteredFinishedMatches();
  const displayedMatches = showAll ? filteredMatches : filteredMatches.slice(0, 5);

  // Calculate statistics based on filtered finished matches only
  const calculateStats = (matches: typeof h2hMatches) => {
    let homeWins = 0;
    let awayWins = 0;
    let draws = 0;
    let homeGoalsTotal = 0;
    let awayGoalsTotal = 0;
    let over15 = 0;
    let over25 = 0;
    let over35 = 0;
    let btts = 0;

    matches.forEach((match) => {
      const homeGoals = match.goals.home;
      const awayGoals = match.goals.away;

      if (homeGoals === null || awayGoals === null) return;

      // Determine which team is home in this historical match
      const isHomeTeamHome = match.teams.home.id === fixture.teams.home.id;

      if (isHomeTeamHome) {
        homeGoalsTotal += homeGoals;
        awayGoalsTotal += awayGoals;
      } else {
        homeGoalsTotal += awayGoals;
        awayGoalsTotal += homeGoals;
      }

      const totalGoals = homeGoals + awayGoals;

      if (homeGoals > awayGoals) {
        if (isHomeTeamHome) {
          homeWins++;
        } else {
          awayWins++;
        }
      } else if (awayGoals > homeGoals) {
        if (isHomeTeamHome) {
          awayWins++;
        } else {
          homeWins++;
        }
      } else {
        draws++;
      }

      // Calculate over/under stats
      if (totalGoals > 1.5) over15++;
      if (totalGoals > 2.5) over25++;
      if (totalGoals > 3.5) over35++;

      // Both teams to score
      if (homeGoals > 0 && awayGoals > 0) btts++;
    });

    return {
      totalMatches: matches.length,
      homeWins,
      awayWins,
      draws,
      homeGoalsTotal,
      awayGoalsTotal,
      over15,
      over25,
      over35,
      btts
    };
  };

  const stats = calculateStats(filteredFinishedMatches);

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5">
      {/* Header with team logos and simple win stats */}
      <div className="flex items-center justify-center mb-6 px-4 md:px-8">
        <div className="flex items-center justify-between w-full max-w-2xl">
          {/* Home team section */}
          <div className="flex items-center justify-center">
            <SmartImage
              src={fixture.teams.home.logo}
              alt={fixture.teams.home.name}
              width={40}
              height={40}
              className="w-10 h-10 md:w-14 md:h-14 object-contain"
            />
          </div>

          {/* Win/Draw/Loss Stats */}
          <div className="flex items-center gap-4 md:gap-6">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-foreground">
                {stats.homeWins}
              </div>
              <div className="text-xs md:text-sm text-muted-foreground mt-1">Wins</div>
            </div>

            <div className="text-2xl md:text-3xl text-muted-foreground font-light">|</div>

            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-foreground">
                {stats.draws}
              </div>
              <div className="text-xs md:text-sm text-muted-foreground mt-1">Draws</div>
            </div>

            <div className="text-2xl md:text-3xl text-muted-foreground font-light">|</div>

            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-foreground">
                {stats.awayWins}
              </div>
              <div className="text-xs md:text-sm text-muted-foreground mt-1">Wins</div>
            </div>
          </div>

          {/* Away team section */}
          <div className="flex items-center justify-center">
            <SmartImage
              src={fixture.teams.away.logo}
              alt={fixture.teams.away.name}
              width={40}
              height={40}
              className="w-10 h-10 md:w-14 md:h-14 object-contain"
            />
          </div>
        </div>
      </div>

      {/* Summary text */}
      <div className="text-sm text-muted-foreground mb-6 text-center">
        {fixture.teams.home.name} vs {fixture.teams.away.name}&apos;s head to head record shows that in the previous {stats.totalMatches} meetings, {fixture.teams.home.name} has won {stats.homeWins} times, {fixture.teams.away.name} has won {stats.awayWins} times, and {stats.draws} ended in a draw. {fixture.teams.home.name} scored {stats.homeGoalsTotal} goals and {fixture.teams.away.name} scored {stats.awayGoalsTotal} goals in these matches.
      </div>

      {/* Statistics cards */}
      <div className="grid grid-cols-3 gap-3 mb-6">
        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over15 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 1.5</div>
          <div className="text-xs text-muted-foreground">{stats.over15} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over15 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over15 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over25 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 2.5</div>
          <div className="text-xs text-muted-foreground">{stats.over25} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over25 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over25 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.over35 / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">Over 3.5</div>
          <div className="text-xs text-muted-foreground">{stats.over35} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.over35 / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.over35 / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">{stats.totalMatches > 0 ? Math.round((stats.btts / stats.totalMatches) * 100) : 0}%</div>
          <div className="text-xs text-muted-foreground">BTTS</div>
          <div className="text-xs text-muted-foreground">{stats.btts} / {stats.totalMatches} matches</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: `${stats.totalMatches > 0 ? (stats.btts / stats.totalMatches) * 100 : 0}%`,
                backgroundColor: getBarColor(stats.totalMatches > 0 ? (stats.btts / stats.totalMatches) * 100 : 0)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">26%</div>
          <div className="text-xs text-muted-foreground">Clean Sheets</div>
          <div className="text-xs text-muted-foreground">{fixture.teams.home.name}</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: '26%',
                backgroundColor: getBarColor(26)
              }}
            />
          </div>
        </div>

        <div className="bg-muted/30 rounded-lg p-3 text-center">
          <div className="text-lg font-bold">26%</div>
          <div className="text-xs text-muted-foreground">Clean Sheets</div>
          <div className="text-xs text-muted-foreground">{fixture.teams.away.name}</div>
          <div className="w-full h-1 bg-gray-200 rounded mt-2">
            <div
              className="h-full rounded transition-all duration-300"
              style={{
                width: '26%',
                backgroundColor: getBarColor(26)
              }}
            />
          </div>
        </div>
      </div>

      {/* Filter Buttons */}
      <div className="flex gap-2 mb-4">
        <button
          onClick={() => setHomeFilter(!homeFilter)}
          className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
            homeFilter
              ? 'bg-primary text-primary-foreground'
              : 'bg-muted hover:bg-muted/80'
          }`}
        >
          Home
        </button>
        <button
          onClick={() => setTournamentFilter(!tournamentFilter)}
          className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
            tournamentFilter
              ? 'bg-primary text-primary-foreground'
              : 'bg-muted hover:bg-muted/80'
          }`}
        >
          This tournament
        </button>
      </div>

      {/* All Matches */}
      <div className="space-y-0">
        {displayedMatches.map((match, index: number) => {
          return (
            <div
              key={match.fixture?.id || index}
              className="border-b border-custom-gray last:border-b-0 py-4 cursor-pointer hover:bg-muted/30 transition-colors"
            >
              {/* First row: Date and League */}
              <div className="flex items-center justify-between mb-2">
                <div className="text-xs text-muted-foreground">
                  {match.fixture?.date ? format(new Date(match.fixture.date), 'd MMM yyyy') : 'Unknown Date'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {match.league?.name || 'Unknown League'}
                </div>
              </div>

              {/* Second row: Teams and Score */}
              <div className="flex items-center justify-center">
                <div className="flex items-center justify-between w-full max-w-md">
                  <div className="flex items-center space-x-2 flex-1 justify-end">
                    <span className="font-medium text-sm">
                      {match.teams?.home?.name || 'Home Team'}
                    </span>
                    {match.teams?.home?.logo && (
                      <SmartImage
                        src={match.teams.home.logo}
                        alt={match.teams?.home?.name || 'Home Team'}
                        width={16}
                        height={16}
                        className="w-4 h-4 object-contain"
                      />
                    )}
                  </div>

                  <div className="text-sm font-bold mx-4 min-w-[50px] text-center">
                    {match.goals?.home !== null && match.goals?.away !== null
                      ? `${match.goals.home} - ${match.goals.away}`
                      : match.fixture?.date ? format(new Date(match.fixture.date), 'HH:mm') : 'TBD'
                    }
                  </div>

                  <div className="flex items-center space-x-2 flex-1 justify-start">
                    {match.teams?.away?.logo && (
                      <SmartImage
                        src={match.teams.away.logo}
                        alt={match.teams?.away?.name || 'Away Team'}
                        width={16}
                        height={16}
                        className="w-4 h-4 object-contain"
                      />
                    )}
                    <span className="font-medium text-sm">
                      {match.teams?.away?.name || 'Away Team'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Show More Button */}
      {filteredMatches.length > 5 && (
        <div className="flex justify-center mt-4">
          <button
            onClick={() => setShowAll(!showAll)}
            className="px-4 py-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors"
          >
            {showAll ? 'Show Less' : 'Show All Matches'}
          </button>
        </div>
      )}
    </div>
  );
}
