'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Fixture, Prediction } from '@/lib/types';

interface RadarChartProps {
  fixture: Fixture;
  predictions: Prediction;
}

interface RadarDataPoint {
  label: string;
  homeValue: number;
  awayValue: number;
}

export function RadarChart({ fixture, predictions }: RadarChartProps) {
  // State for toggling team visibility
  const [showHome, setShowHome] = useState(true);
  const [showAway, setShowAway] = useState(true);

  // Map comparison data to radar chart attributes
  const radarData: RadarDataPoint[] = [
    {
      label: 'Rating',
      homeValue: parseFloat(predictions.comparison.total.home) || 0,
      awayValue: parseFloat(predictions.comparison.total.away) || 0,
    },
    {
      label: 'Attack',
      homeValue: parseFloat(predictions.comparison.att.home) || 0,
      awayValue: parseFloat(predictions.comparison.att.away) || 0,
    },
    {
      label: 'Squad',
      homeValue: parseFloat(predictions.comparison.form.home) || 0,
      awayValue: parseFloat(predictions.comparison.form.away) || 0,
    },
    {
      label: 'Goalkeepers',
      homeValue: parseFloat(predictions.comparison.goals.home) || 0,
      awayValue: parseFloat(predictions.comparison.goals.away) || 0,
    },
    {
      label: 'Defense',
      homeValue: parseFloat(predictions.comparison.def.home) || 0,
      awayValue: parseFloat(predictions.comparison.def.away) || 0,
    },
    {
      label: 'Midfield',
      homeValue: parseFloat(predictions.comparison.h2h.home) || 0,
      awayValue: parseFloat(predictions.comparison.h2h.away) || 0,
    },
  ];

  // Chart dimensions - responsive
  const getChartSize = () => {
    if (typeof window !== 'undefined') {
      const width = window.innerWidth;
      if (width < 768) return 280; // Mobile
      if (width < 1024) return 320; // Tablet
      return 350; // Desktop
    }
    return 350; // Default for SSR
  };

  const [size, setSize] = useState(getChartSize);
  const center = size / 2;
  const radius = size * 0.34; // Maintain proportion

  // Handle window resize for responsiveness
  useEffect(() => {
    const handleResize = () => {
      setSize(getChartSize());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  const levels = 5;

  // Calculate points for hexagon
  const getPoint = (index: number, value: number, maxValue: number = 100) => {
    const angle = (Math.PI * 2 * index) / 6 - Math.PI / 2; // Start from top
    const r = (value / maxValue) * radius;
    return {
      x: center + Math.cos(angle) * r,
      y: center + Math.sin(angle) * r,
    };
  };

  // Calculate label positions (outside the chart)
  const getLabelPoint = (index: number) => {
    const angle = (Math.PI * 2 * index) / 6 - Math.PI / 2;
    const r = radius + 30;
    return {
      x: center + Math.cos(angle) * r,
      y: center + Math.sin(angle) * r,
    };
  };

  // Generate grid lines (hexagon levels)
  const generateGridLines = () => {
    const lines = [];
    for (let level = 1; level <= levels; level++) {
      const points = [];
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI * 2 * i) / 6 - Math.PI / 2;
        const r = (level / levels) * radius;
        points.push(`${center + Math.cos(angle) * r},${center + Math.sin(angle) * r}`);
      }
      lines.push(
        <polygon
          key={level}
          points={points.join(' ')}
          fill="none"
          stroke="#cccccc"
          strokeWidth="1"
          opacity={0.8}
        />
      );
    }
    return lines;
  };

  // Generate axis lines
  const generateAxisLines = () => {
    const lines = [];
    for (let i = 0; i < 6; i++) {
      const angle = (Math.PI * 2 * i) / 6 - Math.PI / 2;
      const endX = center + Math.cos(angle) * radius;
      const endY = center + Math.sin(angle) * radius;
      lines.push(
        <line
          key={i}
          x1={center}
          y1={center}
          x2={endX}
          y2={endY}
          stroke="#cccccc"
          strokeWidth="1"
          opacity={0.8}
        />
      );
    }
    return lines;
  };

  // Generate data polygon for a team
  const generateDataPolygon = (data: RadarDataPoint[], isHome: boolean) => {
    const points = data.map((item, index) => {
      const value = isHome ? item.homeValue : item.awayValue;
      const point = getPoint(index, value);
      return `${point.x},${point.y}`;
    });

    const color = isHome ? 'var(--team-home-primary)' : 'var(--team-away-primary)'; // Blue for home, red for away
    const fillColor = isHome ? 'rgba(51, 153, 219, 0.2)' : 'rgba(232, 76, 61, 0.2)';

    return (
      <polygon
        points={points.join(' ')}
        fill={fillColor}
        stroke={color}
        strokeWidth="2"
        opacity={0.8}
      />
    );
  };

  // Generate data points
  const generateDataPoints = (data: RadarDataPoint[], isHome: boolean) => {
    const color = isHome ? 'var(--team-home-primary)' : 'var(--team-away-primary)';

    return data.map((item, index) => {
      const value = isHome ? item.homeValue : item.awayValue;
      const point = getPoint(index, value);
      return (
        <circle
          key={`${isHome ? 'home' : 'away'}-${index}`}
          cx={point.x}
          cy={point.y}
          r="4"
          fill={color}
          stroke="white"
          strokeWidth="2"
        />
      );
    });
  };

  return (
    <Card>
      <CardHeader>
        <h2 className="text-lg font-semibold">Attributes</h2>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Radar Chart */}
          <div className="flex justify-center">
            <div className="relative">
              <svg width={size} height={size} className="overflow-visible">
                {/* Grid lines */}
                {generateGridLines()}
                {/* Axis lines */}
                {generateAxisLines()}
                {/* Away team data (behind) */}
                {showAway && generateDataPolygon(radarData, false)}
                {/* Home team data (front) */}
                {showHome && generateDataPolygon(radarData, true)}
                {/* Data points */}
                {showAway && generateDataPoints(radarData, false)}
                {showHome && generateDataPoints(radarData, true)}
                
                {/* Labels */}
                {radarData.map((item, index) => {
                  const labelPoint = getLabelPoint(index);
                  const homeValue = Math.round(item.homeValue);
                  const awayValue = Math.round(item.awayValue);
                  
                  return (
                    <g key={index}>
                      {/* Attribute label */}
                      <text
                        x={labelPoint.x}
                        y={labelPoint.y - 15}
                        textAnchor="middle"
                        className="text-sm font-medium fill-foreground"
                      >
                        {item.label}
                      </text>
                      {/* Home team value */}
                      <text
                        x={labelPoint.x - 15}
                        y={labelPoint.y + 5}
                        textAnchor="middle"
                        className="text-sm font-bold"
                        fill="#3499db"
                      >
                        {homeValue}
                      </text>
                      {/* Away team value */}
                      <text
                        x={labelPoint.x + 15}
                        y={labelPoint.y + 5}
                        textAnchor="middle"
                        className="text-sm font-bold"
                        fill="#e84c3d"
                      >
                        {awayValue}
                      </text>
                    </g>
                  );
                })}
              </svg>
            </div>
          </div>

          {/* Legend */}
          <div className="flex items-center justify-center gap-8">
            <div
              className={`flex items-center gap-2 cursor-pointer transition-opacity ${!showHome ? 'opacity-50' : ''}`}
              onClick={() => setShowHome(!showHome)}
            >
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#3499db' }}></div>
              <span className="text-sm font-medium">{fixture.teams.home.name}</span>
            </div>
            <div
              className={`flex items-center gap-2 cursor-pointer transition-opacity ${!showAway ? 'opacity-50' : ''}`}
              onClick={() => setShowAway(!showAway)}
            >
              <div className="w-4 h-4 rounded-full" style={{ backgroundColor: '#e84c3d' }}></div>
              <span className="text-sm font-medium">{fixture.teams.away.name}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
