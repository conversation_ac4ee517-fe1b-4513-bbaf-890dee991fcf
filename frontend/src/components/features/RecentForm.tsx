'use client';

import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Fixture } from '@/lib/types';
import { useTeamRecentFixtures } from '@/hooks/useMatches';
import { SmartImage } from '@/components/ui/smart-image';
import { Loader2 } from 'lucide-react';
import { generateMatchUrl } from '@/lib/utils';
import { useRouter } from 'next/navigation';

// Cup competition league IDs
const CUP_COMPETITION_IDS = [
  15,   // FIFA Club World Cup
  2,    // UEFA Champions League
  3,    // UEFA Europa League
  848,  // UEFA Europa Conference League
  1,    // World Cup
  8,    // World Cup
  4,    // European Championship (UEFA Euro)
  9,    // Copa America (CONMEBOL)
  6,    // Africa Cup of Nations
  7,    // AFC Asian Cup
  17,   // AFC Champions League Elite
  1132, // AFC Champions League Two
  13,   // Copa Libertadores (CONMEBOL)
  11,   // Copa Sudamericana (CONMEBOL)
  12,   // CAF Champions League
  22,   // CONCACAF Gold Cup
  856,  // CONCACAF Champions Cup
  45,   // FA Cup
  48,   // League Cup (Carabao Cup)
  66,   // Coupe de France
  81,   // DFB Pokal
  137,  // Coppa Italia
  143,  // Copa Del Rey
  96,   // Taça De Portugal
];

// Helper function to check if a league is a cup competition
const isCupCompetition = (leagueId: number): boolean => {
  return CUP_COMPETITION_IDS.includes(leagueId);
};

interface RecentFormProps {
  fixture: Fixture;
}

interface FormResult {
  result: 'W' | 'D' | 'L';
  opponent: string;
  opponentLogo?: string;
  score: string;
  isHome: boolean;
  teamName: string;
  teamLogo?: string;
  displayText: string;
  fixtureId: number;
}

export function RecentForm({ fixture }: RecentFormProps) {
  const leagueId = fixture.league.id;
  const isCurrentFixtureCup = isCupCompetition(leagueId);
  
  // For cup competitions, always show all competitions (no league filter)
  const [showAllCompetitions, setShowAllCompetitions] = useState(isCurrentFixtureCup);
  const router = useRouter();

  const handleFixtureClick = (formResult: FormResult) => {
    const homeTeam = formResult.isHome ? formResult.teamName : formResult.opponent;
    const awayTeam = formResult.isHome ? formResult.opponent : formResult.teamName;
    const url = generateMatchUrl(homeTeam, awayTeam, formResult.fixtureId);
    router.push(url);
  };

  const homeTeamId = fixture.teams.home.id;
  const awayTeamId = fixture.teams.away.id;
  const leagueName = fixture.league.name;

  // Fetch recent fixtures for league-specific view (disabled for cup competitions)
  const { data: homeLeagueFixtures, isLoading: homeLeagueLoading } = useTeamRecentFixtures(
    homeTeamId,
    10, // Get more to filter by league
    { enabled: !showAllCompetitions && !isCurrentFixtureCup }
  );
  const { data: awayLeagueFixtures, isLoading: awayLeagueLoading } = useTeamRecentFixtures(
    awayTeamId,
    10, // Get more to filter by league
    { enabled: !showAllCompetitions && !isCurrentFixtureCup }
  );

  // Fetch recent fixtures across all competitions (toggle view)
  const { data: homeRecentFixtures, isLoading: homeRecentLoading } = useTeamRecentFixtures(
    homeTeamId,
    5,
    { enabled: showAllCompetitions }
  );
  const { data: awayRecentFixtures, isLoading: awayRecentLoading } = useTeamRecentFixtures(
    awayTeamId,
    5,
    { enabled: showAllCompetitions }
  );

  const isLoading = showAllCompetitions
    ? (homeRecentLoading || awayRecentLoading)
    : (homeLeagueLoading || awayLeagueLoading);



  // Parse form from recent fixtures
  const parseFormFromFixtures = (fixtures: Fixture[], teamId: number): FormResult[] => {
    if (!fixtures) return [];

    return fixtures.slice(0, 5).map(fixture => {
      const isHome = fixture.teams.home.id === teamId;
      const opponent = isHome ? fixture.teams.away : fixture.teams.home;
      const team = isHome ? fixture.teams.home : fixture.teams.away;
      const homeGoals = fixture.goals.home || 0;
      const awayGoals = fixture.goals.away || 0;

      let result: 'W' | 'D' | 'L';
      if (homeGoals === awayGoals) {
        result = 'D';
      } else if ((isHome && homeGoals > awayGoals) || (!isHome && awayGoals > homeGoals)) {
        result = 'W';
      } else {
        result = 'L';
      }

      // Format score based on team perspective
      const teamScore = isHome ? homeGoals : awayGoals;
      const opponentScore = isHome ? awayGoals : homeGoals;

      // Create display text based on home/away (without score)
      const teamName = team.name;
      const opponentName = opponent.name;

      return {
        result,
        opponent: opponent.name,
        opponentLogo: opponent.logo,
        score: `${teamScore} - ${opponentScore}`,
        isHome,
        teamName: team.name,
        teamLogo: team.logo,
        displayText: `${teamName} vs ${opponentName}`,
        fixtureId: fixture.fixture.id
      };
    });
  };

  // Filter league fixtures for league-specific view
  const homeLeagueFilteredFixtures = (homeLeagueFixtures || []).filter(
    (fixture: Fixture) => fixture.league.id === leagueId
  );
  const awayLeagueFilteredFixtures = (awayLeagueFixtures || []).filter(
    (fixture: Fixture) => fixture.league.id === leagueId
  );

  // Get form data based on current view
  const homeForm = showAllCompetitions
    ? parseFormFromFixtures(homeRecentFixtures || [], homeTeamId)
    : parseFormFromFixtures(homeLeagueFilteredFixtures, homeTeamId);

  const awayForm = showAllCompetitions
    ? parseFormFromFixtures(awayRecentFixtures || [], awayTeamId)
    : parseFormFromFixtures(awayLeagueFilteredFixtures, awayTeamId);

  const getResultColor = (result: 'W' | 'D' | 'L') => {
    switch (result) {
      case 'W':
        return 'text-white' + ' ' + 'bg-[var(--form-win)]';
      case 'D':
        return 'text-white' + ' ' + 'bg-[var(--form-draw)]';
      case 'L':
        return 'text-white' + ' ' + 'bg-[var(--form-loss)]';
      default:
        return 'bg-gray-500 text-white';
    }
  };



  return (
    <Card>
      <CardHeader>
        <div className="space-y-3">
          <CardTitle>Recent Form</CardTitle>
          {!isCurrentFixtureCup && (
            <div className="flex gap-2">
              <button
                 className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                   !showAllCompetitions
                     ? 'bg-primary text-primary-foreground'
                     : 'bg-transparent hover:bg-muted/50'
                 }`}
                 onClick={() => !isCurrentFixtureCup && setShowAllCompetitions(false)}
               >
                 {leagueName}
               </button>
               <button
                 className={`px-3 py-1.5 rounded-lg text-sm font-medium cursor-pointer transition-colors ${
                   showAllCompetitions
                     ? 'bg-primary text-primary-foreground'
                     : 'bg-transparent hover:bg-muted/50'
                 }`}
                 onClick={() => !isCurrentFixtureCup && setShowAllCompetitions(true)}
               >
                 ALL
               </button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <div className="space-y-3">
            {/* Display each match as a row */}
            {Array.from({ length: Math.max(homeForm.length, awayForm.length) }, (_, index) => {
              const homeMatch = homeForm[index];
              const awayMatch = awayForm[index];

              return (
                <div key={index} className="grid grid-cols-2 gap-4">
                  {/* Home team match (left side) */}
                  <div 
                    className={`flex items-center justify-between ${homeMatch ? 'cursor-pointer hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors' : ''}`}
                    onClick={homeMatch ? () => handleFixtureClick(homeMatch) : undefined}
                  >
                    {homeMatch ? (
                      <>
                        {/* Left team - Desktop: name, Mobile: logo */}
                        <div className="flex-1 text-right pr-2 flex justify-end items-center">
                          <span className="hidden sm:block text-sm font-medium truncate">
                            {homeMatch.isHome ? homeMatch.teamName : homeMatch.opponent}
                          </span>
                          <div className="block sm:hidden">
                            <SmartImage
                              src={homeMatch.isHome ? homeMatch.teamLogo : homeMatch.opponentLogo}
                              alt={homeMatch.isHome ? homeMatch.teamName : homeMatch.opponent}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                        </div>
                        <Badge
                          className={`${getResultColor(homeMatch.result)} text-xs font-bold px-2 py-1 min-w-[50px] text-center shrink-0`}
                        >
                          {homeMatch.score}
                        </Badge>
                        {/* Right team - Desktop: name, Mobile: logo */}
                        <div className="flex-1 text-left pl-2 flex justify-start items-center">
                          <span className="hidden sm:block text-sm font-medium truncate">
                            {homeMatch.isHome ? homeMatch.opponent : homeMatch.teamName}
                          </span>
                          <div className="block sm:hidden">
                            <SmartImage
                              src={homeMatch.isHome ? homeMatch.opponentLogo : homeMatch.teamLogo}
                              alt={homeMatch.isHome ? homeMatch.opponent : homeMatch.teamName}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="flex-1"></div>
                    )}
                  </div>

                  {/* Away team match (right side) */}
                  <div 
                    className={`flex items-center justify-between ${awayMatch ? 'cursor-pointer hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors' : ''}`}
                    onClick={awayMatch ? () => handleFixtureClick(awayMatch) : undefined}
                  >
                    {awayMatch ? (
                      <>
                        {/* Left team - Desktop: name, Mobile: logo */}
                        <div className="flex-1 text-right pr-2 flex justify-end items-center">
                          <span className="hidden sm:block text-sm font-medium truncate">
                            {awayMatch.isHome ? awayMatch.teamName : awayMatch.opponent}
                          </span>
                          <div className="block sm:hidden">
                            <SmartImage
                              src={awayMatch.isHome ? awayMatch.teamLogo : awayMatch.opponentLogo}
                              alt={awayMatch.isHome ? awayMatch.teamName : awayMatch.opponent}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                        </div>
                        <Badge
                          className={`${getResultColor(awayMatch.result)} text-xs font-bold px-2 py-1 min-w-[50px] text-center shrink-0`}
                        >
                          {awayMatch.score}
                        </Badge>
                        {/* Right team - Desktop: name, Mobile: logo */}
                        <div className="flex-1 text-left pl-2 flex justify-start items-center">
                          <span className="hidden sm:block text-sm font-medium truncate">
                            {awayMatch.isHome ? awayMatch.opponent : awayMatch.teamName}
                          </span>
                          <div className="block sm:hidden">
                            <SmartImage
                              src={awayMatch.isHome ? awayMatch.opponentLogo : awayMatch.teamLogo}
                              alt={awayMatch.isHome ? awayMatch.opponent : awayMatch.teamName}
                              width={20}
                              height={20}
                              className="object-contain"
                            />
                          </div>
                        </div>
                      </>
                    ) : (
                      <div className="flex-1"></div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
