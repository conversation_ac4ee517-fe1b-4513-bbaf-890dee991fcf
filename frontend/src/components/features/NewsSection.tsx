'use client';


import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface NewsItem {
  id: string;
  title: string;
  summary: string;
  image?: string;
  publishedAt: string;
  source: string;
}

interface NewsSectionProps {
  className?: string;
}

// Mock news data (in a real app, this would come from an API)
const mockNews: NewsItem[] = [
  {
    id: '1',
    title: 'Transfer Rumors: Man Utd Plot Isak Hijack',
    summary: 'Man City Offer Real Madrid Haaland Swap',
    image: '/news/transfer-1.jpg',
    publishedAt: '2h ago',
    source: 'Sky Sports',
  },
  {
    id: '2',
    title: 'Dream Transfer: <PERSON> to Chelsea',
    summary: 'Inter Milan defender linked with summer move',
    image: '/news/transfer-2.jpg',
    publishedAt: '1h ago',
    source: 'ESPN',
  },
  {
    id: '3',
    title: '<PERSON>: <PERSON> Makes Admission After Failed Liverpool Bid',
    summary: 'Newcastle manager speaks about striker\'s future',
    image: '/news/isak.jpg',
    publishedAt: '30 min ago',
    source: 'BBC Sport',
  },
  {
    id: '4',
    title: '"I Achieved Everything"—<PERSON>-min Announces Spurs Departure As LAFC Eye South...',
    summary: 'Tottenham star considering MLS move',
    image: '/news/son.jpg',
    publishedAt: '2h ago',
    source: 'The Athletic',
  },
  {
    id: '5',
    title: 'History of Liverpool\'s No.7 Shirt: Florian Wirtz Joins Club Legends',
    summary: 'German midfielder set to inherit iconic number',
    image: '/news/wirtz.jpg',
    publishedAt: '1h ago',
    source: 'Liverpool Echo',
  },
];

export function NewsSection({ className }: NewsSectionProps) {
  return (
    <div className={cn('rounded-lg border bg-card text-card-foreground shadow-sm p-3.5 space-y-4', className)}>
      {/* Header */}
      <h2 className="text-xl font-bold">News</h2>

      {/* News items */}
        {mockNews.map((item) => (
          <div key={item.id} className="rounded-lg border bg-card text-card-foreground cursor-pointer hover:bg-muted/50 transition-colors flex gap-3">
            {/* News image placeholder */}
            <div className="h-16 w-16 rounded-lg bg-muted flex items-center justify-center flex-shrink-0">
              <span className="text-xs">📰</span>
            </div>

            {/* News content */}
            <div className="flex-1 space-y-1">
              <h3 className="font-medium text-sm leading-tight line-clamp-2">
                {item.title}
              </h3>

              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{item.source}</span>
                <span>•</span>
                <span>{item.publishedAt}</span>
              </div>
            </div>
          </div>
        ))}
        
      {/* See more button */}
      <div className="flex justify-center pt-2">
        <Button variant="ghost" size="sm" className="text-muted-foreground">
          See more
        </Button>
      </div>
    </div>
  );
}
