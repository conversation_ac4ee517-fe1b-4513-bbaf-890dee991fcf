'use client';

import { useState } from 'react';
import Image from 'next/image';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Fixture, VoteOption, VoteCategory } from '@/lib/types';
import { getTeamLogoUrl } from '@/lib/utils';
import { useVoting, useHasUserVoted } from '@/hooks/useVoting';
import { Loader2, Vote } from 'lucide-react';

interface VotingModalProps {
  fixture: Fixture;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function VotingModal({ fixture, open, onOpenChange }: VotingModalProps) {
  const homeTeam = fixture.teams.home;
  const awayTeam = fixture.teams.away;
  const [selectedVote, setSelectedVote] = useState<VoteOption | null>(null);
  
  const { mutate: castVote, isPending } = useVoting(fixture.fixture.id);
  const hasUserVoted = useHasUserVoted(fixture.fixture.id, VoteCategory.MATCH_OUTCOME);

  const handleVote = () => {
    if (!selectedVote) return;

    castVote({ vote: selectedVote, category: VoteCategory.MATCH_OUTCOME }, {
      onSuccess: () => {
        onOpenChange(false);
        setSelectedVote(null);
      },
    });
  };

  const handleClose = () => {
    onOpenChange(false);
    setSelectedVote(null);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Vote className="h-5 w-5" />
            {hasUserVoted.hasVoted ? 'Update Your Vote' : 'Cast Your Vote'}
          </DialogTitle>
          <DialogDescription>
            {hasUserVoted.hasVoted 
              ? 'Change your prediction for this match'
              : 'Who do you think will win this match?'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Match Info */}
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded overflow-hidden bg-muted">
                  <Image
                    src={homeTeam.logo || getTeamLogoUrl(homeTeam.id)}
                    alt={`${homeTeam.name} logo`}
                    width={32}
                    height={32}
                    className="h-8 w-8 object-contain"
                  />
                </div>
                <span className="font-medium">{homeTeam.name}</span>
              </div>
              <span className="text-sm text-muted-foreground">vs</span>
              <div className="flex items-center gap-3">
                <span className="font-medium">{awayTeam.name}</span>
                <div className="flex h-8 w-8 items-center justify-center rounded overflow-hidden bg-muted">
                  <Image
                    src={awayTeam.logo || getTeamLogoUrl(awayTeam.id)}
                    alt={`${awayTeam.name} logo`}
                    width={32}
                    height={32}
                    className="h-8 w-8 object-contain"
                  />
                </div>
              </div>
            </div>
          </Card>

          {/* Current Vote Display */}
          {hasUserVoted.hasVoted && (
            <div className="text-center">
              <Badge variant="secondary" className="mb-2">
                Current vote: {hasUserVoted.userVote === VoteOption.HOME_WIN ? homeTeam.name :
                              hasUserVoted.userVote === VoteOption.AWAY_WIN ? awayTeam.name : 'Draw'}
              </Badge>
            </div>
          )}

          {/* Voting Options */}
          <div className="space-y-2">
            {/* Home Team */}
            <Button
              variant={selectedVote === VoteOption.HOME_WIN ? 'default' : 'outline'}
              className="w-full justify-start h-12"
              onClick={() => setSelectedVote(VoteOption.HOME_WIN)}
              disabled={isPending}
            >
              <div className="flex items-center gap-3">
                <div className="flex h-6 w-6 items-center justify-center rounded overflow-hidden bg-muted">
                  <Image
                    src={homeTeam.logo || getTeamLogoUrl(homeTeam.id)}
                    alt={`${homeTeam.name} logo`}
                    width={24}
                    height={24}
                    className="h-6 w-6 object-contain"
                  />
                </div>
                <span>{homeTeam.name} Win</span>
              </div>
            </Button>

            {/* Draw */}
            <Button
              variant={selectedVote === VoteOption.DRAW ? 'default' : 'outline'}
              className="w-full justify-center h-12"
              onClick={() => setSelectedVote(VoteOption.DRAW)}
              disabled={isPending}
            >
              Draw
            </Button>

            {/* Away Team */}
            <Button
              variant={selectedVote === VoteOption.AWAY_WIN ? 'default' : 'outline'}
              className="w-full justify-start h-12"
              onClick={() => setSelectedVote(VoteOption.AWAY_WIN)}
              disabled={isPending}
            >
              <div className="flex items-center gap-3">
                <div className="flex h-6 w-6 items-center justify-center rounded overflow-hidden bg-muted">
                  <Image
                    src={awayTeam.logo || getTeamLogoUrl(awayTeam.id)}
                    alt={`${awayTeam.name} logo`}
                    width={24}
                    height={24}
                    className="h-6 w-6 object-contain"
                  />
                </div>
                <span>{awayTeam.name} Win</span>
              </div>
            </Button>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              variant="outline"
              className="flex-1"
              onClick={handleClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              className="flex-1"
              onClick={handleVote}
              disabled={!selectedVote || isPending}
            >
              {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {hasUserVoted.hasVoted ? 'Update Vote' : 'Cast Vote'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}