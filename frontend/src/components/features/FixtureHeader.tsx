'use client';

import Image from 'next/image';
import { Fixture } from '@/lib/types';
import { getTeamLogoUrl, getLeagueLogoUrl } from '@/lib/utils';
import { format, differenceInHours, differenceInSeconds, isToday, isTomorrow } from 'date-fns';

import { CalendarIcon, FootballFieldIcon, RefereeIcon } from '@/components/ui/icons';

interface FixtureHeaderProps {
  fixture: Fixture;
  activeTab?: string;
  onTabChange?: (tab: string) => void;
}

export function FixtureHeader({ fixture, activeTab = 'overview', onTabChange }: FixtureHeaderProps) {
  const homeTeam = fixture.teams.home;
  const awayTeam = fixture.teams.away;
  const status = fixture.fixture.status;
  const goals = fixture.goals;
  const league = fixture.league;



  const isLive = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE', 'SUSP', 'INT'].includes(status.short);
  const isFinished = ['FT', 'AET', 'PEN'].includes(status.short);
  const isUpcoming = status.short === 'NS' || status.short === 'TBD';

  const formatMatchTime = () => {
    const statusShort = status.short;
    const elapsed = status.elapsed;

    // Handle not started matches
    if (statusShort === 'NS' || statusShort === 'TBD') {
      return format(new Date(fixture.fixture.date), 'HH:mm');
    }

    // Handle finished/break statuses FIRST - show status when actually finished/at break
    if (statusShort === 'FT') return 'FT';
    if (statusShort === 'AET') return 'AET';
    if (statusShort === 'PEN') return 'PEN';
    if (statusShort === 'HT') return 'HT';
    if (statusShort === 'BT') return 'Break';
    if (statusShort === 'ET') return 'ET';
    if (statusShort === 'P') return 'Penalties';

    // Handle live statuses - show elapsed time with apostrophe
    if (['1H', '2H', 'LIVE'].includes(statusShort)) {
      return (
        <span>
          {elapsed || 0}<span className="live-timer-apostrophe">&apos;</span>
        </span>
      );
    }

    // Fallback to status short
    return statusShort;
  };

  const getTimeUntilMatch = () => {
    const matchDate = new Date(fixture.fixture.date);
    const now = new Date();

    if (matchDate <= now) {
      return null; // Match has started or finished
    }

    const hoursUntil = differenceInHours(matchDate, now);
    const minutesUntil = Math.floor((differenceInSeconds(matchDate, now) % 3600) / 60);

    if (hoursUntil < 1) {
      if (minutesUntil <= 1) {
        return 'Starting soon';
      } else {
        return `in ${minutesUntil} minutes`;
      }
    } else if (hoursUntil < 24) {
      if (hoursUntil === 1 && minutesUntil > 30) {
        return 'in 1.5 hours';
      } else if (hoursUntil === 1) {
        return 'in 1 hour';
      } else {
        return `in ${hoursUntil} hours`;
      }
    } else {
      if (isToday(matchDate)) {
        return 'Today';
      } else if (isTomorrow(matchDate)) {
        return 'Tomorrow';
      } else {
        const daysDiff = Math.ceil(hoursUntil / 24);
        if (daysDiff === 2) {
          return 'Day after tomorrow';
        } else if (daysDiff <= 7) {
          return `in ${daysDiff} days`;
        } else {
          return format(matchDate, 'MMM d');
        }
      }
    }
  };

  // Define tabs based on match status
  const getTabs = () => {
    const baseTabs = [
      { id: 'overview', label: isUpcoming ? 'Preview' : 'Overview' },
      { id: 'h2h', label: 'Head-to-Head' },
      { id: 'table', label: 'Table' }
    ];

    // Always add Tips tab (for all match statuses)
    baseTabs.splice(1, 0, { id: 'tips', label: 'Tips' });

    // Add tabs for live/finished matches
    if (isLive || isFinished) {
      baseTabs.splice(2, 0,
        { id: 'stats', label: 'Stats' },
        { id: 'lineup', label: 'Lineup' }
      );
    }

    return baseTabs;
  };

  return (
    <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-3.5 pb-0">
      {/* Top Section: Competition Info */}
      <div className="flex items-center justify-center mb-4 relative">
        <div className="flex items-center space-x-3">
          {league.logo && (
            <Image
              src={league.logo || getLeagueLogoUrl(league.id)}
              alt={league.name}
              width={28}
              height={28}
              className="w-7 h-7 md:w-6 md:h-6 rounded"
            />
          )}
          <div className="text-center">
            <h2 className="font-semibold text-sm md:text-base text-foreground">{league.name}</h2>
            <p className="text-xs md:text-sm text-muted-foreground">{league.round}</p>
          </div>
        </div>

        {/* Follow Button - positioned absolutely to the right */}
        <button className="absolute right-0 px-4 py-2 bg-muted text-foreground text-sm rounded-lg hover:bg-muted/80 transition-colors">
          Follow
        </button>
      </div>

      {/* Match Details - Hidden on mobile */}
      <div className="hidden md:flex items-center justify-center space-x-6 text-sm text-muted-foreground mb-6 py-4 border-t border-b border-custom-gray -mx-4 px-4">
        <div className="flex items-center space-x-1">
          <CalendarIcon className="text-muted-foreground" width={16} height={16} />
          <span>{format(new Date(fixture.fixture.date), 'EEE dd MMM, HH:mm')}</span>
        </div>

        {fixture.fixture.venue?.name && (
          <div className="flex items-center space-x-1">
            <FootballFieldIcon className="text-muted-foreground" width={16} height={16} />
            <span>{fixture.fixture.venue.name}</span>
          </div>
        )}

        {fixture.fixture.referee && (
          <div className="flex items-center space-x-1">
            <RefereeIcon className="text-muted-foreground" width={16} height={16} />
            <span>{fixture.fixture.referee}</span>
          </div>
        )}
      </div>

      {/* Main Match Section */}
      <div className="text-center mb-6">
        {/* Teams Row */}
        <div className="flex items-center justify-between mb-4">
          {/* Home Team */}
          <div className="flex-1 flex flex-col items-center text-center">
            <Image
              src={homeTeam.logo || getTeamLogoUrl(homeTeam.id) || '/default-team-logo.png'}
              alt={homeTeam.name}
              width={40}
              height={40}
              className="w-[34px] h-[34px] md:w-10 md:h-10 rounded flex-shrink-0 mb-2"
            />
            <h3 className="font-bold text-lg md:text-2xl text-foreground mb-2 px-1">{homeTeam.name}</h3>
          </div>

          {/* Score/Time */}
          <div className="flex flex-col items-center mx-2 md:mx-4 flex-shrink-0">
            {isUpcoming ? (
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-foreground">
                  {format(new Date(fixture.fixture.date), 'HH:mm')}
                </div>
                <div className="text-sm text-muted-foreground mt-1">
                  {getTimeUntilMatch()}
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-foreground">
                  {goals.home ?? 0} - {goals.away ?? 0}
                </div>
                <div className="flex items-center justify-center space-x-2 mt-1">
                  <span className={`text-sm ${isLive ? 'text-red-500' : 'text-muted-foreground'}`}>
                    {formatMatchTime()}
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Away Team */}
          <div className="flex-1 flex flex-col items-center text-center">
            <Image
              src={awayTeam.logo || getTeamLogoUrl(awayTeam.id) || '/default-team-logo.png'}
              alt={awayTeam.name}
              width={40}
              height={40}
              className="w-[34px] h-[34px] md:w-10 md:h-10 rounded flex-shrink-0 mb-2"
            />
            <h3 className="font-bold text-lg md:text-2xl text-foreground mb-2 px-1">{awayTeam.name}</h3>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="mt-6">
        <div className="flex overflow-x-auto scrollbar-hide">
          {getTabs().map((tab) => (
            <button
              key={tab.id}
              onClick={() => onTabChange?.(tab.id)}
              className={`
                flex-shrink-0 px-4 md:px-6 py-3 text-sm font-medium cursor-pointer transition-colors border-b-2 whitespace-nowrap hover:no-underline
                ${activeTab === tab.id
                  ? 'border-primary bg-primary text-primary-foreground'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground/50 bg-transparent hover:bg-muted/50'
                }
              `}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
