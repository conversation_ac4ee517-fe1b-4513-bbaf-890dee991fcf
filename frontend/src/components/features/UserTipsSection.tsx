'use client';

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { 
  Trophy, 
  TrendingUp, 
  Target, 
  DollarSign, 
  BarChart3,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import { api } from '@/lib/api';
import { TipStatus } from '@/lib/types';
import { TIPS_QUERY_KEYS } from '@/hooks/useTips';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

export function UserTipsSection() {
  const [activeTab, setActiveTab] = useState<'overview' | 'history'>('overview');

  // Fetch user tipster stats
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: TIPS_QUERY_KEYS.userStats(),
    queryFn: () => api.getUserTipsterStats(),
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Fetch user tips
  const { data: tips, isLoading: tipsLoading } = useQuery({
    queryKey: TIPS_QUERY_KEYS.userTips(),
    queryFn: () => api.getUserTips(),
    staleTime: 1000 * 60 * 2, // 2 minutes
  });

  // Get status icon and color
  const getStatusDisplay = (status: TipStatus) => {
    switch (status) {
      case TipStatus.PENDING:
        return { icon: <Clock className="h-4 w-4" />, color: 'text-yellow-500', label: 'Pending' };
      case TipStatus.WON:
        return { icon: <CheckCircle className="h-4 w-4" />, color: 'text-green-500', label: 'Won' };
      case TipStatus.LOST:
        return { icon: <XCircle className="h-4 w-4" />, color: 'text-red-500', label: 'Lost' };
      case TipStatus.VOID:
        return { icon: <AlertCircle className="h-4 w-4" />, color: 'text-gray-500', label: 'Void' };
      case TipStatus.PUSH:
        return { icon: <AlertCircle className="h-4 w-4" />, color: 'text-blue-500', label: 'Push' };
      default:
        return { icon: <Clock className="h-4 w-4" />, color: 'text-muted-foreground', label: 'Unknown' };
    }
  };

  // Format profit with proper sign and color
  const formatProfit = (profit: number) => {
    const sign = profit >= 0 ? '+' : '';
    return `${sign}${profit.toFixed(2)}`;
  };

  // Get profit color class
  const getProfitColor = (profit: number) => {
    if (profit > 0) return 'text-green-500';
    if (profit < 0) return 'text-red-500';
    return 'text-muted-foreground';
  };

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'overview' | 'history')}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="history">Tip History</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-green-500" />
                  <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {statsLoading ? (
                  <Skeleton className="h-6 w-16" />
                ) : (
                  <div className={`text-xl font-bold ${getProfitColor(stats?.profit || 0)}`}>
                    {formatProfit(stats?.profit || 0)}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-500" />
                  <CardTitle className="text-sm font-medium">Hit Rate</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {statsLoading ? (
                  <Skeleton className="h-6 w-16" />
                ) : (
                  <div className="text-xl font-bold text-blue-500">
                    {(stats?.hitRate || 0).toFixed(1)}%
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-purple-500" />
                  <CardTitle className="text-sm font-medium">Yield</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {statsLoading ? (
                  <Skeleton className="h-6 w-16" />
                ) : (
                  <div className="text-xl font-bold text-purple-500">
                    {(stats?.yield || 0).toFixed(1)}%
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
              <CardHeader className="p-3.5 pb-2">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-orange-500" />
                  <CardTitle className="text-sm font-medium">Total Tips</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="p-3.5 pt-0">
                {statsLoading ? (
                  <Skeleton className="h-6 w-16" />
                ) : (
                  <div className="text-xl font-bold text-orange-500">
                    {stats?.totalTips || 0}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Performance Summary */}
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardHeader className="p-3.5">
              <CardTitle className="flex items-center gap-2">
                <Trophy className="h-5 w-5" />
                Performance Summary
              </CardTitle>
              <CardDescription>
                Your overall tipping performance and statistics
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3.5 pt-0">
              {statsLoading ? (
                <div className="space-y-4">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              ) : stats ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Won Tips</span>
                      <span className="font-semibold text-green-500">{stats.wonTips}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Lost Tips</span>
                      <span className="font-semibold text-red-500">{stats.lostTips}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Void Tips</span>
                      <span className="font-semibold text-gray-500">{stats.voidTips}</span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Average Odds</span>
                      <span className="font-semibold">{(stats.averageOdds || 0).toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Current Streak</span>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold">{stats.currentStreak || 0}</span>
                        {stats.currentStreakType === 'win' && stats.currentStreak > 0 && (
                          <Badge variant="default" className="text-xs">🔥</Badge>
                        )}
                        {stats.currentStreakType === 'lose' && stats.currentStreak > 0 && (
                          <Badge variant="destructive" className="text-xs">❄️</Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Best Win Streak</span>
                      <span className="font-semibold">{stats.longestWinStreak || 0}</span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Trophy className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No tipping statistics yet</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Start creating tips to see your performance here!
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardHeader className="p-3.5">
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Explore more tipping features and rankings
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3.5 pt-0">
              <div className="flex flex-wrap gap-3">
                <Button asChild variant="outline" size="sm">
                  <Link href="/tipsters/ranking">
                    <Trophy className="h-4 w-4 mr-2" />
                    View Rankings
                    <ExternalLink className="h-3 w-3 ml-2" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="sm">
                  <Link href="/">
                    <Target className="h-4 w-4 mr-2" />
                    Create Tips
                    <ExternalLink className="h-3 w-3 ml-2" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history" className="space-y-6">
          <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <CardHeader className="p-3.5">
              <CardTitle>Recent Tips</CardTitle>
              <CardDescription>
                Your latest tips and their outcomes
              </CardDescription>
            </CardHeader>
            <CardContent className="p-3.5 pt-0">
              {tipsLoading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Skeleton className="h-10 w-10 rounded" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-48" />
                        <Skeleton className="h-3 w-32" />
                      </div>
                      <Skeleton className="h-6 w-16" />
                    </div>
                  ))}
                </div>
              ) : tips && tips.length > 0 ? (
                <div className="space-y-4">
                  {tips.slice(0, 10).map((tip) => {
                    const statusDisplay = getStatusDisplay(tip.status);
                    return (
                      <div key={tip._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-semibold">
                              {tip.fixture?.teams?.home?.name} vs {tip.fixture?.teams?.away?.name}
                            </span>
                            <Badge variant="outline" className="text-xs">
                              {tip.tipType}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Odds: {tip.odds.toFixed(2)} • {formatDistanceToNow(new Date(tip.createdAt), { addSuffix: true })}
                          </div>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className={`flex items-center gap-1 ${statusDisplay.color}`}>
                            {statusDisplay.icon}
                            <span className="text-sm font-medium">{statusDisplay.label}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  {tips.length > 10 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" size="sm">
                        View All Tips
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg text-muted-foreground">No tips yet</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Start creating tips on match pages to see them here!
                  </p>
                  <Button asChild className="mt-4" size="sm">
                    <Link href="/">
                      Browse Matches
                    </Link>
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
