'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Send, MessageCircle, Users, AlertCircle, LogIn, Smile } from 'lucide-react';
import { useFixtureChat } from '@/hooks/useFixtureChat';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';

interface FixtureChatProps {
  fixtureId: number;
}

export function FixtureChat({ fixtureId }: FixtureChatProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const { user } = useAuth();
  const emojiPickerRef = useRef<HTMLDivElement>(null);

  // Available emojis for the chat
  const availableEmojis = ['🐐', '⚽', '🔥', '💪', '👏', '😍', '😢', '😡', '🎯', '⭐', '👑', '🚀'];

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  const {
    messages,
    isConnected,
    isChatOpen,
    isLoading,
    error,
    messageInput,
    setMessageInput,
    sendMessage,
    handleKeyPress,
    messagesEndRef,
    isAuthenticated,
    isJoined
  } = useFixtureChat({ fixtureId });

  // Function to insert emoji into message input
  const insertEmoji = (emoji: string) => {
    setMessageInput(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return (
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardHeader className="p-3.5">
          <CardTitle className="flex items-center gap-2 text-sm font-medium">
            <MessageCircle className="h-4 w-4" />
            Match Chat
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3.5 pt-0">
          <div className="flex flex-col items-center gap-3 py-4 text-center">
            <LogIn className="h-8 w-8 text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">Join the conversation</p>
              <p className="text-xs text-muted-foreground mt-1">
                Sign in to chat with other fans during the match
              </p>
            </div>
            <Link href="/auth/login">
              <Button size="sm" className="mt-2">
                Sign In
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <CardHeader className="p-3.5">
          <CardTitle className="flex items-center gap-2 text-sm font-medium">
            <MessageCircle className="h-4 w-4" />
            Match Chat
          </CardTitle>
        </CardHeader>
        <CardContent className="p-3.5 pt-0">
          <div className="flex items-center justify-center py-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-sm text-muted-foreground">Connecting to chat...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Chat closed state - hide completely when chat is closed
  if (!isChatOpen && !isLoading) {
    return null;
  }

  return (
    <Card className="rounded-lg border bg-card text-card-foreground shadow-sm">
      <CardHeader 
        className="p-3.5 cursor-pointer hover:bg-muted/30 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <CardTitle className="flex items-center justify-between text-sm font-medium">
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4" />
            Match Chat
            {isConnected && isJoined && (
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 bg-green-500 rounded-full" />
                <span className="text-xs text-muted-foreground">Live</span>
              </div>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">
              {isExpanded ? 'Click to minimize' : 'Click to expand'}
            </span>
          </div>
        </CardTitle>
      </CardHeader>

      {isExpanded && (
        <CardContent className="p-3.5 pt-0 space-y-4">
          {/* Error message */}
          {error && (
            <div className="flex items-center gap-2 p-2 bg-destructive/10 text-destructive rounded text-xs">
              <AlertCircle className="h-3 w-3" />
              {error}
            </div>
          )}

          {/* Messages area */}
          <div className="space-y-3">
            <ScrollArea className="h-64 w-full rounded border p-2" data-chat-scroll-area>
              <div className="space-y-2">
                {messages.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-32 text-center">
                    <MessageCircle className="h-8 w-8 text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">No messages yet</p>
                    <p className="text-xs text-muted-foreground">Be the first to start the conversation!</p>
                  </div>
                ) : (
                  messages.map((message) => {
                    const isOwnMessage = message.userId === user?._id;
                    return (
                      <div key={message._id} className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-xs font-medium text-foreground">
                            {isOwnMessage ? 'You' : message.userName}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                          </span>
                        </div>
                        <div className={`text-sm text-foreground rounded p-2 break-words ${
                          isOwnMessage
                            ? 'bg-blue-500/20 border border-blue-500/30'
                            : 'bg-muted/30'
                        }`}>
                          {message.content}
                        </div>
                      </div>
                    );
                  })
                )}
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>
          </div>

          {/* Message input */}
          <div className="space-y-2">
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Input
                  value={messageInput}
                  onChange={(e) => setMessageInput(e.target.value)}
                  onKeyDown={handleKeyPress}
                  placeholder="Type your message..."
                  disabled={!isConnected || !isJoined}
                  maxLength={500}
                  className="text-sm pr-10"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  disabled={!isConnected || !isJoined}
                >
                  <Smile className="h-4 w-4" />
                </Button>

                {/* Emoji Picker */}
                {showEmojiPicker && (
                  <div ref={emojiPickerRef} className="absolute bottom-full right-0 mb-2 bg-background border rounded-lg shadow-lg p-2 z-10">
                    <div className="grid grid-cols-6 gap-1">
                      {availableEmojis.map((emoji) => (
                        <Button
                          key={emoji}
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 text-lg hover:bg-muted"
                          onClick={() => insertEmoji(emoji)}
                        >
                          {emoji}
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              <Button
                onClick={sendMessage}
                disabled={!messageInput.trim() || !isConnected || !isJoined}
                size="sm"
                className="px-3"
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
            {messageInput.length > 400 && (
              <div className="text-xs text-muted-foreground text-right">
                {messageInput.length}/500 characters
              </div>
            )}
          </div>

          {/* Connection status */}
          {!isConnected && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <div className="h-2 w-2 bg-red-500 rounded-full" />
              Disconnected - trying to reconnect...
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}
