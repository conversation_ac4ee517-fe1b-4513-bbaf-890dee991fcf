'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AuthModal } from './AuthModal';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  requireAuth?: boolean;
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requireAuth = true 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading spinner while checking auth status
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  // If auth is not required, always show children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // If auth is required but user is not authenticated
  if (!isAuthenticated) {
    // Show custom fallback if provided
    if (fallback) {
      return <>{fallback}</>;
    }

    // Default fallback - show auth modal
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-center space-y-4">
          <h3 className="text-lg font-semibold">Authentication Required</h3>
          <p className="text-muted-foreground">
            Please sign in to access this feature.
          </p>
          <AuthModal open={true} onOpenChange={() => {}} />
        </div>
      </div>
    );
  }

  // User is authenticated, show protected content
  return <>{children}</>;
}

// Hook for conditional rendering based on auth status
export function useAuthGuard() {
  const { isAuthenticated, isLoading } = useAuth();

  return {
    isAuthenticated,
    isLoading,
    requireAuth: (content: ReactNode, fallback?: ReactNode) => (
      <ProtectedRoute fallback={fallback}>
        {content}
      </ProtectedRoute>
    ),
  };
}
