'use client';

import Image from 'next/image';
import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { isApiSportsImage } from '@/lib/image-loader';

interface SmartImageProps {
  src: string | undefined | null;
  alt: string;
  width: number;
  height: number;
  fallbackText?: string;
  fallbackIcon?: string;
  className?: string;
  priority?: boolean;
  onError?: () => void;
}

export function SmartImage({
  src,
  alt,
  width,
  height,
  fallbackText,
  fallbackIcon = '⚽',
  className,
  priority = false,
  onError,
}: SmartImageProps) {
  const [imageError, setImageError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 1; // Reduced retries to avoid hammering API-Sports

  // Check if src is empty or invalid
  const isValidSrc = src && src.trim() !== '';

  // Reset error state when src changes
  useEffect(() => {
    setImageError(false);
    setRetryCount(0);
  }, [src]);

  // If src is empty/invalid, show fallback immediately
  if (!isValidSrc) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground font-medium',
          className
        )}
        style={{ width, height }}
        title={fallbackText || alt}
      >
        <span className="text-center">
          {fallbackIcon}
        </span>
      </div>
    );
  }

  const handleImageError = () => {
    console.warn(`Image failed to load: ${src}`);

    // With our proxy, API-Sports images should be more reliable
    // But still show fallback if they fail
    if (retryCount < maxRetries) {
      setTimeout(() => {
        setRetryCount(prev => prev + 1);
      }, 1000);
    } else {
      setImageError(true);
      onError?.();
    }
  };

  // If image failed, show fallback
  if (imageError) {
    return (
      <div
        className={cn(
          'flex items-center justify-center bg-muted text-muted-foreground font-medium',
          className
        )}
        style={{ width, height }}
        title={fallbackText || alt}
      >
        <span className="text-center">
          {fallbackIcon}
        </span>
      </div>
    );
  }

  // Now all images go through Next.js Image component with our custom loader
  // API-Sports images will be automatically routed through our proxy

  // Only use blur placeholder for images that are 40x40 or larger (Next.js requirement)
  const shouldUseBlurPlaceholder = isApiSportsImage(src) && width >= 40 && height >= 40;

  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      onError={handleImageError}
      quality={85}
      // Only add blur placeholder for images that meet Next.js size requirements
      placeholder={shouldUseBlurPlaceholder ? 'blur' : 'empty'}
      blurDataURL={shouldUseBlurPlaceholder ? 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==' : undefined}
    />
  );
}
