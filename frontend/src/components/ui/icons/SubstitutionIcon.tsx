import React from 'react';

interface SubstitutionIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const SubstitutionIcon: React.FC<SubstitutionIconProps> = ({
  className = '',
  width = 16,
  height = 16,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 16 16"
      fill="none"
      className={className}
    >
      <mask
        id="substitution-mask"
        style={{ maskType: 'alpha' }}
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="16"
        height="16"
      >
        <path fill="#D9D9D9" d="M0 0h16v16H0z" />
      </mask>
      <g mask="url(#substitution-mask)">
        <path
          d="M8.033 13.333q-2.233 0-3.8-1.55T2.667 8v-.117L1.6 8.95l-.933-.934L3.333 5.35 6 8.016l-.934.934L4 7.883V8q0 1.666 1.175 2.833T8.033 12q.434 0 .85-.1.417-.1.817-.3l1 1a5.5 5.5 0 0 1-1.3.55q-.667.183-1.367.183"
          fill="#5de482"
        />
        <path
          d="M8.333 2.35q2.234 0 3.8 1.55Q13.7 5.45 13.7 7.685v.117l1.066-1.067.934.933-2.667 2.667-2.667-2.667.934-.933 1.066 1.067v-.117q0-1.667-1.175-2.833T8.333 3.684a3.6 3.6 0 0 0-.85.1q-.417.1-.817.3l-1-1a5.5 5.5 0 0 1 1.3-.55 5 5 0 0 1 1.367-.183"
          fill="#f1281e"
        />
      </g>
    </svg>
  );
};