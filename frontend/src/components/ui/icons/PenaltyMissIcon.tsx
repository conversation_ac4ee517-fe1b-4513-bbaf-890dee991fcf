import React from 'react';

interface PenaltyMissIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const PenaltyMissIcon: React.FC<PenaltyMissIconProps> = ({
  className = '',
  width = 20,
  height = 20,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.667 1.667h-15V10h1.666V3.333h13.334V10h1.666V1.667zM7.042 7.5l3.125 3.125 3.04-3.041 2.063 2.062-3.04 3.041 3.061 3.063-2.062 2.062-3.062-3.062-3.146 3.146-2.063-2.063 3.146-3.146L4.98 9.562z"
        fill="#D70C17"
      />
    </svg>
  );
};