interface FootballFieldIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export function FootballFieldIcon({ className = "text-muted-foreground", width = 20, height = 20 }: FootballFieldIconProps) {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g/>
      <g strokeLinecap="round" strokeLinejoin="round"/>
      <g fill="currentColor" stroke="none">
        <path d="M18.11 22.71H5.9a2.67 2.67 0 0 1-2.67-2.66V4A2.67 2.67 0 0 1 5.9 1.29h12.21A2.66 2.66 0 0 1 20.77 4v16.1a2.66 2.66 0 0 1-2.66 2.61M5.9 2.79A1.16 1.16 0 0 0 4.73 4v16.1a1.16 1.16 0 0 0 1.17 1.11h12.21a1.16 1.16 0 0 0 1.16-1.16V4a1.16 1.16 0 0 0-1.16-1.16z"/>
        <path d="M20 12.75h-5.53a.75.75 0 1 1 0-1.5H20a.75.75 0 1 1 0 1.5m-10.46 0H4a.75.75 0 1 1 0-1.5h5.54a.75.75 0 1 1 0 1.5m5.82-6.62H8.64a.76.76 0 0 1-.75-.75V2a.75.75 0 0 1 .75-.75h6.72a.74.74 0 0 1 .75.75v3.38a.75.75 0 0 1-.75.75m-6-1.5h5.22V2.79H9.39zm6 18.08H8.64a.75.75 0 0 1-.75-.71v-3.38a.76.76 0 0 1 .75-.75h6.72a.75.75 0 0 1 .75.75V22a.74.74 0 0 1-.75.71m-6-1.5h5.22v-1.84H9.39z"/>
        <path d="M12 15.15a3.22 3.22 0 1 1 0-6.44 3.22 3.22 0 0 1 0 6.44m0-4.93a1.72 1.72 0 1 0 0 3.44 1.72 1.72 0 0 0 0-3.44"/>
      </g>
    </svg>
  );
}