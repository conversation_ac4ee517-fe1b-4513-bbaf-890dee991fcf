import React from 'react';

interface YellowCardIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const YellowCardIcon: React.FC<YellowCardIconProps> = ({
  className = '',
  width = 20,
  height = 20,
}) => {
  return (
    <svg
      fill="none"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      className={className}
    >
      <rect
        fill="#FBBF24"
        height="14"
        rx="2"
        width="10"
        x="5"
        y="3"
      />
    </svg>
  );
};