import React from 'react';

interface GoalIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const GoalIcon: React.FC<GoalIconProps> = ({
  className = '',
  width = 18,
  height = 18,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 18 18"
      fill="none"
      width={width}
      height={height}
      className={className}
    >
      <g clipPath="url(#goal-clip)" fill="currentColor">
        <path d="m9 6.37 2.502 1.817-.956 2.941H7.454l-.956-2.94z" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9 .45a8.55 8.55 0 1 0 0 17.1A8.55 8.55 0 0 0 9 .45M7.002 2.045A7.25 7.25 0 0 0 3.004 4.95l.87-.246.539 2.806-2.085 1.953-.558-.71a7.2 7.2 0 0 0 1.526 4.698l.034-.904 2.835.355 1.213 2.587-.85.312A7.2 7.2 0 0 0 9 16.235c.868 0 1.7-.153 2.472-.434l-.85-.312 1.213-2.587 2.835-.355.034.903a7.2 7.2 0 0 0 1.526-4.697l-.558.71-2.085-1.953.539-2.806.87.246a7.25 7.25 0 0 0-3.998-2.905l.504.753L9 4.177l-2.502-1.38z"
        />
      </g>
      <defs>
        <clipPath id="goal-clip">
          <path fill="#fff" d="M0 0h18v18H0z" />
        </clipPath>
      </defs>
    </svg>
  );
};