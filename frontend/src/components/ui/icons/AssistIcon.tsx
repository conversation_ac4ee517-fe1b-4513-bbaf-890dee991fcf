import React from 'react';

interface AssistIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const AssistIcon: React.FC<AssistIconProps> = ({
  className = '',
  width = 18,
  height = 18,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 480.043 480.043"
      className={className}
      transform="rotate(-30)"
    >
      <g strokeWidth="0" />
      <g strokeLinecap="round" strokeLinejoin="round" />
      <path
        fill="currentColor"
        d="M432.7 341.222c-4-.1-8-.1-12.4-.1-6.5 0-12.199.1-17.8.4l5.7 31.299c.2 1.301 1.4 2.201 2.7 2.201h27.7c1.3 0 2.399-.9 2.699-2.201l6.5-31.6h-5c-3.198.1-6.499.1-10.099.001m-98.4 32.099c.2 1.301 1.4 2.201 2.7 2.201h27.7c1.3 0 2.4-.9 2.7-2.201l6.2-30.6c-12.301.5-27.601 1-44.5 1.5zm-214.9 4.301c.2 1.3 1.4 2.199 2.7 2.199h27.7c1.3 0 2.4-.899 2.7-2.199l5.8-28.5c-17.5.4-32.6.699-43.9.9zm-69.6-33.9 6.2 33.9c.2 1.3 1.4 2.199 2.7 2.199h27.7c1.3 0 2.4-.899 2.7-2.199l5.7-27.7c-14.8-.7-30.2-3.601-44.4-8.3q-.75 1.05-.6 2.1m387.9-198.3c-10.7 0-9.7 32.4-32.5 40.7-11 4-21.6 5.6-30.9 5.6-16.6 0-29.3-4.9-34-9.5-7.399-7.2 59.801-29.9 45.4-49.4l-22-29.5c-1.5-2-3.8-3.1-6.1-3.1-1.5 0-3 .5-4.4 1.5l-73.8 54.2s-28.2 0-33.1 16.5c0 0-.801-.1-2.201-.1-6.1 0-22.699 1.3-27.199 15.2 0 0-69.9 13.8-111.6 36.2-41.6 22.301-105.3 21-105.3 56.5s56.4 59.2 101.1 59.2 269-6.8 300.4-8.601c6.7-.399 13-.5 18.9-.5 8.399 0 15.899.201 22.5.201 23.8 0 36.1-3 36.5-30.301.6-40.199 5.5-102.4-25.101-141.3-7.899-9.699-12.999-13.499-16.599-13.499m-145.5 156.8h-37c-.7 0-1.3-.3-1.8-.699l-80.3-73.5c-1.6-1.5-.9-4.1 1.1-4.6l28-7.5c.9-.2 1.9 0 2.5.6l89.399 81c1.801 1.599.601 4.699-1.899 4.699m74.7-.1-39-.5c-.7 0-1.3-.301-1.8-.7l-99.601-90.3c-1.6-1.5-1-4.1 1.1-4.7l27.9-7.5c.9-.2 1.801 0 2.5.6l110.8 98.299c1.802 1.701.601 4.801-1.899 4.801"
      />
    </svg>
  );
};