import React from 'react';

interface VarIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const VarIcon: React.FC<VarIconProps> = ({
  className = '',
  width = 24,
  height = 24,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      height={height}
      viewBox="0 0 120.966 79.375"
      width={width}
      className={className}
    >
      <g fill="currentColor" strokeWidth="1.074">
        <path d="M0 0v72.972h37.248v5.864c0 .299.24.539.538.539H87.2c.298 0 .539-.24.539-.539v-5.864h33.226V0zm6.932 6.93h107.103v59.11H6.932z" />
        <path d="M80.72 19.085V52.9h8.328V41.371h2.014l7.441 11.882 9.136-1.464-8.343-12.37c3.317-1.711 6.336-4.617 6.336-8.976 0-8.488-5.488-11.358-12.404-11.358zm11.329 6.496c1.015.023 5.184.283 5.184 4.72 0 5.072-5.412 4.722-5.412 4.722h-2.773V25.58zm-36.3-6.464-12.38 34.136h8.436l2.17-6.952h11.632l2.318 7.274 8.743-1.401-11.87-33.057zm3.983 8.747 3.77 11.833h-7.465zm-36.109-8.747-9.28 1.487 11.839 32.971h9.05L47.61 19.44h-9.003l-7.364 23.587z" />
      </g>
    </svg>
  );
};