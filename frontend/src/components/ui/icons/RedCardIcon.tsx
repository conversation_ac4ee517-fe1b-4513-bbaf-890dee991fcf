import React from 'react';

interface RedCardIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const RedCardIcon: React.FC<RedCardIconProps> = ({
  className = '',
  width = 20,
  height = 20,
}) => {
  return (
    <svg
      fill="none"
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      className={className}
    >
      <rect
        fill="#DC2626"
        height="14"
        rx="2"
        width="10"
        x="5"
        y="3"
      />
    </svg>
  );
};