interface CalendarIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export function CalendarIcon({ className = "text-muted-foreground", width = 16, height = 16 }: CalendarIconProps) {
  return (
    <svg 
      width={width} 
      height={height} 
      viewBox="0 0 24 24" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path 
        d="M6.585 20h10.822C19.112 20 20 19.041 20 17.21V6.79C20 4.96 19.112 4 17.407 4H6.585C4.888 4 4 4.95 4 6.79v10.42C4 19.04 4.888 20 6.585 20m-.007-1.704c-.653 0-1.014-.368-1.014-1.121V9.299c0-.753.361-1.121 1.014-1.121h10.837c.652 0 1.013.368 1.013 1.121v7.876c0 .753-.361 1.121-1.014 1.121zm3.905-7.165h.464c.283 0 .377-.094.377-.394v-.505c0-.308-.094-.402-.377-.402h-.464c-.283 0-.377.094-.377.402v.505c0 .3.094.394.377.394m2.57 0h.464c.275 0 .37-.094.37-.394v-.505c0-.308-.095-.402-.37-.402h-.464c-.283 0-.377.094-.377.402v.505c0 .3.094.394.377.394m2.562 0h.464c.282 0 .377-.094.377-.394v-.505c0-.308-.095-.402-.377-.402h-.464c-.275 0-.37.094-.37.402v.505c0 .3.095.394.37.394M7.92 13.888h.456c.283 0 .377-.095.377-.403v-.505c0-.3-.094-.394-.377-.394H7.92c-.282 0-.377.095-.377.394v.505c0 .309.095.403.377.403m2.562 0h.464c.283 0 .377-.095.377-.403v-.505c0-.3-.094-.394-.377-.394h-.464c-.283 0-.377.095-.377.394v.505c0 .309.094.403.377.403m2.57 0h.464c.275 0 .37-.095.37-.403v-.505c0-.3-.095-.394-.37-.394h-.464c-.283 0-.377.095-.377.394v.505c0 .309.094.403.377.403m2.562 0h.464c.282 0 .377-.095.377-.403v-.505c0-.3-.095-.394-.377-.394h-.464c-.275 0-.37.095-.37.394v.505c0 .309.095.403.37.403M7.92 16.636h.456c.283 0 .377-.095.377-.394v-.505c0-.308-.094-.403-.377-.403H7.92c-.282 0-.377.095-.377.403v.505c0 .3.095.394.377.394m2.562 0h.464c.283 0 .377-.095.377-.394v-.505c0-.308-.094-.403-.377-.403h-.464c-.283 0-.377.095-.377.403v.505c0 .3.094.394.377.394m2.57 0h.464c.275 0 .37-.095.37-.394v-.505c0-.308-.095-.403-.37-.403h-.464c-.283 0-.377.095-.377.403v.505c0 .3.094.394.377.394" 
        fill="currentColor"
      />
    </svg>
  );
}