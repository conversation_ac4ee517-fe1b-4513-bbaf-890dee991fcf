import React from 'react';

interface DoubleYellowIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const DoubleYellowIcon: React.FC<DoubleYellowIconProps> = ({
  className = '',
  width = 20,
  height = 20,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 120 120"
      fill="none"
      className={className}
    >
      <path
        d="M40 30H25c-5.523 0-10 4.477-10 10v60c0 5.523 4.477 10 10 10h40c5.523 0 10-4.477 10-10v-5H55c-8.284 0-15-6.716-15-15z"
        fill="#FFB800"
      />
      <path
        d="M45 20c0-5.523 4.477-10 10-10h40c5.523 0 10 4.477 10 10v60c0 5.523-4.477 10-10 10H55c-5.523 0-10-4.477-10-10z"
        fill="#FF495C"
      />
    </svg>
  );
};