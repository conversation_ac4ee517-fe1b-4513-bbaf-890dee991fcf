import React from 'react';

interface PenaltyIconProps {
  className?: string;
  width?: number;
  height?: number;
}

export const PenaltyIcon: React.FC<PenaltyIconProps> = ({
  className = '',
  width = 20,
  height = 20,
}) => {
  return (
    <svg
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      fill="none"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16 12a6 6 0 1 1-12 0 6 6 0 0 1 12 0m-5.804-3.994 1.572 1.44-1.31 1.834-2.357-.458-.065-2.03zm-.589 6.94 1.179 1.245 2.03-.852.392-1.833-2.03-.262-1.57 1.703zM10 16.518c-1.244 0-2.357-.459-3.143-1.244l.851-.786-.785-2.16-1.31-.459v.982c-.13-.262-.13-.59-.13-.851 0-.59.13-1.179.327-1.703l1.113-1.374.262-.459C7.97 7.81 8.952 7.482 10 7.482h.066l.523.196 1.113.131c.72.262 1.31.72 1.834 1.31l-.197.786.72 2.095.459.13c-.066 2.423-2.095 4.388-4.518 4.388"
        fill="#1B883F"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.333 1.667h15V10h-1.666V3.333H3.333V10H1.667V1.667z"
        fill="#1B883F"
      />
    </svg>
  );
};