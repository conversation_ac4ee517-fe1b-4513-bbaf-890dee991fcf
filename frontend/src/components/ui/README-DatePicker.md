# DatePicker Component

A reusable date picker component that opens a calendar popover when clicked. Features include date navigation, a "Today" button, and customizable styling.

## Features

- 📅 Calendar popover with date selection
- 🎯 "Today" button for quick navigation to current date
- 🎨 Customizable button variants and sizes
- 📱 Responsive design
- 🌐 Smart date formatting (Today, Tomorrow, Yesterday)
- ⌨️ Keyboard navigation support
- 🎭 Light/dark mode support

## Usage

### Basic Usage

```tsx
import { DatePicker } from "@/components/ui/date-picker"

function MyComponent() {
  const [date, setDate] = useState<Date | undefined>(new Date())

  return (
    <DatePicker
      date={date}
      onDateChange={setDate}
      placeholder="Pick a date"
    />
  )
}
```

### Advanced Usage

```tsx
<DatePicker
  date={selectedDate}
  onDateChange={handleDateChange}
  placeholder="Select a date"
  buttonVariant="outline"
  buttonSize="sm"
  showTodayButton={true}
  dateFormat="MM/dd/yyyy"
  className="min-w-[140px]"
  disabled={false}
/>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `date` | `Date \| undefined` | `undefined` | The selected date |
| `onDateChange` | `(date: Date \| undefined) => void` | `undefined` | Callback when date changes |
| `placeholder` | `string` | `"Pick a date"` | Placeholder text when no date selected |
| `disabled` | `boolean` | `false` | Whether the picker is disabled |
| `className` | `string` | `undefined` | Additional CSS classes |
| `buttonVariant` | `"default" \| "destructive" \| "outline" \| "secondary" \| "ghost" \| "link"` | `"outline"` | Button style variant |
| `buttonSize` | `"default" \| "sm" \| "lg" \| "icon"` | `"sm"` | Button size |
| `showTodayButton` | `boolean` | `true` | Whether to show the Today button |
| `dateFormat` | `string` | `"PPP"` | Date format string (date-fns format) |

## Examples

### Different Button Variants

```tsx
{/* Outline variant (default) */}
<DatePicker buttonVariant="outline" />

{/* Secondary variant */}
<DatePicker buttonVariant="secondary" />

{/* Ghost variant */}
<DatePicker buttonVariant="ghost" />
```

### Custom Date Format

```tsx
{/* US format */}
<DatePicker dateFormat="MM/dd/yyyy" />

{/* European format */}
<DatePicker dateFormat="dd/MM/yyyy" />

{/* Long format */}
<DatePicker dateFormat="EEEE, MMMM do, yyyy" />
```

### Without Today Button

```tsx
<DatePicker showTodayButton={false} />
```

## Integration with Forms

The DatePicker works seamlessly with form libraries:

```tsx
// With React Hook Form
<Controller
  name="date"
  control={control}
  render={({ field }) => (
    <DatePicker
      date={field.value}
      onDateChange={field.onChange}
      placeholder="Select date"
    />
  )}
/>
```

## Styling

The component uses Tailwind CSS classes and can be customized via the `className` prop. The button styling follows the design system variants defined in the Button component.

## Accessibility

- Full keyboard navigation support
- ARIA labels and roles
- Focus management
- Screen reader friendly

## Dependencies

- `react-day-picker` - Calendar component
- `date-fns` - Date formatting
- `@radix-ui/react-popover` - Popover primitive
- `lucide-react` - Calendar icon
