'use client';

import { Head<PERSON> } from './Header';
import { Footer } from './Footer';
import { cn } from '@/lib/utils';

interface FixtureLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export function FixtureLayout({ children, className }: FixtureLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <Header />
      
      {/* Main content area - full width without sidebar */}
      <div className="w-full px-2 sm:px-4 py-6">
        <main className={cn(
          'w-full max-w-7xl mx-auto min-h-[calc(100vh-4rem)]',
          className
        )}>
          {children}
        </main>
      </div>
      
      {/* Footer */}
      <Footer />
    </div>
  );
}
