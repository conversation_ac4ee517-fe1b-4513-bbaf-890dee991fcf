'use client';

import Link from 'next/link';
import { FOOTER_LINKS, SOCIAL_LINKS, BETTING_PARTNERS, APP_STORE_LINKS } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface FooterProps {
  className?: string;
}

export function Footer({ className }: FooterProps) {
  return (
    <footer className={cn('border-t bg-background', className)}>
      {/* Betting partners section */}
      <div className="border-b bg-muted/30">
        <div className="container px-4 py-6">
          <div className="flex flex-wrap items-center justify-center gap-6 md:gap-8">
            {BETTING_PARTNERS.map((partner) => (
              <Link
                key={partner.name}
                href={partner.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
              >
                {/* Partner logo placeholder */}
                <div className="h-8 w-20 bg-muted rounded flex items-center justify-center text-xs font-medium">
                  {partner.name}
                </div>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Main footer content */}
      <div className="w-full px-4 py-8 max-w-7xl mx-auto">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Important links */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold uppercase tracking-wide">Important</h3>
            <ul className="space-y-2">
              {FOOTER_LINKS.important.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Quick links */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold uppercase tracking-wide">Quick Links</h3>
            <ul className="space-y-2">
              {FOOTER_LINKS.quickLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Features */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold uppercase tracking-wide">Features</h3>
            <ul className="space-y-2">
              {FOOTER_LINKS.features.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Predictions */}
          <div className="space-y-4">
            <h3 className="text-sm font-semibold uppercase tracking-wide">Predictions</h3>
            <ul className="space-y-2">
              {FOOTER_LINKS.predictions.map((link) => (
                <li key={link.href}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* App download and social links */}
        <div className="mt-8 flex flex-col items-center justify-between gap-4 border-t pt-8 md:flex-row">
          {/* App store links */}
          <div className="flex items-center gap-4">
            <Link
              href={APP_STORE_LINKS.ios}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 rounded-lg bg-muted px-4 py-2 text-sm hover:bg-muted/80 transition-colors cursor-pointer"
            >
              <div className="h-6 w-6 bg-foreground rounded text-background flex items-center justify-center text-xs">
                📱
              </div>
              App Store
            </Link>
            <Link
              href={APP_STORE_LINKS.android}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 rounded-lg bg-muted px-4 py-2 text-sm hover:bg-muted/80 transition-colors cursor-pointer"
            >
              <div className="h-6 w-6 bg-foreground rounded text-background flex items-center justify-center text-xs">
                🤖
              </div>
              Google Play
            </Link>
          </div>

          {/* Social links */}
          <div className="flex items-center gap-4">
            {SOCIAL_LINKS.map((social) => (
              <Link
                key={social.platform}
                href={social.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
              >
                <div className="h-8 w-8 rounded-full bg-muted flex items-center justify-center text-sm">
                  {getSocialIcon(social.icon)}
                </div>
                <span className="sr-only">{social.platform}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-8 border-t pt-4 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 KickoffScore. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}

// Helper function to get social media icons
function getSocialIcon(icon: string): string {
  const iconMap: Record<string, string> = {
    facebook: '📘',
    instagram: '📷',
    telegram: '✈️',
    discord: '💬',
  };
  
  return iconMap[icon] || '🔗';
}
