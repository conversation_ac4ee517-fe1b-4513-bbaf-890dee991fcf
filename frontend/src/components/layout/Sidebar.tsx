'use client';

import React, { useState, useMemo, useCallback } from 'react';
import { ChevronDown, ChevronRight, Filter } from 'lucide-react';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn, getLeagueLogoUrl } from '@/lib/utils';
import { TOP_LEAGUES } from '@/lib/constants';
import { useLeagues } from '@/hooks/useLeagues';
import { SimpleLeague } from '@/lib/types';
import { getLeagueTier, getLeaguePriority } from '@/lib/leagueTiers';

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

export function Sidebar({ isOpen = true, onClose, className }: SidebarProps) {
  const [isAllLeaguesExpanded, setIsAllLeaguesExpanded] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCountries, setExpandedCountries] = useState<Set<string>>(new Set());

  // Fetch all leagues from the backend
  const { data: allLeagues, isLoading: isLoadingLeagues, error: leaguesError } = useLeagues();

  const handleLeagueClick = useCallback((leagueId: number | undefined) => {
    if (!leagueId) {
      console.warn('League ID is undefined');
      return;
    }
    // TODO: Implement league navigation
    console.log('Navigate to league:', leagueId);
  }, []);

  const toggleCountry = useCallback((country: string) => {
    const newExpanded = new Set(expandedCountries);
    if (newExpanded.has(country)) {
      newExpanded.delete(country);
    } else {
      newExpanded.add(country);
    }
    setExpandedCountries(newExpanded);
  }, [expandedCountries]);

  // Convert backend leagues to simple format for easier use - memoized to prevent infinite re-renders
  const simpleLeagues: SimpleLeague[] = useMemo(() => {
    return allLeagues?.map(league => ({
      id: league?.league?.id || league?._id || 0,
      name: league?.league?.name || '',
      country: league?.country?.name || '',
      logo: league?.league?.logo || '',
      flag: league?.country?.flag || '',
      type: league?.league?.type || ''
    })) || [];
  }, [allLeagues]);

  // Filter and organize leagues - memoized to prevent infinite re-renders
  const { leaguesByCountry, sortedCountries } = useMemo(() => {
    // Filter leagues based on search query (with null/undefined safety)
    const filtered = simpleLeagues.filter(league => {
      const leagueName = league?.name?.toLowerCase() || '';
      const leagueCountry = league?.country?.toLowerCase() || '';
      const query = searchQuery.toLowerCase();

      return leagueName.includes(query) || leagueCountry.includes(query);
    });

    // Group leagues by country and sort by tier within each country
    const byCountry = filtered.reduce((acc, league) => {
      const country = league.country || 'Unknown';
      if (!acc[country]) {
        acc[country] = [];
      }
      acc[country].push(league);
      return acc;
    }, {} as Record<string, SimpleLeague[]>);

    // Sort leagues within each country by tier and priority
    Object.keys(byCountry).forEach(country => {
      byCountry[country].sort((a, b) => {
        const tierA = getLeagueTier(a.id);
        const tierB = getLeagueTier(b.id);

        // Sort by tier first (lower tier number = higher priority)
        if (tierA !== tierB) {
          return tierA - tierB;
        }

        // If same tier, sort by priority within tier
        const priorityA = getLeaguePriority(a.id);
        const priorityB = getLeaguePriority(b.id);

        return priorityA - priorityB;
      });
    });

    // Sort countries alphabetically, with "International" first
    const sorted = Object.keys(byCountry).sort((a, b) => {
      if (a === 'International') return -1;
      if (b === 'International') return 1;
      return a.localeCompare(b);
    });

    return {
      leaguesByCountry: byCountry,
      sortedCountries: sorted
    };
  }, [simpleLeagues, searchQuery]);

  // Auto-expand countries when searching - fixed to prevent infinite re-renders
  React.useEffect(() => {
    if (searchQuery.trim()) {
      // Expand all countries that have matching leagues
      setExpandedCountries(new Set(sortedCountries));
    } else {
      // Collapse all when not searching
      setExpandedCountries(new Set());
    }
  }, [searchQuery, sortedCountries]); // sortedCountries is now memoized, so this won't cause infinite loops

  // Get top leagues with real data from backend (with safety checks) - memoized
  const topLeaguesWithData = useMemo(() => {
    return TOP_LEAGUES.map(topLeague => {
      const backendLeague = simpleLeagues.find(league => league?.id === topLeague.id);
      return backendLeague || topLeague;
    });
  }, [simpleLeagues]);

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={cn(
        'fixed left-0 top-16 z-50 h-[calc(100vh-4rem)] w-64 transform transition-transform duration-200 ease-in-out md:relative md:top-0 md:z-0 md:h-full md:translate-x-0 md:w-64',
        isOpen ? 'translate-x-0' : '-translate-x-full',
        className
      )}>
        <div className="flex h-full flex-col gap-4">
          {/* Top leagues card */}
          <Card className="flex-shrink-0">
            <div className="p-3.5">
              <h3 className="text-sm font-semibold text-muted-foreground uppercase tracking-wide pb-3">
                Top leagues
              </h3>

              <div className="space-y-1">
                {topLeaguesWithData.map((league) => (
                  <Button
                    key={league.id}
                    variant="ghost"
                    className="w-full justify-start gap-3 h-10 px-3"
                    onClick={() => handleLeagueClick(league?.id)}
                  >
                    {/* Real league logo */}
                    <div className="flex h-6 w-6 items-center justify-center rounded overflow-hidden bg-muted">
                      <Image
                        src={league?.logo || getLeagueLogoUrl(league?.id || 0)}
                        alt={`${league?.name || 'League'} logo`}
                        width={24}
                        height={24}
                        className="h-6 w-6 object-contain"
                        onError={(e) => {
                          // Fallback to emoji icon if image fails to load
                          e.currentTarget.style.display = 'none';
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            parent.innerHTML = getLeagueIcon(league?.name || 'Unknown');
                            parent.className = 'flex h-6 w-6 items-center justify-center rounded bg-muted text-xs';
                          }
                        }}
                      />
                    </div>
                    <span className="text-sm">{league?.name || 'Unknown League'}</span>
                  </Button>
                ))}
              </div>
            </div>
          </Card>

          {/* All leagues card - separate container */}
          <Card className="flex-1">
            <div className="flex flex-col h-full">
              {/* Header with toggle */}
              <div className="p-3.5 border-b">
                <Button
                  variant="ghost"
                  className="w-full justify-between h-10 px-3"
                  onClick={() => setIsAllLeaguesExpanded(!isAllLeaguesExpanded)}
                >
                  <span className="text-sm font-semibold text-muted-foreground uppercase tracking-wide">
                    All leagues
                  </span>
                  {isAllLeaguesExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </Button>
              </div>

              {/* Expandable content */}
              {isAllLeaguesExpanded && (
                <div className="flex flex-col flex-1 overflow-hidden">
                  {/* Filter bar */}
                  <div className="p-3.5 border-b">
                    <div className="relative">
                      <Filter className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                      <Input
                        type="search"
                        placeholder="Filter"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="pl-10 h-9 bg-muted/50"
                      />
                    </div>
                  </div>

                  {/* Leagues list grouped by country */}
                  <div className="flex-1 overflow-y-auto p-3.5">
                    {isLoadingLeagues ? (
                      <div className="text-sm text-muted-foreground py-4 text-center">
                        Loading leagues...
                      </div>
                    ) : leaguesError ? (
                      <div className="text-sm text-status-error py-4 text-center">
                        Failed to load leagues
                      </div>
                    ) : sortedCountries.length === 0 ? (
                      <div className="text-sm text-muted-foreground py-4 text-center">
                        {searchQuery ? 'No leagues found' : 'No leagues available'}
                      </div>
                    ) : (
                      <div className="space-y-2">
                        {sortedCountries.map((country) => {
                          const countryLeagues = leaguesByCountry[country];
                          const isExpanded = expandedCountries.has(country);

                          return (
                            <div key={country} className="space-y-1">
                              {/* Country header */}
                              <Button
                                variant="ghost"
                                className="w-full justify-between h-9 px-3 text-left"
                                onClick={() => toggleCountry(country)}
                              >
                                <div className="flex items-center gap-3">
                                  {/* Country flag */}
                                  <div className="flex h-5 w-5 items-center justify-center rounded-full overflow-hidden bg-muted">
                                    {countryLeagues[0]?.flag ? (
                                      <Image
                                        src={countryLeagues[0].flag}
                                        alt={`${country} flag`}
                                        width={20}
                                        height={20}
                                        className="h-5 w-5 object-cover rounded-full"
                                        onError={(e) => {
                                          e.currentTarget.style.display = 'none';
                                          const parent = e.currentTarget.parentElement;
                                          if (parent) {
                                            parent.innerHTML = '🌍';
                                            parent.className = 'flex h-5 w-5 items-center justify-center rounded-full bg-muted text-xs';
                                          }
                                        }}
                                      />
                                    ) : (
                                      <span className="text-xs">🌍</span>
                                    )}
                                  </div>
                                  <span className="text-sm font-medium">{country}</span>
                                </div>
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>

                              {/* Country leagues */}
                              {isExpanded && (
                                <div className="ml-8 space-y-1">
                                  {countryLeagues.map((league) => (
                                    <Button
                                      key={league.id}
                                      variant="ghost"
                                      className="w-full justify-start gap-3 h-9 px-3"
                                      onClick={() => handleLeagueClick(league?.id)}
                                    >
                                      {/* League logo */}
                                      <div className="flex h-4 w-4 items-center justify-center rounded overflow-hidden bg-muted">
                                        <Image
                                          src={league?.logo || getLeagueLogoUrl(league?.id || 0)}
                                          alt={`${league?.name || 'League'} logo`}
                                          width={16}
                                          height={16}
                                          className="h-4 w-4 object-contain"
                                          onError={(e) => {
                                            e.currentTarget.style.display = 'none';
                                            const parent = e.currentTarget.parentElement;
                                            if (parent) {
                                              parent.innerHTML = '⚽';
                                              parent.className = 'flex h-4 w-4 items-center justify-center rounded bg-muted text-xs';
                                            }
                                          }}
                                        />
                                      </div>
                                      <span className="text-sm">{league?.name || 'Unknown League'}</span>
                                    </Button>
                                  ))}
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      </aside>
    </>
  );
}

// Helper function to get league icons (simplified for now)
function getLeagueIcon(leagueName: string): string {
  const iconMap: Record<string, string> = {
    'Premier League': '⚽',
    'Championship': '🏆',
    'Champions League': '🌟',
    'FA Cup': '🏆',
    'Europa League': '🌍',
    'FIFA World Cup': '🌎',
    'League One': '1️⃣',
    'LaLiga': '🇪🇸',
    'League Two': '2️⃣',
    'EFL Cup': '🏆',
  };
  
  return iconMap[leagueName] || '⚽';
}
