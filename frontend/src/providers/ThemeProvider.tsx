'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { THEME_CONFIG } from '@/lib/constants';

interface ThemeProviderProps {
  children: React.ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={THEME_CONFIG.defaultTheme}
      storageKey={THEME_CONFIG.storageKey}
      enableSystem
      disableTransitionOnChange
    >
      {children}
    </NextThemesProvider>
  );
}
