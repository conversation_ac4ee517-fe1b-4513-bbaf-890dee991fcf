'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useRef, useCallback } from 'react';
import { Socket } from 'socket.io-client';
import socketManager, { ChatMessage } from '@/lib/socket';
import { SocketFixtureUpdate, Fixture } from '@/lib/types';
import { allMockLiveFixtures } from '@/lib/mockData';
import { useAuth } from '@/contexts/AuthContext';

// Chat message type is now imported from socket.ts

// Socket event types
interface GoalEvent {
  fixtureId: number;
  team: string;
  player: string;
  time: number;
}

interface RedCardEvent {
  fixtureId: number;
  team: string;
  player: string;
  time: number;
}

interface StatusChangeEvent {
  fixtureId: number;
  status: string;
  elapsed?: number;
}

interface SocketContextType {
  fixtureSocket: Socket | null;
  chatSocket: Socket | null;
  isConnected: boolean;
  subscribeToLiveMatches: () => void;
  subscribeToFixture: (fixtureId: number) => void;
  subscribeToLeague: (leagueId: number) => void;
  unsubscribeFromFixture: (fixtureId: number) => void;
  onFixtureUpdate: (callback: (data: SocketFixtureUpdate) => void) => () => void;
  onLiveFixturesUpdate: (callback: (fixtures: Fixture[]) => void) => () => void;
  onGoalScored: (callback: (data: GoalEvent) => void) => () => void;
  onRedCard: (callback: (data: RedCardEvent) => void) => () => void;
  onStatusChange: (callback: (data: StatusChangeEvent) => void) => () => void;
  // Chat functions
  initializeChatSocket: () => void;
  joinFixtureChat: (fixtureId: number) => void;
  sendMessage: (content: string) => void;
  onMessage: (callback: (message: ChatMessage) => void) => () => void;
}

export const SocketContext = createContext<SocketContextType | undefined>(undefined);

export function useSocket() {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
}

interface SocketProviderProps {
  children: ReactNode;
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [isConnected, setIsConnected] = useState(false);
  const [fixtureSocket, setFixtureSocket] = useState<Socket | null>(null);
  const [chatSocket, setChatSocket] = useState<Socket | null>(null);
  const [useMockData, setUseMockData] = useState(false);
  const mockUpdateInterval = useRef<NodeJS.Timeout | null>(null);
  const { token, isAuthenticated } = useAuth();

  // Store active intervals to prevent memory leaks
  const activeIntervals = useRef<Set<NodeJS.Timeout>>(new Set());

  useEffect(() => {
    // Initialize fixture socket on mount
    const socket = socketManager.initializeFixtureSocket();
    setFixtureSocket(socket);

    // Capture the current intervals reference for cleanup
    const currentIntervals = activeIntervals.current;

    // Listen for connection status changes
    socket.on('connect', () => {
      setIsConnected(true);
      setUseMockData(false);
      // Clear mock data interval if real connection is established
      if (mockUpdateInterval.current) {
        clearInterval(mockUpdateInterval.current);
        mockUpdateInterval.current = null;
      }
    });

    socket.on('disconnect', () => {
      setIsConnected(false);
      setUseMockData(true);
    });

    socket.on('connect_error', () => {
      setIsConnected(false);
      setUseMockData(true);
    });

    // Cleanup on unmount
    return () => {
      socketManager.removeAllListeners();
      socketManager.disconnect();
      if (mockUpdateInterval.current) {
        clearInterval(mockUpdateInterval.current);
      }
      // Clear all active intervals to prevent memory leaks
      currentIntervals.forEach(interval => clearInterval(interval));
      currentIntervals.clear();
    };
  }, []);

  // Helper function to manage intervals and prevent memory leaks
  const createManagedInterval = useCallback((callback: () => void, delay: number): (() => void) => {
    const interval = setInterval(callback, delay);
    activeIntervals.current.add(interval);

    return () => {
      clearInterval(interval);
      activeIntervals.current.delete(interval);
    };
  }, []);

  const contextValue: SocketContextType = {
    fixtureSocket,
    chatSocket,
    isConnected,
    subscribeToLiveMatches: () => socketManager.subscribeToLiveMatches(),
    subscribeToFixture: (fixtureId: number) => socketManager.subscribeToFixture(fixtureId),
    subscribeToLeague: (leagueId: number) => socketManager.subscribeToLeague(leagueId),
    unsubscribeFromFixture: (fixtureId: number) => socketManager.unsubscribeFromFixture(fixtureId),
    onFixtureUpdate: (callback: (data: SocketFixtureUpdate) => void) => {
       if (useMockData) {
         // Simulate random fixture updates with proper cleanup
         return createManagedInterval(() => {
           const randomFixture = allMockLiveFixtures[Math.floor(Math.random() * allMockLiveFixtures.length)];
           const mockUpdate: SocketFixtureUpdate = {
             fixtureId: randomFixture.fixture.id,
             fixture: randomFixture,
             type: 'fixture-update'
           };
           callback(mockUpdate);
         }, 15000); // Update every 15 seconds
       } else {
         return socketManager.onFixtureUpdate(callback);
       }
     },
    onLiveFixturesUpdate: (callback: (fixtures: Fixture[]) => void) => {
      if (useMockData) {
        // Immediately send mock data
        callback(allMockLiveFixtures);

        // Set up interval for mock updates with proper cleanup
        return createManagedInterval(() => {
          callback(allMockLiveFixtures);
        }, 30000); // Update every 30 seconds
      } else {
        return socketManager.onLiveFixturesUpdate(callback);
      }
    },
    onGoalScored: (callback: (data: GoalEvent) => void) => {
      if (useMockData) {
        // Simulate random goals with proper cleanup
        return createManagedInterval(() => {
          if (Math.random() < 0.1) { // 10% chance every interval
            const mockGoal: GoalEvent = {
              fixtureId: allMockLiveFixtures[Math.floor(Math.random() * allMockLiveFixtures.length)].fixture.id,
              team: Math.random() > 0.5 ? 'home' : 'away',
              player: 'Mock Player',
              time: Math.floor(Math.random() * 90) + 1
            };
            callback(mockGoal);
          }
        }, 20000); // Check every 20 seconds
      } else {
        return socketManager.onGoalScored(callback);
      }
    },
    onRedCard: (callback: (data: RedCardEvent) => void) => {
      if (useMockData) {
        // Simulate random red cards (less frequent) with proper cleanup
        return createManagedInterval(() => {
          if (Math.random() < 0.02) { // 2% chance every interval
            const mockRedCard: RedCardEvent = {
              fixtureId: allMockLiveFixtures[Math.floor(Math.random() * allMockLiveFixtures.length)].fixture.id,
              team: Math.random() > 0.5 ? 'home' : 'away',
              player: 'Mock Player',
              time: Math.floor(Math.random() * 90) + 1
            };
            callback(mockRedCard);
          }
        }, 25000); // Check every 25 seconds
      } else {
        return socketManager.onRedCard(callback);
      }
    },
    onStatusChange: (callback: (data: StatusChangeEvent) => void) => {
      if (useMockData) {
        // Simulate status changes with proper cleanup
        return createManagedInterval(() => {
          if (Math.random() < 0.05) { // 5% chance every interval
            const statuses = ['1H', '2H', 'HT', 'FT'];
            const mockStatusChange: StatusChangeEvent = {
              fixtureId: allMockLiveFixtures[Math.floor(Math.random() * allMockLiveFixtures.length)].fixture.id,
              status: statuses[Math.floor(Math.random() * statuses.length)],
              elapsed: Math.floor(Math.random() * 90) + 1
            };
            callback(mockStatusChange);
          }
        }, 30000); // Check every 30 seconds
      } else {
        return socketManager.onStatusChange(callback);
      }
    },
    // Chat functions
    initializeChatSocket: () => {
      if (isAuthenticated && token && !chatSocket) {
        const socket = socketManager.initializeChatSocket(token);
        setChatSocket(socket);
      }
    },
    joinFixtureChat: (fixtureId: number) => {
      if (chatSocket) {
        socketManager.joinFixtureChat(fixtureId);
      }
    },
    sendMessage: (content: string) => {
      if (chatSocket) {
        socketManager.sendMessage(content);
      }
    },
    onMessage: (callback: (message: ChatMessage) => void) => {
      if (chatSocket) {
        socketManager.onMessage(callback);
        return () => {
          chatSocket.off('new-message', callback);
        };
      }
      return () => {};
    },
  };

  return (
    <SocketContext.Provider value={contextValue}>
      {children}
    </SocketContext.Provider>
  );
}
