// Mock data for the fixtures page

// Match status and betting related enums
export enum MatchStatus {
  NOT_STARTED = 'NS',
  FIRST_HALF = '1H', 
  HALF_TIME = 'HT',
  SECOND_HALF = '2H',
  FULL_TIME = 'FT',
  EXTRA_TIME = 'ET',
  PENALTIES = 'PEN'
}

export enum BettingOutcome {
  HOME_WIN = '1',
  DRAW = 'X', 
  AWAY_WIN = '2'
}

export enum MatchResult {
  WIN = 'W',
  DRAW = 'D',
  LOSS = 'L'
}

export const formatMatchTime = (date: Date): string => {
  return date.toLocaleTimeString('en-GB', { 
    hour: '2-digit', 
    minute: '2-digit',
    hour12: false 
  });
};

export const formatMatchDate = (date: Date): string => {
  return date.toLocaleDateString('en-GB', {
    weekday: 'short',
    day: 'numeric',
    month: 'long'
  });
};

export const formatCountdown = (targetDate: Date): string => {
  const now = new Date();
  const diff = targetDate.getTime() - now.getTime();
  
  if (diff <= 0) return '00:00:00';
  
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

export const formatOdds = (odds: number): string => {
  return odds.toFixed(2);
};

export const formatScore = (homeScore: number | null, awayScore: number | null): string => {
  if (homeScore === null || awayScore === null) return '-';
  return `${homeScore} - ${awayScore}`;
};

// Mock data for the fixtures page
export const mockStore = {
  user: {
    favoriteTeams: [47, 40], // Tottenham, Burnley
    favoriteLeagues: [39] // Premier League
  },
  theme: 'dark' as const
};

export const mockQuery = {
  featuredMatch: {
    _id: 1,
    fixture: {
      id: 1,
      date: '2025-08-16T14:00:00Z',
      timestamp: 1723824000,
      referee: 'Michael Oliver',
      venue: {
        name: 'Tottenham Hotspur Stadium',
        city: 'London'
      },
      status: {
        short: 'NS' as const,
        long: 'Not Started',
        elapsed: null
      },
      timezone: 'UTC',
      periods: {}
    },
    league: {
      id: 39,
      name: 'Premier League',
      country: 'England',
      logo: 'https://media.api-sports.io/football/leagues/39.png',
      flag: 'https://media.api-sports.io/flags/gb.svg',
      season: 2025,
      round: 'Round 1'
    },
    teams: {
      home: {
        id: 47,
        name: 'Tottenham Hotspur',
        logo: 'https://media.api-sports.io/football/teams/47.png'
      },
      away: {
        id: 40,
        name: 'Burnley', 
        logo: 'https://media.api-sports.io/football/teams/40.png'
      }
    },
    goals: {
      home: null,
      away: null
    },
    score: {
      halftime: { home: null, away: null },
      fulltime: { home: null, away: null },
      extratime: { home: null, away: null },
      penalty: { home: null, away: null }
    }
  },
  teamForm: [
    { team: 'Paris Saint-Germain', homeScore: 2, awayScore: 2, opponent: 'Tottenham Hotspur', result: 'D' as const },
    { team: 'Bayern München', homeScore: 4, awayScore: 0, opponent: 'Tottenham Hotspur', result: 'L' as const },
    { team: 'Tottenham Hotspur', homeScore: 1, awayScore: 1, opponent: 'Newcastle United', result: 'D' as const },
    { team: 'Arsenal', homeScore: 0, awayScore: 1, opponent: 'Tottenham Hotspur', result: 'W' as const },
    { team: 'Luton Town', homeScore: 0, awayScore: 0, opponent: 'Tottenham Hotspur', result: 'D' as const },
    { team: 'Burnley', homeScore: 0, awayScore: 1, opponent: 'Lazio', result: 'L' as const },
    { team: 'Stoke City', homeScore: 1, awayScore: 0, opponent: 'Burnley', result: 'L' as const },
    { team: 'Huddersfield Town', homeScore: 0, awayScore: 2, opponent: 'Burnley', result: 'W' as const },
    { team: 'Shrewsbury Town', homeScore: 2, awayScore: 2, opponent: 'Burnley', result: 'D' as const },
    { team: 'Burnley', homeScore: 3, awayScore: 1, opponent: 'Millwall', result: 'W' as const }
  ],
  bettingOdds: {
    homeWin: 1.50,
    draw: 4.33,
    awayWin: 6.50
  },
  leagueStandings: [
    { position: 1, team: 'Aston Villa', points: 0, played: 0 },
    { position: 2, team: 'Newcastle', points: 0, played: 0 },
    { position: 3, team: 'Liverpool', points: 4, played: 2 },
    { position: 4, team: 'Bournemouth', points: 2, played: 1 }
  ],
  upcomingMatches: [
    { 
      homeTeam: 'Brighton', 
      awayTeam: 'Fulham', 
      date: '2025-08-16T14:00:00Z',
      status: 'Today' as const 
    },
    { 
      homeTeam: 'Sunderland', 
      awayTeam: 'West Ham', 
      date: '2025-08-16T14:00:00Z',
      status: 'Today' as const 
    },
    { 
      homeTeam: 'Tottenham', 
      awayTeam: 'Burnley', 
      date: '2025-08-16T14:00:00Z',
      status: 'Today' as const 
    },
    { 
      homeTeam: 'Wolves', 
      awayTeam: 'Man City', 
      date: '2025-08-17T16:30:00Z',
      status: 'Tomorrow' as const 
    },
    { 
      homeTeam: 'Chelsea', 
      awayTeam: 'Crystal Palace', 
      date: '2025-08-18T13:00:00Z',
      status: 'Tomorrow' as const 
    }
  ]
};

export const mockRootProps = {
  initialFilter: 'upcoming' as const,
  selectedDate: '2025-08-16'
};