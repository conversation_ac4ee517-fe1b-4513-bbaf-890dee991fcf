import { TipType } from './types';

export interface TipTypeConfig {
  value: TipType;
  label: string;
  description: string;
  category: 'match' | 'goals' | 'cards' | 'corners' | 'team' | 'score';
  icon: string;
  popular: boolean;
}

export const TIP_TYPE_CONFIGS: TipTypeConfig[] = [
  // Match Result Category
  {
    value: TipType.MATCH_RESULT,
    label: 'Match Result (1X2)',
    description: 'Predict the final result of the match',
    category: 'match',
    icon: '⚽',
    popular: true,
  },
  {
    value: TipType.DOUBLE_CHANCE,
    label: 'Double Chance',
    description: 'Cover two of the three possible outcomes',
    category: 'match',
    icon: '🎯',
    popular: false,
  },
  {
    value: TipType.HALF_TIME_RESULT,
    label: 'Half Time Result',
    description: 'Predict the result at half time',
    category: 'match',
    icon: '⏱️',
    popular: false,
  },

  // Goals Category
  {
    value: TipType.BTTS,
    label: 'Both Teams To Score',
    description: 'Will both teams score at least one goal?',
    category: 'goals',
    icon: '⚽',
    popular: true,
  },
  {
    value: TipType.OVER_UNDER_GOALS,
    label: 'Over/Under Goals',
    description: 'Total goals scored in the match',
    category: 'goals',
    icon: '📊',
    popular: true,
  },


  // Team Performance Category
  {
    value: TipType.FIRST_GOAL,
    label: 'First Goal',
    description: 'Team to score the first goal',
    category: 'team',
    icon: '🥇',
    popular: false,
  },

  // Cards Category
  {
    value: TipType.OVER_UNDER_CARDS,
    label: 'Over/Under Cards',
    description: 'Total cards shown in the match',
    category: 'cards',
    icon: '🟨',
    popular: false,
  },

  // Corners Category
  {
    value: TipType.OVER_UNDER_CORNERS,
    label: 'Over/Under Corners',
    description: 'Total corner kicks in the match',
    category: 'corners',
    icon: '📐',
    popular: false,
  },

  // Score Category
  {
    value: TipType.CORRECT_SCORE,
    label: 'Correct Score',
    description: 'Exact final score of the match',
    category: 'score',
    icon: '🎯',
    popular: false,
  },

  // Advanced Category
  {
    value: TipType.HANDICAP,
    label: 'Handicap',
    description: 'Asian handicap betting',
    category: 'match',
    icon: '⚖️',
    popular: false,
  },
];

export const TIP_CATEGORIES = [
  { key: 'match', label: 'Match Result', icon: '⚽' },
  { key: 'goals', label: 'Goals', icon: '🥅' },
  { key: 'team', label: 'Team Performance', icon: '👥' },
  { key: 'cards', label: 'Cards', icon: '🟨' },
  { key: 'corners', label: 'Corners', icon: '📐' },
  { key: 'score', label: 'Exact Score', icon: '🎯' },
] as const;

// Helper functions
export const getTipTypeConfig = (tipType: TipType): TipTypeConfig | undefined => {
  return TIP_TYPE_CONFIGS.find(config => config.value === tipType);
};

export const getPopularTipTypes = (): TipTypeConfig[] => {
  return TIP_TYPE_CONFIGS.filter(config => config.popular);
};

export const getTipTypesByCategory = (category: string): TipTypeConfig[] => {
  return TIP_TYPE_CONFIGS.filter(config => config.category === category);
};

export const formatTipTypeLabel = (tipType: TipType): string => {
  const config = getTipTypeConfig(tipType);
  return config?.label || tipType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};

// Common tip lines/values
export const COMMON_GOAL_LINES = [0.5, 1.5, 2.5, 3.5, 4.5, 5.5];
export const COMMON_CORNER_LINES = [7.5, 8.5, 9.5, 10.5, 11.5, 12.5];
export const COMMON_CARD_LINES = [2.5, 3.5, 4.5, 5.5, 6.5];
export const COMMON_HANDICAP_VALUES = [-2.5, -2, -1.5, -1, -0.5, 0, 0.5, 1, 1.5, 2, 2.5];

// Validation helpers
export const validateTipDetails = (tipType: TipType, details: Record<string, unknown>): boolean => {
  switch (tipType) {
    case TipType.BTTS:
      return details.bttsValue !== undefined;
    
    case TipType.OVER_UNDER_GOALS:
    case TipType.OVER_UNDER_CORNERS:
    case TipType.OVER_UNDER_CARDS:
      return details.line !== undefined && details.overUnder !== undefined;
    
    case TipType.MATCH_RESULT:
    case TipType.HALF_TIME_RESULT:
      return details.result !== undefined || details.halfTimeResult !== undefined;
    
    case TipType.DOUBLE_CHANCE:
      return details.doubleChance !== undefined;

    case TipType.FIRST_GOAL:
      return details.firstGoalTeam !== undefined;
    
    case TipType.CORRECT_SCORE:
      return details.homeScore !== undefined && details.awayScore !== undefined;
    
    case TipType.HANDICAP:
      return details.handicapValue !== undefined && details.handicapTeam !== undefined;
    
    default:
      return false;
  }
};

export const getMinimumOdds = (): number => 1.01;
export const getMaximumOdds = (): number => 1000;
export const getDefaultStake = (): number => 10;
