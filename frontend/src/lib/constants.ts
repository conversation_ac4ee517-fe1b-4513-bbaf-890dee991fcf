// App configuration
export const APP_CONFIG = {
  name: 'KickoffScore',
  description: 'Live Football Scores & Predictions',
  url: 'https://kickoffpredictions.com',
  api: {
    baseUrl: 'https://api.kickoffpredictions.com',
    key: '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756',
  },
  socket: {
    url: 'https://api.kickoffpredictions.com',
  },
} as const;

// Top leagues configuration (using real API-Sports league IDs and CDN logos)
export const TOP_LEAGUES = [
  {
    id: 39,
    name: 'Premier League',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/39.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
  {
    id: 144,
    name: 'Championship',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/144.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
  {
    id: 2,
    name: 'Champions League',
    country: 'Europe',
    logo: 'https://media.api-sports.io/football/leagues/2.png',
    flag: '🇪🇺',
  },
  {
    id: 45,
    name: 'FA Cup',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/45.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
  {
    id: 3,
    name: 'Europa League',
    country: 'Europe',
    logo: 'https://media.api-sports.io/football/leagues/3.png',
    flag: '🇪🇺',
  },
  {
    id: 1,
    name: 'FIFA World Cup',
    country: 'World',
    logo: 'https://media.api-sports.io/football/leagues/1.png',
    flag: '🌍',
  },
  {
    id: 146,
    name: 'League One',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/146.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
  {
    id: 140,
    name: 'LaLiga',
    country: 'Spain',
    logo: 'https://media.api-sports.io/football/leagues/140.png',
    flag: '🇪🇸',
  },
  {
    id: 147,
    name: 'League Two',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/147.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
  {
    id: 48,
    name: 'EFL Cup',
    country: 'England',
    logo: 'https://media.api-sports.io/football/leagues/48.png',
    flag: '🏴󠁧󠁢󠁥󠁮󠁧󠁿',
  },
] as const;

// Match status constants
export const MATCH_STATUS = {
  NOT_STARTED: 'NS',
  FIRST_HALF: '1H',
  HALFTIME: 'HT',
  SECOND_HALF: '2H',
  EXTRA_TIME: 'ET',
  BREAK_TIME: 'BT',
  PENALTY: 'P',
  FINISHED: 'FT',
  AFTER_EXTRA_TIME: 'AET',
  FINISHED_PENALTY: 'PEN',
  POSTPONED: 'PST',
  CANCELLED: 'CANC',
  SUSPENDED: 'SUSP',
  ABANDONED: 'ABD',
  LIVE: 'LIVE',
} as const;

// Match filter options
export const MATCH_FILTERS = [
  { key: 'all', label: 'All', color: 'bg-blue-600' },
  { key: 'live', label: 'Live', color: 'bg-red-500' },
  { key: 'finished', label: 'Finished', color: 'bg-gray-500' },
  { key: 'upcoming', label: 'Upcoming', color: 'bg-blue-500' },
  { key: 'by-time', label: 'By Time', color: 'bg-green-500' },
] as const;

// Live status indicators
export const LIVE_STATUSES = ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'];
export const FINISHED_STATUSES = ['FT', 'AET', 'PEN'];
export const UPCOMING_STATUSES = ['NS', 'TBD'];

// Theme configuration
export const THEME_CONFIG = {
  defaultTheme: 'dark',
  storageKey: 'kickoffscore-theme',
} as const;

// Navigation links for footer
export const FOOTER_LINKS = {
  important: [
    { label: 'Terms and Conditions', href: '/terms' },
    { label: 'Betmines Privacy Policy', href: '/privacy' },
    { label: 'Contact us', href: '/contact' },
    { label: 'FAQ', href: '/faq' },
  ],
  quickLinks: [
    { label: 'Matches', href: '/matches' },
    { label: 'Leagues', href: '/leagues' },
    { label: 'Match Previews', href: '/previews' },
    { label: 'Premium Services', href: '/premium' },
  ],
  predictions: [
    { label: 'Predictions for Today', href: '/predictions/today' },
    { label: 'Predictions for Tomorrow', href: '/predictions/tomorrow' },
    { label: 'Predictions for Yesterday', href: '/predictions/yesterday' },
  ],
  features: [
    { label: 'Advanced Filters', href: '/filters' },
    { label: 'BetMines Machine', href: '/machine' },
    { label: 'Leagues Stats', href: '/stats' },
    { label: 'Daily Bets', href: '/bets' },
    { label: 'Favourites', href: '/favourites' },
  ],
} as const;

// Social media links
export const SOCIAL_LINKS = [
  { platform: 'Facebook', url: 'https://facebook.com/kickoffscore', icon: 'facebook' },
  { platform: 'Instagram', url: 'https://instagram.com/kickoffscore', icon: 'instagram' },
  { platform: 'Telegram', url: 'https://t.me/kickoffscore', icon: 'telegram' },
  { platform: 'Discord', url: 'https://discord.gg/kickoffscore', icon: 'discord' },
] as const;

// Betting partners (placeholder logos)
export const BETTING_PARTNERS = [
  { name: 'BET365', logo: '/partners/bet365.png', url: 'https://bet365.com' },
  { name: 'Betmines', logo: '/partners/betmines.png', url: 'https://betmines.com' },
  { name: 'Gambling Therapy', logo: '/partners/gambling-therapy.png', url: 'https://gamblingtherapy.org' },
  { name: 'GamStop', logo: '/partners/gamstop.png', url: 'https://gamstop.co.uk' },
] as const;

// App store links
export const APP_STORE_LINKS = {
  ios: 'https://apps.apple.com/app/kickoffscore',
  android: 'https://play.google.com/store/apps/details?id=com.kickoffscore',
} as const;

// Query keys for React Query
export const QUERY_KEYS = {
  fixtures: 'fixtures',
  liveFixtures: 'live-fixtures',
  fixturesByDate: 'fixtures-by-date',
  fixturesByLeague: 'fixtures-by-league',
  leagues: 'leagues',
  teams: 'teams',
  standings: 'standings',
  search: 'search',
  odds: 'odds',
  predictions: 'predictions',
  headToHead: 'head-to-head',
  fixtureStats: 'fixture-stats',
  voteStats: 'vote-stats',
  userVote: 'user-vote',
  batchVoteStats: 'batch-vote-stats',
  userVoteCount: 'user-vote-count',
  userVotesByDate: 'user-votes-by-date',
  allUserVotes: 'all-user-votes',
  venue: 'venue',
} as const;

// Cache times (in milliseconds)
export const CACHE_TIMES = {
  fixtures: 15 * 1000, // 15 seconds for live data
  leagues: 60 * 60 * 1000, // 1 hour
  teams: 60 * 60 * 1000, // 1 hour
  standings: 30 * 60 * 1000, // 30 minutes
  static: 24 * 60 * 60 * 1000, // 24 hours
} as const;

// Responsive breakpoints
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;
