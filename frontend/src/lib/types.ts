// Core API types based on backend documentation

export interface Fixture {
  _id: number;
  date: Date;
  lastUpdated: Date;
  fixture: {
    id: number;
    referee?: string;
    timezone: string;
    date: string;
    timestamp: number;
    periods: {
      first?: number;
      second?: number;
    };
    venue?: {
      id?: number;
      name?: string;
      city?: string;
      capacity?: number;
      surface?: string;
    };
    status: {
      long: string;
      short: string; // "NS", "1H", "HT", "2H", "FT", "AET", "PEN", etc.
      elapsed?: number;
      extra?: number; // Additional stoppage/injury time in minutes
    };
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
    flag?: string;
    season: number;
    round: string;
  };
  teams: {
    home: Team;
    away: Team;
  };
  goals: {
    home?: number;
    away?: number;
  };
  score: {
    halftime: { home?: number; away?: number };
    fulltime: { home?: number; away?: number };
    extratime: { home?: number; away?: number };
    penalty: { home?: number; away?: number };
  };
  events?: MatchEvent[];
  lineups?: Lineup[];
  statistics?: MatchStatistic[];
  teamRatings?: TeamRatings;
}

export interface Team {
  id: number;
  name: string;
  logo?: string;
  winner?: boolean;
}

export interface Venue {
  _id: string;
  address: string;
  capacity: number;
  city: string;
  country: string;
  image?: string;
  name: string;
  surface: string;
}

// League typesstructure (matches MongoDB document)
export interface League {
  _id: number;
  apiId: number;
  league: {
    id: number;
    name: string;
    type: string;
    logo: string;
  };
  country: {
    name: string;
    code: string | null;
    flag: string | null;
  };
  seasons: Array<{
    year: number;
    start: string;
    end: string;
    current: boolean;
  }>;
  lastUpdated: Date;
}

// Simplified League interface for frontend use
export interface SimpleLeague {
  id: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  type?: string;
}

export interface MatchEvent {
  time: {
    elapsed: number;
    extra?: number;
  };
  team: {
    id: number;
    name: string;
    logo?: string;
  };
  player?: {
    id?: number;
    name?: string;
  };
  assist?: {
    id?: number;
    name?: string;
  };
  type: string; // "Goal", "Card", "Subst", "Var"
  detail: string; // "Normal Goal", "Yellow Card", etc.
  comments?: string;
}

export interface Lineup {
  team: {
    id: number;
    name: string;
    logo?: string;
    colors?: {
      player?: {
        primary?: string;
        number?: string;
        border?: string;
      };
      goalkeeper?: {
        primary?: string;
        number?: string;
        border?: string;
      };
    };
  };
  coach?: {
    id?: number;
    name?: string;
    photo?: string;
  };
  formation?: string;
  startXI: LineupPlayer[];
  substitutes: LineupPlayer[];
}

export interface LineupPlayer {
  player: {
    id: number;
    name: string;
    number?: number;
    pos?: string;
    grid?: string;
  };
}

export interface MatchStatistic {
  team: {
    id: number;
    name: string;
    logo?: string;
  };
  statistics: {
    type: string;
    value: number | string | null;
  }[];
}

export interface Standing {
  rank: number;
  team: Team;
  points: number;
  goalsDiff: number;
  group: string;
  form: string;
  status: string;
  description?: string;
  all: {
    played: number;
    win: number;
    draw: number;
    lose: number;
    goals: {
      for: number;
      against: number;
    };
  };
  home?: {
    played: number;
    win: number;
    draw: number;
    lose: number;
    goals: {
      for: number;
      against: number;
    };
  };
  away?: {
    played: number;
    win: number;
    draw: number;
    lose: number;
    goals: {
      for: number;
      against: number;
    };
  };
}

// API response structure for standings
export interface StandingsResponse {
  league: {
    id: number;
    name: string;
    season: number;
  };
  standings: Standing[][];
}

// Odds types
export interface Bookmaker {
  id: number;
  name: string;
  logo?: string;
}

export interface Bet {
  id: number;
  name: string;
  values: BetValue[];
}

export interface BetValue {
  value: string;
  odd: string;
}

export interface Odds {
  fixture: {
    id: number;
    timezone: string;
    date: string;
    timestamp: number;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo: string;
    flag: string;
    season: number;
  };
  teams: {
    home: Team;
    away: Team;
  };
  status: {
    short: string;
    long: string;
  };
  bookmakers: {
    id: number;
    name: string;
    bets: Bet[];
  }[];
}

// Predictions types
export interface Prediction {
  fixture: {
    id: number;
    date: string;
    timestamp: number;
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo: string;
    flag: string;
    season: number;
  };
  teams: {
    home: Team;
    away: Team;
  };
  predictions: {
    winner: {
      id: number | null;
      name: string;
      comment: string;
    };
    win_or_draw: boolean;
    under_over: string | null;
    goals: {
      home: string;
      away: string;
    };
    advice: string;
    percent: {
      home: string;
      draw: string;
      away: string;
    };
  };
  comparison: {
    form: {
      home: string;
      away: string;
    };
    att: {
      home: string;
      away: string;
    };
    def: {
      home: string;
      away: string;
    };
    poisson_distribution: {
      home: string;
      away: string;
    };
    h2h: {
      home: string;
      away: string;
    };
    goals: {
      home: string;
      away: string;
    };
    total: {
      home: string;
      away: string;
    };
  };
}

// Head-to-Head types
export interface HeadToHead {
  fixture: {
    id: number;
    date: string;
    timestamp: number;
    status: {
      short: string;
      long: string;
    };
  };
  league: {
    id: number;
    name: string;
    country: string;
    logo: string;
    season: number;
  };
  teams: {
    home: Team;
    away: Team;
  };
  goals: {
    home: number | null;
    away: number | null;
  };
  score: {
    halftime: {
      home: number | null;
      away: number | null;
    };
    fulltime: {
      home: number | null;
      away: number | null;
    };
    extratime: {
      home: number | null;
      away: number | null;
    };
    penalty: {
      home: number | null;
      away: number | null;
    };
  };
}

// Statistics types
export interface FixtureStatistics {
  team: Team;
  statistics: {
    type: string;
    value: number | string | null;
  }[];
}

// Team Statistics types
export interface TeamStatistics {
  league: {
    id: number;
    name: string;
    country: string;
    logo?: string;
    flag?: string;
    season: number;
  };
  team: Team;
  form: string; // e.g., "WWDLW"
  fixtures: {
    played: {
      home: number;
      away: number;
      total: number;
    };
    wins: {
      home: number;
      away: number;
      total: number;
    };
    draws: {
      home: number;
      away: number;
      total: number;
    };
    loses: {
      home: number;
      away: number;
      total: number;
    };
  };
  goals: {
    for: {
      total: {
        home: number;
        away: number;
        total: number;
      };
      average: {
        home: string;
        away: string;
        total: string;
      };
    };
    against: {
      total: {
        home: number;
        away: number;
        total: number;
      };
      average: {
        home: string;
        away: string;
        total: string;
      };
    };
  };
  biggest: {
    streak: {
      wins: number;
      draws: number;
      loses: number;
    };
    wins: {
      home?: string;
      away?: string;
    };
    loses: {
      home?: string;
      away?: string;
    };
    goals: {
      for: {
        home: number;
        away: number;
      };
      against: {
        home: number;
        away: number;
      };
    };
  };
  clean_sheet: {
    home: number;
    away: number;
    total: number;
  };
  failed_to_score: {
    home: number;
    away: number;
    total: number;
  };
}

// Form data types
export interface FormMatch {
  result: 'W' | 'D' | 'L';
  fixture: {
    id: number;
    date: string;
  };
  teams: {
    home: Team;
    away: Team;
  };
  goals: {
    home: number;
    away: number;
  };
  league?: {
    name: string;
    logo?: string;
  };
}

export interface TeamForm {
  teamId: number;
  season: number;
  form: string; // e.g., "WWDLW"
  matches: FormMatch[];
}

// Team Rating types
export interface PlayerRatingInfo {
  playerId: number;
  name: string;
  rating: number;
  substitute: boolean;
  minutes: number | null;
  position: string;
}

export interface TeamRatingData {
  rating: number;
  playersCount: number;
  ratedPlayersCount: number;
  playerRatings: PlayerRatingInfo[];
}

export interface TeamRatings {
  home: TeamRatingData;
  away: TeamRatingData;
}

// Socket event types
export interface SocketFixtureUpdate {
  fixtureId: number;
  fixture: Fixture;
  type: 'fixture-update' | 'goal-scored' | 'red-card' | 'status-change';
}

export interface SocketTeamRatingUpdate {
  fixtureId: number;
  teamRatings: TeamRatings;
  type: 'team-rating-update';
}

// Filter types for UI
export type MatchFilter = 'all' | 'live' | 'finished' | 'upcoming' | 'by-time';

export interface MatchFilters {
  filter: MatchFilter;
  date?: string;
  league?: number;
  team?: number;
}

// News types (placeholder for future implementation)
export interface NewsItem {
  id: string;
  title: string;
  summary: string;
  image?: string;
  publishedAt: string;
  source: string;
  url: string;
}

// User types (re-export from auth types for consistency)
export interface User {
  _id: string;
  email: string;
  name: string;
  profileImage?: string;
  favoriteTeams: number[];
  favoriteLeagues: number[];
  favoritePlayers: number[];
  providers: {
    email?: { verified: boolean };
    google?: {
      id: string;
      email: string;
      verified: boolean;
    };
  };
  notificationPreferences: {
    goals: boolean;
    redCards: boolean;
    matchStart: boolean;
    matchEnd: boolean;
    favoriteTeams: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// Constants for match status
export const MATCH_STATUS = {
  NOT_STARTED: 'NS',
  FIRST_HALF: '1H',
  HALFTIME: 'HT',
  SECOND_HALF: '2H',
  EXTRA_TIME: 'ET',
  BREAK_TIME: 'BT',
  PENALTY: 'P',
  FINISHED: 'FT',
  AFTER_EXTRA_TIME: 'AET',
  FINISHED_PENALTY: 'PEN',
  POSTPONED: 'PST',
  CANCELLED: 'CANC',
  SUSPENDED: 'SUSP',
  ABANDONED: 'ABD',
} as const;

export type MatchStatus = typeof MATCH_STATUS[keyof typeof MATCH_STATUS];

// Helper functions for match status
export const isLiveMatch = (status: string): boolean => {
  return ['1H', '2H', 'HT', 'ET', 'BT', 'P', 'LIVE'].includes(status);
};

export const isFinishedMatch = (status: string): boolean => {
  return ['FT', 'AET', 'PEN'].includes(status);
};

export const isUpcomingMatch = (status: string): boolean => {
  return ['NS', 'TBD', 'PST', 'CANC', 'SUSP', 'ABD'].includes(status) || status === 'NS';
};

export const getMatchStatusDisplay = (status: string): string => {
  switch (status) {
    case MATCH_STATUS.NOT_STARTED:
      return 'Not Started';
    case MATCH_STATUS.FIRST_HALF:
      return '1st Half';
    case MATCH_STATUS.HALFTIME:
      return 'Half Time';
    case MATCH_STATUS.SECOND_HALF:
      return '2nd Half';
    case MATCH_STATUS.EXTRA_TIME:
      return 'Extra Time';
    case MATCH_STATUS.BREAK_TIME:
      return 'Break Time';
    case MATCH_STATUS.PENALTY:
      return 'Penalties';
    case MATCH_STATUS.FINISHED:
      return 'Full Time';
    case MATCH_STATUS.AFTER_EXTRA_TIME:
      return 'After Extra Time';
    case MATCH_STATUS.FINISHED_PENALTY:
      return 'Finished (Penalties)';
    case MATCH_STATUS.POSTPONED:
      return 'Postponed';
    case MATCH_STATUS.CANCELLED:
      return 'Cancelled';
    case MATCH_STATUS.SUSPENDED:
      return 'Suspended';
    case MATCH_STATUS.ABANDONED:
      return 'Abandoned';
    default:
      return status;
  }
};

// Voting System Types
export enum VoteCategory {
  MATCH_OUTCOME = 'match_outcome',
  OVER_UNDER = 'over_under',
  BTTS = 'btts'
}

export enum VoteOption {
  // Match Outcome options
  HOME_WIN = 'home',
  DRAW = 'draw',
  AWAY_WIN = 'away',

  // Over/Under options
  OVER_25 = 'over_2_5',
  UNDER_25 = 'under_2_5',

  // Both Teams to Score options
  BTTS_YES = 'btts_yes',
  BTTS_NO = 'btts_no'
}

export interface FixtureVote {
  fixtureId: number;
  vote: VoteOption;
  userId?: string;
  userType: 'authenticated' | 'guest';
  createdAt: Date;
}

// Updated to match the actual backend response structure
export interface VoteStats {
  match_outcome: {
    homeVotes: number;
    drawVotes: number;
    awayVotes: number;
    totalVotes: number;
  };
  over_under: {
    over25Votes: number;
    under25Votes: number;
    totalVotes: number;
  };
  btts: {
    yesVotes: number;
    noVotes: number;
    totalVotes: number;
  };
}

export interface UserVote {
  fixtureId: number;
  category: VoteCategory;
  vote: VoteOption;
  createdAt: Date;
}

export interface UserVotesByCategory {
  match_outcome?: {
    vote: VoteOption;
    createdAt: Date;
    updatedAt: Date;
  };
  over_under?: {
    vote: VoteOption;
    createdAt: Date;
    updatedAt: Date;
  };
  btts?: {
    vote: VoteOption;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface VoteResponse {
  message: string;
  category: VoteCategory;
  vote: VoteOption;
  stats: VoteStats;
  userType: 'authenticated' | 'guest';
}

export interface BatchVoteStats {
  [fixtureId: number]: VoteStats;
}

// Tips types
export enum TipType {
  BTTS = 'btts', // Both Teams To Score
  OVER_UNDER_GOALS = 'over_under_goals', // Over/Under total goals
  OVER_UNDER_CORNERS = 'over_under_corners', // Over/Under total corners
  OVER_UNDER_CARDS = 'over_under_cards', // Over/Under total cards
  MATCH_RESULT = 'match_result', // 1X2 (Home/Draw/Away)
  DOUBLE_CHANCE = 'double_chance', // 1X, X2, 12
  FIRST_GOAL = 'first_goal', // Team to score first goal
  HANDICAP = 'handicap', // Asian handicap
  CORRECT_SCORE = 'correct_score', // Exact score prediction
  HALF_TIME_RESULT = 'half_time_result' // Half time result
}

export enum TipStatus {
  PENDING = 'pending',
  WON = 'won',
  LOST = 'lost',
  VOID = 'void',
  PUSH = 'push'
}

export interface TipDetails {
  // Market selection
  market?: 'fulltime' | 'firsthalf' | 'secondhalf';

  // For BTTS
  bttsValue?: 'yes' | 'no';

  // For Over/Under types
  line?: number; // e.g., 2.5, 9.5, 4.5
  overUnder?: 'over' | 'under';

  // For Match Result
  result?: 'home' | 'draw' | 'away';

  // For Double Chance
  doubleChance?: '1X' | 'X2' | '12';

  // For First Goal
  firstGoalTeam?: 'home' | 'away';

  // For Handicap
  handicapValue?: number; // e.g., -1.5, +0.5
  handicapTeam?: 'home' | 'away';

  // For Correct Score
  homeScore?: number;
  awayScore?: number;

  // For Half Time Result
  halfTimeResult?: 'home' | 'draw' | 'away';
}

export interface Tip {
  _id?: string;
  userId: string;
  fixtureId: number;
  tipType: TipType;
  details: TipDetails;
  odds: number;
  stake?: number;
  description?: string;
  status: TipStatus;
  result?: {
    actualValue?: unknown;
    calculatedAt: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  isPublic: boolean;
  likes?: number;
  comments?: number;
}

export interface TipWithDetails extends Tip {
  user: {
    _id: string;
    name?: string;
    profileImage?: string;
  };
  fixture: {
    _id: number;
    teams: {
      home: { name: string; logo?: string };
      away: { name: string; logo?: string };
    };
    league: {
      name: string;
      country: string;
      logo?: string;
    };
    fixture: {
      date: string;
      status: { short: string; long: string };
    };
  };
}

export interface TipsterStats {
  userId: string;
  userName?: string; // Display name
  username?: string; // URL-friendly username
  totalTips: number;
  wonTips: number;
  lostTips: number;
  voidTips: number;
  pushTips: number;
  totalStake: number;
  totalReturn: number;
  profit: number;
  yield: number;
  hitRate: number;
  averageOdds: number;
  longestWinStreak: number;
  longestLoseStreak: number;
  currentStreak: number;
  currentStreakType: 'win' | 'lose' | 'none';
  lastUpdated: Date;
}

// Best Odds types
export interface BestOddsResult {
  fixtureId: number;
  betType: TipType;
  selection: string;
  bestOdds: {
    value: string;
    decimal: number;
    bookmaker: {
      id: number;
      name: string;
    };
    lastUpdated: string;
  } | null;
  allOdds: Array<{
    value: string;
    decimal: number;
    bookmaker: {
      id: number;
      name: string;
    };
  }>;
  available: boolean;
  error?: string;
}

export interface OddsComparison {
  fixtureId: number;
  betType: TipType;
  selections: Record<string, {
    bestOdds: {
      value: string;
      decimal: number;
      bookmaker: {
        id: number;
        name: string;
      };
    } | null;
    allOdds: Array<{
      value: string;
      decimal: number;
      bookmaker: {
        id: number;
        name: string;
      };
    }>;
  }>;
  lastUpdated: string;
}
