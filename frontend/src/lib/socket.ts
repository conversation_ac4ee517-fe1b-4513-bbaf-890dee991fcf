import { io, Socket } from 'socket.io-client';
import { SocketFixtureUpdate, Fixture } from './types';

// Socket event types
interface GoalEvent {
  fixtureId: number;
  team: string;
  player: string;
  time: number;
}

interface RedCardEvent {
  fixtureId: number;
  team: string;
  player: string;
  time: number;
}

interface StatusChangeEvent {
  fixtureId: number;
  status: string;
  elapsed?: number;
}

export interface ChatMessage {
  _id: string;
  fixtureId: number;
  userId: string;
  userName: string;
  content: string;
  createdAt: Date;
}

const SOCKET_URL = 'https://api.kickoffpredictions.com';

class SocketManager {
  private fixtureSocket: Socket | null = null;
  private chatSocket: Socket | null = null;
  private isConnected = false;

  // Initialize fixture socket for live updates
  initializeFixtureSocket(): Socket {
    if (this.fixtureSocket) {
      return this.fixtureSocket;
    }

    this.fixtureSocket = io(`${SOCKET_URL}/fixtures`, {
      transports: ['polling', 'websocket'], // Try polling first, then websocket
      timeout: 30000,
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.fixtureSocket.on('connect', () => {
      console.log('Connected to fixture socket');
      this.isConnected = true;
    });

    this.fixtureSocket.on('disconnect', (reason) => {
      console.log('Disconnected from fixture socket:', reason);
      this.isConnected = false;
    });

    this.fixtureSocket.on('connect_error', (error) => {
      console.error('Fixture socket connection error:', error);
      // Don't throw error, just log it
    });

    this.fixtureSocket.on('reconnect', (attemptNumber) => {
      console.log('Fixture socket reconnected after', attemptNumber, 'attempts');
    });

    this.fixtureSocket.on('reconnect_error', (error) => {
      console.error('Fixture socket reconnection failed:', error);
    });

    return this.fixtureSocket;
  }

  // Initialize chat socket for match discussions
  initializeChatSocket(token?: string): Socket {
    if (this.chatSocket) {
      return this.chatSocket;
    }

    const socketOptions: {
      transports: string[];
      timeout: number;
      forceNew: boolean;
      reconnection: boolean;
      reconnectionAttempts: number;
      reconnectionDelay: number;
      auth?: { token: string };
    } = {
      transports: ['polling', 'websocket'], // Try polling first, then websocket
      timeout: 30000,
      forceNew: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    };

    if (token) {
      socketOptions.auth = { token };
    }

    this.chatSocket = io(`${SOCKET_URL}/chat`, socketOptions);

    this.chatSocket.on('connect', () => {
      console.log('Connected to chat socket');
    });

    this.chatSocket.on('disconnect', (reason) => {
      console.log('Disconnected from chat socket:', reason);
    });

    this.chatSocket.on('connect_error', (error) => {
      console.error('Chat socket connection error:', error);
      // Don't throw error, just log it
    });

    this.chatSocket.on('reconnect', (attemptNumber) => {
      console.log('Chat socket reconnected after', attemptNumber, 'attempts');
    });

    this.chatSocket.on('reconnect_error', (error) => {
      console.error('Chat socket reconnection failed:', error);
    });

    this.chatSocket.on('connect_error', (error) => {
      console.error('Chat socket connection error:', error);
    });

    return this.chatSocket;
  }

  // Subscribe to live matches
  subscribeToLiveMatches(): void {
    if (!this.fixtureSocket) {
      this.initializeFixtureSocket();
    }

    this.fixtureSocket?.emit('subscribe-live');
  }

  // Subscribe to specific fixture
  subscribeToFixture(fixtureId: number): void {
    if (!this.fixtureSocket) {
      this.initializeFixtureSocket();
    }
    
    this.fixtureSocket?.emit('subscribe-fixture', fixtureId);
  }

  // Subscribe to league matches
  subscribeToLeague(leagueId: number): void {
    if (!this.fixtureSocket) {
      this.initializeFixtureSocket();
    }
    
    this.fixtureSocket?.emit('subscribe-league', leagueId);
  }

  // Unsubscribe from fixture
  unsubscribeFromFixture(fixtureId: number): void {
    this.fixtureSocket?.emit('unsubscribe-fixture', fixtureId);
  }

  // Listen for fixture updates
  onFixtureUpdate(callback: (data: SocketFixtureUpdate) => void): () => void {
    this.fixtureSocket?.on('fixture-update', callback);
    return () => {
      this.fixtureSocket?.off('fixture-update', callback);
    };
  }

  // Listen for live fixtures updates (bulk updates for all live matches)
  onLiveFixturesUpdate(callback: (fixtures: Fixture[]) => void): () => void {
    this.fixtureSocket?.on('live-fixtures-update', callback);
    return () => {
      this.fixtureSocket?.off('live-fixtures-update', callback);
    };
  }

  // Listen for goals
  onGoalScored(callback: (data: GoalEvent) => void): () => void {
    this.fixtureSocket?.on('goal-scored', callback);
    return () => {
      this.fixtureSocket?.off('goal-scored', callback);
    };
  }

  // Listen for red cards
  onRedCard(callback: (data: RedCardEvent) => void): () => void {
    this.fixtureSocket?.on('red-card', callback);
    return () => {
      this.fixtureSocket?.off('red-card', callback);
    };
  }

  // Listen for status changes
  onStatusChange(callback: (data: StatusChangeEvent) => void): () => void {
    this.fixtureSocket?.on('status-change', callback);
    return () => {
      this.fixtureSocket?.off('status-change', callback);
    };
  }

  // Chat functions
  joinFixtureChat(fixtureId: number): void {
    this.chatSocket?.emit('join-fixture', fixtureId);
  }

  sendMessage(content: string): void {
    this.chatSocket?.emit('send-message', { content });
  }

  onMessage(callback: (message: ChatMessage) => void): void {
    this.chatSocket?.on('new-message', callback);
  }

  onChatError(callback: (error: Error) => void): void {
    this.chatSocket?.on('error', callback);
  }

  // Remove all listeners
  removeAllListeners(): void {
    this.fixtureSocket?.removeAllListeners();
    this.chatSocket?.removeAllListeners();
  }

  // Disconnect sockets
  disconnect(): void {
    this.fixtureSocket?.disconnect();
    this.chatSocket?.disconnect();
    this.fixtureSocket = null;
    this.chatSocket = null;
    this.isConnected = false;
  }

  // Get connection status
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  // Get fixture socket instance
  getFixtureSocket(): Socket | null {
    return this.fixtureSocket;
  }

  // Get chat socket instance
  getChatSocket(): Socket | null {
    return this.chatSocket;
  }
}

// Create singleton instance
export const socketManager = new SocketManager();

// Export socket manager for use in components
export default socketManager;
