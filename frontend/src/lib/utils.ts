import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Timezone-safe date formatting to avoid date offset issues
export function formatDateSafe(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// Get league logo URL from API-Sports media CDN
export function getLeagueLogoUrl(leagueId: number): string {
  return `https://media.api-sports.io/football/leagues/${leagueId}.png`;
}

// Fix malformed team IDs that have an extra "2" prefix
export function fixTeamId(teamId: number): number {
  const teamIdStr = teamId.toString();

  // Check if team ID starts with "2" and has 5 digits (like 26353, 26354)
  // These should be corrected to 4-digit IDs (6353, 6354)
  if (teamIdStr.length === 5 && teamIdStr.startsWith('2')) {
    const correctedId = parseInt(teamIdStr.substring(1));
    console.log(`🔧 Correcting malformed team ID: ${teamId} -> ${correctedId}`);
    return correctedId;
  }

  return teamId;
}

// Get team logo URL from API-Sports media CDN
export function getTeamLogoUrl(teamId: number): string {
  const correctedTeamId = fixTeamId(teamId);
  return `https://media.api-sports.io/football/teams/${correctedTeamId}.png`;
}

// Get country flag URL from API-Sports media CDN
export function getCountryFlagUrl(countryCode: string): string {
  return `https://media.api-sports.io/flags/${countryCode.toLowerCase()}.svg`;
}

// Generate SEO-friendly slug from team names
export function generateMatchSlug(homeTeam: string, awayTeam: string): string {
  const slugify = (text: string): string => {
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  };

  const homeSlug = slugify(homeTeam);
  const awaySlug = slugify(awayTeam);

  return `${homeSlug}-vs-${awaySlug}`;
}

// Generate match URL
export function generateMatchUrl(homeTeam: string, awayTeam: string, fixtureId: number): string {
  const slug = generateMatchSlug(homeTeam, awayTeam);
  return `/matches/${slug}/${fixtureId}`;
}
