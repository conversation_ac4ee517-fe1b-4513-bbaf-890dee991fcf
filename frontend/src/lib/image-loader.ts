// Custom image loader for API-Sports images with server-side caching
// This loader routes API-Sports images through our proxy to eliminate 429 errors

interface ImageLoaderProps {
  src: string;
  width: number;
  quality?: number;
}

export default function customImageLoader({ src }: ImageLoaderProps): string {
  // Handle empty or invalid src
  if (!src || src.trim() === '') {
    console.warn('Empty src passed to image loader');
    return src; // Return as-is, SmartImage will handle the fallback
  }

  // Check if this is an API-Sports image
  if (src.includes('media.api-sports.io')) {
    try {
      // Extract the path from the API-Sports URL
      const url = new URL(src);
      const pathWithoutLeadingSlash = url.pathname.slice(1); // Remove leading slash

      // Route through our image proxy
      const proxyUrl = `/api/image-proxy/${pathWithoutLeadingSlash}`;

      return proxyUrl;
    } catch (error) {
      console.error('Error parsing API-Sports URL:', error);
      return src; // Return original src if URL parsing fails
    }
  }

  // For non-API-Sports images, return as-is
  return src;
}

// Utility function to convert API-Sports URLs to proxy URLs
export function convertToProxyUrl(apiSportsUrl: string): string {
  if (!apiSportsUrl || !apiSportsUrl.includes('media.api-sports.io')) {
    return apiSportsUrl;
  }
  
  try {
    const url = new URL(apiSportsUrl);
    const pathWithoutLeadingSlash = url.pathname.slice(1);
    return `/api/image-proxy/${pathWithoutLeadingSlash}`;
  } catch (error) {
    console.error('Failed to convert API-Sports URL to proxy URL:', error);
    return apiSportsUrl;
  }
}

// Utility function to check if URL is an API-Sports image
export function isApiSportsImage(src: string): boolean {
  return src.includes('media.api-sports.io');
}
