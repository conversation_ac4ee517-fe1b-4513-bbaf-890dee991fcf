import { promises as fs } from 'fs';
import path from 'path';

const CACHE_DIR = path.join(process.cwd(), '.next/cache/images');

interface CacheStats {
  totalFiles: number;
  totalSize: number;
  oldestFile: string | null;
  newestFile: string | null;
  expiredFiles: number;
}

interface CacheMetadata {
  originalUrl: string;
  cachedAt: number;
  contentType: string;
  size: number;
}

// Get cache statistics
export async function getCacheStats(): Promise<CacheStats> {
  try {
    const files = await fs.readdir(CACHE_DIR);
    const metadataFiles = files.filter(file => file.endsWith('.meta'));
    
    let totalSize = 0;
    let oldestTime = Infinity;
    let newestTime = 0;
    let oldestFile: string | null = null;
    let newestFile: string | null = null;
    let expiredFiles = 0;
    
    const now = Math.floor(Date.now() / 1000);
    const maxAge = 30 * 24 * 60 * 60; // 30 days
    
    for (const metaFile of metadataFiles) {
      try {
        const metaPath = path.join(CACHE_DIR, metaFile);
        const metaContent = await fs.readFile(metaPath, 'utf-8');
        const metadata: CacheMetadata = JSON.parse(metaContent);
        
        totalSize += metadata.size;
        
        if (metadata.cachedAt < oldestTime) {
          oldestTime = metadata.cachedAt;
          oldestFile = metadata.originalUrl;
        }
        
        if (metadata.cachedAt > newestTime) {
          newestTime = metadata.cachedAt;
          newestFile = metadata.originalUrl;
        }
        
        if ((now - metadata.cachedAt) > maxAge) {
          expiredFiles++;
        }
      } catch (error) {
        console.error(`Error reading metadata file ${metaFile}:`, error);
      }
    }
    
    return {
      totalFiles: metadataFiles.length,
      totalSize,
      oldestFile,
      newestFile,
      expiredFiles,
    };
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return {
      totalFiles: 0,
      totalSize: 0,
      oldestFile: null,
      newestFile: null,
      expiredFiles: 0,
    };
  }
}

// Clean expired cache files
export async function cleanExpiredCache(): Promise<{ deletedFiles: number; freedSpace: number }> {
  try {
    const files = await fs.readdir(CACHE_DIR);
    const metadataFiles = files.filter(file => file.endsWith('.meta'));
    
    const now = Math.floor(Date.now() / 1000);
    const maxAge = 30 * 24 * 60 * 60; // 30 days
    
    let deletedFiles = 0;
    let freedSpace = 0;
    
    for (const metaFile of metadataFiles) {
      try {
        const metaPath = path.join(CACHE_DIR, metaFile);
        const metaContent = await fs.readFile(metaPath, 'utf-8');
        const metadata: CacheMetadata = JSON.parse(metaContent);
        
        if ((now - metadata.cachedAt) > maxAge) {
          const cacheKey = metaFile.replace('.meta', '');
          const imagePath = path.join(CACHE_DIR, `${cacheKey}.img`);
          
          // Delete both metadata and image files
          await Promise.all([
            fs.unlink(metaPath),
            fs.unlink(imagePath).catch(() => {}), // Ignore if image file doesn't exist
          ]);
          
          deletedFiles++;
          freedSpace += metadata.size;
          
          console.log(`🗑️ Deleted expired cache: ${metadata.originalUrl}`);
        }
      } catch (error) {
        console.error(`Error processing metadata file ${metaFile}:`, error);
      }
    }
    
    return { deletedFiles, freedSpace };
  } catch (error) {
    console.error('Error cleaning expired cache:', error);
    return { deletedFiles: 0, freedSpace: 0 };
  }
}

// Clean all cache files
export async function clearAllCache(): Promise<{ deletedFiles: number; freedSpace: number }> {
  try {
    const files = await fs.readdir(CACHE_DIR);
    let deletedFiles = 0;
    let freedSpace = 0;
    
    // Get total size before deletion
    for (const file of files) {
      if (file.endsWith('.meta')) {
        try {
          const metaPath = path.join(CACHE_DIR, file);
          const metaContent = await fs.readFile(metaPath, 'utf-8');
          const metadata: CacheMetadata = JSON.parse(metaContent);
          freedSpace += metadata.size;
        } catch {
          // Ignore errors reading metadata
        }
      }
    }
    
    // Delete all files
    for (const file of files) {
      try {
        await fs.unlink(path.join(CACHE_DIR, file));
        deletedFiles++;
      } catch (error) {
        console.error(`Error deleting file ${file}:`, error);
      }
    }
    
    console.log(`🗑️ Cleared all cache: ${deletedFiles} files, ${freedSpace} bytes`);
    return { deletedFiles, freedSpace };
  } catch (error) {
    console.error('Error clearing all cache:', error);
    return { deletedFiles: 0, freedSpace: 0 };
  }
}

// Format bytes to human readable format
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Get cache health status
export async function getCacheHealth(): Promise<{
  status: 'healthy' | 'warning' | 'critical';
  message: string;
  stats: CacheStats;
}> {
  const stats = await getCacheStats();
  
  if (stats.totalFiles === 0) {
    return {
      status: 'healthy',
      message: 'Cache is empty',
      stats,
    };
  }
  
  const sizeInMB = stats.totalSize / (1024 * 1024);
  const expiredPercentage = (stats.expiredFiles / stats.totalFiles) * 100;
  
  if (sizeInMB > 1000) { // > 1GB
    return {
      status: 'critical',
      message: `Cache size is very large (${formatBytes(stats.totalSize)}). Consider cleaning expired files.`,
      stats,
    };
  }
  
  if (expiredPercentage > 50) {
    return {
      status: 'warning',
      message: `${expiredPercentage.toFixed(1)}% of cached files are expired. Consider running cache cleanup.`,
      stats,
    };
  }
  
  return {
    status: 'healthy',
    message: `Cache is healthy: ${stats.totalFiles} files, ${formatBytes(stats.totalSize)}`,
    stats,
  };
}
