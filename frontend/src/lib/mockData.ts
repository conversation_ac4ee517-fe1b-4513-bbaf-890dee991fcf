import { Fixture } from './types';

// Mock live fixtures data for when API is unavailable
export const mockLiveFixtures: Fixture[] = [
  {
    _id: 1035394,
    fixture: {
      id: 1035394,
      referee: '<PERSON>',
      timezone: 'UTC',
      date: '2024-01-20T15:00:00+00:00',
      timestamp: 1705762800,
      periods: {
        first: 1705762800,
        second: 1705766400
      },
      venue: {
        id: 556,
        name: 'Old Trafford',
        city: 'Manchester'
      },
      status: {
        long: 'Match Finished',
        short: 'FT',
        elapsed: 90
      }
    },
    league: {
      id: 39,
      name: 'Premier League',
      country: 'England',
      logo: 'https://media.api-sports.io/football/leagues/39.png',
      flag: 'https://media.api-sports.io/flags/gb.svg',
      season: 2024,
      round: 'Regular Season - 21'
    },
    teams: {
      home: {
        id: 33,
        name: 'Manchester United',
        logo: 'https://media.api-sports.io/football/teams/33.png',
        winner: true
      },
      away: {
        id: 40,
        name: 'Liverpool',
        logo: 'https://media.api-sports.io/football/teams/40.png',
        winner: false
      }
    },
    goals: {
      home: 2,
      away: 1
    },
    score: {
      halftime: {
        home: 1,
        away: 0
      },
      fulltime: {
        home: 2,
        away: 1
      },
      extratime: {
        home: undefined,
        away: undefined
      },
      penalty: {
        home: undefined,
        away: undefined
      }
    },
    date: new Date('2024-01-20'),
    lastUpdated: new Date()
  },
  {
    _id: 1035395,
    fixture: {
      id: 1035395,
      referee: 'Anthony Taylor',
      timezone: 'UTC',
      date: '2024-01-20T17:30:00+00:00',
      timestamp: 1705771800,
      periods: {
        first: 1705771800,
        second: undefined
      },
      venue: {
        id: 508,
        name: 'Emirates Stadium',
        city: 'London'
      },
      status: {
        long: 'First Half',
        short: '1H',
        elapsed: 35
      }
    },
    league: {
      id: 39,
      name: 'Premier League',
      country: 'England',
      logo: 'https://media.api-sports.io/football/leagues/39.png',
      flag: 'https://media.api-sports.io/flags/gb.svg',
      season: 2024,
      round: 'Regular Season - 21'
    },
    teams: {
      home: {
        id: 42,
        name: 'Arsenal',
        logo: 'https://media.api-sports.io/football/teams/42.png',
        winner: undefined
      },
      away: {
        id: 50,
        name: 'Manchester City',
        logo: 'https://media.api-sports.io/football/teams/50.png',
        winner: undefined
      }
    },
    goals: {
      home: 1,
      away: 0
    },
    score: {
      halftime: {
        home: undefined,
        away: undefined
      },
      fulltime: {
        home: undefined,
        away: undefined
      },
      extratime: {
        home: undefined,
        away: undefined
      },
      penalty: {
        home: undefined,
        away: undefined
      }
    },
    date: new Date('2024-01-20'),
    lastUpdated: new Date()
  },
  {
    _id: 1035396,
    fixture: {
      id: 1035396,
      referee: 'Craig Pawson',
      timezone: 'UTC',
      date: '2024-01-20T20:00:00+00:00',
      timestamp: 1705780800,
      periods: {
        first: undefined,
        second: undefined
      },
      venue: {
        id: 504,
        name: 'Stamford Bridge',
        city: 'London'
      },
      status: {
        long: 'Not Started',
        short: 'NS',
        elapsed: undefined
      }
    },
    league: {
      id: 39,
      name: 'Premier League',
      country: 'England',
      logo: 'https://media.api-sports.io/football/leagues/39.png',
      flag: 'https://media.api-sports.io/flags/gb.svg',
      season: 2024,
      round: 'Regular Season - 21'
    },
    teams: {
      home: {
        id: 49,
        name: 'Chelsea',
        logo: 'https://media.api-sports.io/football/teams/49.png',
        winner: undefined
      },
      away: {
        id: 47,
        name: 'Tottenham',
        logo: 'https://media.api-sports.io/football/teams/47.png',
        winner: undefined
      }
    },
    goals: {
        home: undefined,
        away: undefined
      },
    score: {
      halftime: {
        home: undefined,
        away: undefined
      },
      fulltime: {
        home: undefined,
        away: undefined
      },
      extratime: {
        home: undefined,
        away: undefined
      },
      penalty: {
        home: undefined,
        away: undefined
      }
    },
    date: new Date('2024-01-20'),
    lastUpdated: new Date()
  }
];

// Generate additional mock fixtures to simulate the 19 live events
const additionalMockFixtures: Fixture[] = [];
for (let i = 4; i <= 19; i++) {
  additionalMockFixtures.push({
    _id: 1035396 + i,
    fixture: {
      id: 1035396 + i,
      referee: 'Referee ' + i,
      timezone: 'UTC',
      date: '2024-01-20T' + (15 + (i % 8)).toString().padStart(2, '0') + ':00:00+00:00',
      timestamp: 1705762800 + (i * 3600),
      periods: {
        first: i % 3 === 0 ? undefined : 1705762800 + (i * 3600),
        second: i % 3 === 0 ? undefined : (i % 2 === 0 ? 1705766400 + (i * 3600) : undefined)
      },
      venue: {
        id: 500 + i,
        name: `Stadium ${i}`,
        city: `City ${i}`
      },
      status: {
        long: i % 3 === 0 ? 'Not Started' : (i % 2 === 0 ? 'Match Finished' : 'Second Half'),
        short: i % 3 === 0 ? 'NS' : (i % 2 === 0 ? 'FT' : '2H'),
        elapsed: i % 3 === 0 ? undefined : (i % 2 === 0 ? 90 : 60 + (i % 30))
      }
    },
    league: {
      id: i % 2 === 0 ? 39 : 78,
      name: i % 2 === 0 ? 'Premier League' : 'Bundesliga',
      country: i % 2 === 0 ? 'England' : 'Germany',
      logo: `https://media.api-sports.io/football/leagues/${i % 2 === 0 ? 39 : 78}.png`,
      flag: `https://media.api-sports.io/flags/${i % 2 === 0 ? 'gb' : 'de'}.svg`,
      season: 2024,
      round: 'Regular Season - 21'
    },
    teams: {
      home: {
        id: 100 + i,
        name: `Team Home ${i}`,
        logo: `https://media.api-sports.io/football/teams/${100 + i}.png`,
        winner: i % 3 === 1 ? true : (i % 3 === 2 ? false : undefined)
      },
      away: {
        id: 200 + i,
        name: `Team Away ${i}`,
        logo: `https://media.api-sports.io/football/teams/${200 + i}.png`,
        winner: i % 3 === 2 ? true : (i % 3 === 1 ? false : undefined)
      }
    },
    goals: {
      home: i % 3 === 0 ? undefined : (i % 4),
      away: i % 3 === 0 ? undefined : ((i + 1) % 3)
    },
    score: {
      halftime: {
        home: i % 3 === 0 ? undefined : Math.floor((i % 4) / 2),
        away: i % 3 === 0 ? undefined : Math.floor(((i + 1) % 3) / 2)
      },
      fulltime: {
        home: i % 2 === 0 ? (i % 4) : undefined,
        away: i % 2 === 0 ? ((i + 1) % 3) : undefined
      },
      extratime: {
        home: undefined,
        away: undefined
      },
      penalty: {
        home: undefined,
        away: undefined
      }
    },
    date: new Date('2024-01-20'),
    lastUpdated: new Date()
  });
}

// Export all mock fixtures (19 total)
export const allMockLiveFixtures = [...mockLiveFixtures, ...additionalMockFixtures];

// Function to simulate live updates
export function getRandomLiveFixtureUpdate(): Fixture {
  const fixtures = allMockLiveFixtures.filter(f => 
    f.fixture.status.short === '1H' || f.fixture.status.short === '2H'
  );
  
  if (fixtures.length === 0) return allMockLiveFixtures[0];
  
  const randomFixture = fixtures[Math.floor(Math.random() * fixtures.length)];
  
  // Simulate score update
  const updatedFixture = {
    ...randomFixture,
    goals: {
      home: (randomFixture.goals.home || 0) + (Math.random() > 0.7 ? 1 : 0),
      away: (randomFixture.goals.away || 0) + (Math.random() > 0.7 ? 1 : 0)
    },
    fixture: {
      ...randomFixture.fixture,
      status: {
        ...randomFixture.fixture.status,
        elapsed: (randomFixture.fixture.status.elapsed || 0) + Math.floor(Math.random() * 5)
      }
    },
    lastUpdated: new Date()
  };
  
  return updatedFixture;
}