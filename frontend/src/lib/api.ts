import {
  Fixture,
  League,
  Standing,
  Team,
  Venue,
  Match<PERSON>ilters,
  <PERSON><PERSON>,
  Prediction,
  HeadToHead,
  FixtureStatistics,
  MatchEvent,
  Lineup,
  LineupPlayer,
  VoteOption,
  VoteCategory,
  VoteResponse,
  VoteStats,
  UserVote,
  UserVotesByCategory,
  BatchVoteStats,
  Tip,
  TipWithDetails,
  TipType,
  TipDetails,
  TipsterStats,
  BestOddsResult,
  OddsComparison,
  TeamStatistics
} from './types';
import { allMockLiveFixtures } from './mockData';

const API_BASE_URL = 'https://api.kickoffpredictions.com';
const API_KEY = '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';

// Helper function to get auth token from localStorage
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('kickoffscore_token');
  }
  return null;
};

// API client configuration
const apiClient = {
  get: async <T>(endpoint: string, params?: Record<string, string | number>): Promise<T> => {
    const url = new URL(`${API_BASE_URL}${endpoint}`);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value.toString());
      });
    }

    // Debug logging to track API calls
    console.log('🌐 API Call:', {
      endpoint,
      params,
      url: url.toString(),
      timestamp: new Date().toLocaleTimeString()
    });

    const headers: Record<string, string> = {
      'X-API-Key': API_KEY,
      'Content-Type': 'application/json',
    };

    const token = getAuthToken();
    if (token) {
      headers['x-auth-token'] = token;
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      let errorMessage = `${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.map((err: { msg?: string; message?: string }) => err.msg || err.message || 'Unknown error').join(', ');
        }
      } catch {
        // If JSON parsing fails, use the status text
        const errorText = await response.text();
        if (errorText) {
          errorMessage = errorText;
        }
      }
      throw new Error(`API Error: ${errorMessage}`);
    }

    return response.json();
  },

  post: async <T>(endpoint: string, data?: Record<string, unknown>): Promise<T> => {
    const headers: Record<string, string> = {
      'X-API-Key': API_KEY,
      'Content-Type': 'application/json',
    };

    const token = getAuthToken();
    if (token) {
      headers['x-auth-token'] = token;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      let errorMessage = `${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.map((err: { msg?: string; message?: string }) => err.msg || err.message || 'Unknown error').join(', ');
        }
      } catch {
        // If JSON parsing fails, use the status text
      }
      throw new Error(`API Error: ${errorMessage}`);
    }

    return response.json();
  },

  delete: async <T>(endpoint: string): Promise<T> => {
    const headers: Record<string, string> = {
      'X-API-Key': API_KEY,
      'Content-Type': 'application/json',
    };

    const token = getAuthToken();
    if (token) {
      headers['x-auth-token'] = token;
    }

    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'DELETE',
      headers,
    });

    if (!response.ok) {
      let errorMessage = `${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.errors && Array.isArray(errorData.errors)) {
          errorMessage = errorData.errors.map((err: { msg?: string; message?: string }) => err.msg || err.message || 'Unknown error').join(', ');
        }
      } catch {
        // If JSON parsing fails, use the status text
      }
      throw new Error(`API Error: ${errorMessage}`);
    }

    return response.json();
  },
};

// API functions
export const api = {
  // Fixtures
  getFixtures: (filters?: MatchFilters): Promise<Fixture[]> => {
    const params: Record<string, string | number> = {};
    
    if (filters?.filter === 'live') {
      params.live = 'all';
    } else if (filters?.date) {
      params.date = filters.date;
    }
    
    if (filters?.league) {
      params.league = filters.league;
    }
    
    if (filters?.team) {
      params.team = filters.team;
    }

    return apiClient.get<Fixture[]>('/api/fixtures', params);
  },

  getLiveFixtures: async (): Promise<Fixture[]> => {
    try {
      return await apiClient.get<Fixture[]>('/api/fixtures', { live: 'all' });
    } catch (error) {
      console.warn('API unavailable, using mock data for live fixtures:', error);
      // Return mock data when API is unavailable
      return allMockLiveFixtures;
    }
  },

  getFixtureById: (id: number, includeTeamRatings: boolean = true): Promise<Fixture> => {
    const params: Record<string, string | number> = { id };
    if (includeTeamRatings) {
      params.includeTeamRatings = 'true';
    }
    return apiClient.get<Fixture>('/api/fixtures', params);
  },

  getFixturesByDate: (date: string): Promise<Fixture[]> => {
    return apiClient.get<Fixture[]>('/api/fixtures', { date });
  },

  getFixturesByLeague: (leagueId: number, season: number): Promise<Fixture[]> => {
    return apiClient.get<Fixture[]>('/api/fixtures', { 
      league: leagueId, 
      season 
    });
  },

  // Leagues
  getLeagues: (): Promise<League[]> => {
    return apiClient.get<League[]>('/api/leagues');
  },

  getLeagueById: (id: number): Promise<League> => {
    return apiClient.get<League>('/api/leagues', { id });
  },

  // Teams
  getTeams: (): Promise<Team[]> => {
    return apiClient.get<Team[]>('/api/teams');
  },

  getTeamById: (id: number): Promise<Team> => {
    return apiClient.get<Team>('/api/teams', { id });
  },

  getTeamsByLeague: (leagueId: number, season: number): Promise<Team[]> => {
    return apiClient.get<Team[]>('/api/teams', {
      league: leagueId,
      season: season
    });
  },

  getTeamStatistics: (teamId: number, leagueId: number, season: number): Promise<TeamStatistics> => {
    return apiClient.get<TeamStatistics>('/api/teams/statistics', {
      team: teamId,
      league: leagueId,
      season: season
    });
  },

  getTeamRecentFixtures: (teamId: number, last: number = 5): Promise<Fixture[]> => {
    return apiClient.get<Fixture[]>('/api/fixtures', {
      team: teamId,
      last: last
    });
  },

  getNextFixtureForTeam: (teamId: number): Promise<Fixture[]> => {
    return apiClient.get<Fixture[]>('/api/fixtures', {
      team: teamId,
      next: 1
    });
  },

  // Bulk next fixtures for league (more efficient than individual team calls)
  getNextFixturesForLeague: (leagueId: number, season: number, limit: number = 20): Promise<Fixture[]> => {
    return apiClient.get<Fixture[]>('/api/fixtures', {
      league: leagueId,
      season: season,
      next: limit
    });
  },

  // Standings
  getStandings: (leagueId: number, season: number): Promise<Standing[][]> => {
    return apiClient.get<Standing[][]>('/api/standings', {
      leagueId,
      season
    });
  },

  // Odds
  getOdds: (fixtureId: number): Promise<Odds[]> => {
    return apiClient.get<Odds[]>('/api/odds', { fixture: fixtureId });
  },

  getOddsByLeague: (leagueId: number, season: number): Promise<Odds[]> => {
    return apiClient.get<Odds[]>('/api/odds', {
      league: leagueId,
      season
    });
  },

  getOddsByDate: (date: string): Promise<Odds[]> => {
    return apiClient.get<Odds[]>('/api/odds', { date });
  },

  getLiveOdds: (fixtureId?: number): Promise<Odds[]> => {
    const params: Record<string, string | number> = {};
    if (fixtureId) {
      params.fixture = fixtureId;
    }
    return apiClient.get<Odds[]>('/api/odds/live', params);
  },

  // Predictions
  getPredictions: async (fixtureId: number): Promise<Prediction> => {
    try {
      return await apiClient.get<Prediction>('/api/predictions', { fixture: fixtureId });
    } catch (error) {
      console.warn('API unavailable, using mock data for predictions:', error);
      // Return mock prediction data when API is unavailable
      return {
        fixture: {
          id: fixtureId,
          date: '2024-01-20T15:00:00+00:00',
          timestamp: 1705762800,
        },
        league: {
          id: 39,
          name: 'Premier League',
          country: 'England',
          logo: 'https://media.api-sports.io/football/leagues/39.png',
          flag: 'https://media.api-sports.io/flags/gb.svg',
          season: 2024,
        },
        teams: {
          home: {
            id: 42,
            name: 'Arsenal',
            logo: 'https://media.api-sports.io/football/teams/42.png',
          },
          away: {
            id: 50,
            name: 'Manchester City',
            logo: 'https://media.api-sports.io/football/teams/50.png',
          },
        },
        predictions: {
          winner: {
            id: 42,
            name: 'Arsenal',
            comment: 'Arsenal has better form and home advantage',
          },
          win_or_draw: true,
          under_over: 'Over 2.5',
          goals: {
            home: '2.1',
            away: '1.8',
          },
          advice: 'Arsenal to win',
          percent: {
            home: '45',
            draw: '25',
            away: '30',
          },
        },
        comparison: {
          form: {
            home: '78',
            away: '72',
          },
          att: {
            home: '85',
            away: '88',
          },
          def: {
            home: '76',
            away: '82',
          },
          poisson_distribution: {
            home: '42',
            away: '38',
          },
          h2h: {
            home: '65',
            away: '58',
          },
          goals: {
            home: '68',
            away: '74',
          },
          total: {
            home: '75',
            away: '73',
          },
        },
      };
    }
  },

  // Head-to-Head
  getHeadToHead: (team1Id: number, team2Id: number): Promise<HeadToHead[]> => {
    return apiClient.get<HeadToHead[]>('/api/fixtures/h2h', {
      h2h: `${team1Id}-${team2Id}`
    });
  },

  getHeadToHeadLimited: (team1Id: number, team2Id: number, last: number): Promise<HeadToHead[]> => {
    return apiClient.get<HeadToHead[]>('/api/fixtures/h2h', {
      h2h: `${team1Id}-${team2Id}`,
      last
    });
  },

  // Fixture Statistics
  getFixtureStatistics: (fixtureId: number): Promise<FixtureStatistics[]> => {
    return apiClient.get<FixtureStatistics[]>(`/api/fixtures/${fixtureId}/statistics`);
  },

  getFixtureEvents: (fixtureId: number): Promise<MatchEvent[]> => {
    return apiClient.get<MatchEvent[]>(`/api/fixtures/${fixtureId}/events`);
  },

  getFixtureLineups: (fixtureId: number): Promise<Lineup[]> => {
    return apiClient.get<Lineup[]>(`/api/fixtures/${fixtureId}/lineups`);
  },

  getFixturePlayerStats: (fixtureId: number, teamId?: number): Promise<LineupPlayer[]> => {
    const params: Record<string, string | number> = {};
    if (teamId) {
      params.team = teamId;
    }
    return apiClient.get<LineupPlayer[]>(`/api/fixtures/${fixtureId}/players`, params);
  },

  // Voting API functions
  castVote: (fixtureId: number, vote: VoteOption, category: VoteCategory = VoteCategory.MATCH_OUTCOME): Promise<VoteResponse> => {
    return apiClient.post<VoteResponse>(`/api/fixtures/votes/${fixtureId}`, { vote, category });
  },

  getVoteStats: (fixtureId: number): Promise<VoteStats> => {
    return apiClient.get<VoteStats>(`/api/fixtures/votes/${fixtureId}/stats`);
  },

  getUserVote: (fixtureId: number): Promise<UserVotesByCategory> => {
    return apiClient.get<UserVotesByCategory>(`/api/fixtures/votes/${fixtureId}/user`);
  },

  getBatchVoteStats: (fixtureIds: number[]): Promise<BatchVoteStats> => {
    const params = new URLSearchParams();
    fixtureIds.forEach(id => params.append('fixtureIds', id.toString()));
    return apiClient.get<BatchVoteStats>(`/api/fixtures/votes/batch/stats?${params.toString()}`);
  },

  getUserVoteCount: (): Promise<{ count: number }> => {
    return apiClient.get<{ count: number }>('/api/fixtures/votes/count');
  },

  getUserVotesByDate: (date: string): Promise<UserVote[]> => {
    return apiClient.get<UserVote[]>(`/api/fixtures/votes/dates?date=${date}`);
  },

  getAllUserVotes: (): Promise<UserVote[]> => {
    return apiClient.get<UserVote[]>('/api/fixtures/votes/user');
  },

  // Search
  searchFixtures: (query: string): Promise<Fixture[]> => {
    // This would need to be implemented on the backend
    // For now, we'll return an empty array
    console.log('Search fixtures not implemented:', query);
    return Promise.resolve([]);
  },

  searchTeams: (query: string): Promise<Team[]> => {
    return apiClient.get<Team[]>('/api/teams', { search: query });
  },

  searchLeagues: (query: string): Promise<League[]> => {
    return apiClient.get<League[]>('/api/leagues', { search: query });
  },

  // Tips API
  getFixtureTips: (fixtureId: number): Promise<TipWithDetails[]> => {
    return apiClient.get<TipWithDetails[]>(`/api/tips/fixture/${fixtureId}`);
  },

  createTip: (tipData: {
    fixtureId: number;
    tipType: TipType;
    details: TipDetails;
    odds: number;
    stake?: number;
    description?: string;
    isPublic?: boolean;
  }): Promise<Tip> => {
    return apiClient.post<Tip>('/api/tips', tipData);
  },

  deleteTip: (tipId: string): Promise<{ message: string }> => {
    return apiClient.delete<{ message: string }>(`/api/tips/${tipId}`);
  },

  likeTip: (tipId: string, action: 'like' | 'unlike'): Promise<{ message: string; likes: number }> => {
    return apiClient.post<{ message: string; likes: number }>(`/api/tips/${tipId}/like`, { action });
  },

  getUserTips: (): Promise<TipWithDetails[]> => {
    return apiClient.get<TipWithDetails[]>('/api/tips/user/me');
  },

  // Chat/Messages API
  getFixtureMessages: (fixtureId: number, limit?: number, before?: string): Promise<{
    messages: Array<{
      _id: string;
      fixtureId: number;
      userId: string;
      userName: string;
      content: string;
      createdAt: string;
    }>;
    chatOpen: boolean;
    pagination: {
      limit: number;
      hasMore: boolean;
      nextBefore: string | null;
    };
  }> => {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (before) params.append('before', before);

    const queryString = params.toString();
    const url = `/api/fixtures/${fixtureId}/messages${queryString ? `?${queryString}` : ''}`;

    return apiClient.get(url);
  },

  getUserTipsterStats: (): Promise<TipsterStats> => {
    return apiClient.get<TipsterStats>('/api/tips/user/me/stats');
  },

  getTipsterRanking: (limit?: number): Promise<TipsterStats[]> => {
    const params: Record<string, string | number> = {};
    if (limit) params.limit = limit;
    return apiClient.get<TipsterStats[]>('/api/tipsters/ranking', params);
  },

  getTipsterProfile: (username: string): Promise<{
    user: { name?: string; username?: string; profileImage?: string };
    stats: TipsterStats;
    recentResults: Array<{
      tipType: string;
      odds: number;
      stake: number;
      status: 'WON' | 'LOST' | 'VOID';
    }>;
  }> => {
    return apiClient.get(`/api/tipsters/profile/${username}`);
  },

  // Best Odds API functions
  getBestOdds: async (
    fixtureId: number,
    tipType: TipType,
    details: TipDetails,
    live: boolean = false
  ): Promise<BestOddsResult> => {
    return apiClient.post<BestOddsResult>('/api/odds/best', {
      fixture: fixtureId,
      tipType,
      details,
      live
    });
  },

  // Legacy function for backward compatibility
  getBestOddsLegacy: async (
    fixtureId: number,
    tipType: TipType,
    selection: string,
    live: boolean = false
  ): Promise<BestOddsResult> => {
    return apiClient.get<BestOddsResult>('/api/odds/best', {
      fixture: fixtureId,
      tipType,
      selection,
      live: live.toString()
    });
  },

  getOddsComparison: async (
    fixtureId: number,
    tipType: TipType,
    live: boolean = false
  ): Promise<OddsComparison> => {
    return apiClient.get<OddsComparison>(`/api/odds/comparison`, {
      fixtureId: fixtureId.toString(),
      tipType,
      live: live.toString()
    });
  },

  getVenue: (id: number): Promise<Venue> => {
    return apiClient.get<Venue>(`/api/venues`, {
      id: id.toString()
    });
  },
};

// Helper functions for date formatting
export const formatDate = (date: Date): string => {
  // Use local date components to avoid timezone issues
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`; // YYYY-MM-DD format
};

export const formatDateTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: false, // Use 24-hour format (no AM/PM)
  });
};

// Constants for popular leagues (based on the screenshot)
export const TOP_LEAGUES = [
  { id: 39, name: 'Premier League', country: 'England' },
  { id: 78, name: 'Bundesliga', country: 'Germany' },
  { id: 140, name: 'La Liga', country: 'Spain' },
  { id: 61, name: 'Ligue 1', country: 'France' },
  { id: 135, name: 'Serie A', country: 'Italy' },
  { id: 2, name: 'UEFA Champions League', country: 'World' },
  { id: 3, name: 'UEFA Europa League', country: 'World' },
  { id: 45, name: 'FA Cup', country: 'England' },
  { id: 144, name: 'Championship', country: 'England' },
  { id: 1, name: 'World Cup', country: 'World' },
] as const;
