// Google OAuth configuration
export const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '';

// Google OAuth utilities
export const initializeGoogleOAuth = (): Promise<typeof window.google> => {
  return new Promise((resolve, reject) => {
    if (typeof window === 'undefined') {
      reject(new Error('Google OAuth can only be initialized in browser'));
      return;
    }

    // Load Google OAuth script
    if (!window.google) {
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      script.onload = () => {
        window.google.accounts.id.initialize({
          client_id: GOOGLE_CLIENT_ID,
          callback: () => {}, // Will be set by component
        });
        resolve(window.google);
      };
      script.onerror = () => reject(new Error('Failed to load Google OAuth'));
      document.head.appendChild(script);
    } else {
      resolve(window.google);
    }
  });
};

interface GoogleOAuthResult {
  accessToken: string;
  profile: {
    id: string;
    email: string;
    name: string;
    picture?: string;
  };
}

export const signInWithGoogle = (): Promise<GoogleOAuthResult> => {
  return new Promise((resolve, reject) => {
    if (!window.google) {
      reject(new Error('Google OAuth not initialized'));
      return;
    }

    window.google.accounts.id.prompt((notification: { isNotDisplayed: () => boolean; isSkippedMoment: () => boolean }) => {
      if (notification.isNotDisplayed() || notification.isSkippedMoment()) {
        // Fallback to popup
        window.google.accounts.oauth2.initTokenClient({
          client_id: GOOGLE_CLIENT_ID,
          scope: 'email profile',
          callback: (response: { access_token?: string }) => {
            if (response.access_token) {
              // Get user profile
              fetch(`https://www.googleapis.com/oauth2/v2/userinfo?access_token=${response.access_token}`)
                .then(res => res.json())
                .then(profile => {
                  resolve({
                    accessToken: response.access_token!,
                    profile: {
                      id: profile.id,
                      email: profile.email,
                      name: profile.name,
                      picture: profile.picture,
                    }
                  });
                })
                .catch(reject);
            } else {
              reject(new Error('Failed to get access token'));
            }
          },
        }).requestAccessToken();
      }
    });
  });
};



// Type declarations for global objects
declare global {
  interface Window {
    google: {
      accounts: {
        id: {
          initialize: (config: { client_id: string; callback: () => void }) => void;
          prompt: (callback: (notification: { isNotDisplayed: () => boolean; isSkippedMoment: () => boolean }) => void) => void;
        };
        oauth2: {
          initTokenClient: (config: {
            client_id: string;
            scope: string;
            callback: (response: { access_token?: string }) => void;
          }) => { requestAccessToken: () => void };
        };
      };
    };
  }
}
