export interface User {
  _id: string;
  email: string;
  name: string;
  profileImage?: string;
  favoriteTeams: number[];
  favoriteLeagues: number[];
  favoritePlayers: number[];
  providers: {
    email?: { verified: boolean };
    google?: {
      id: string;
      email: string;
      verified: boolean;
    };
  };
  notificationPreferences: {
    goals: boolean;
    redCards: boolean;
    matchStart: boolean;
    matchEnd: boolean;
    favoriteTeams: boolean;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name: string;
}

export interface OAuthCredentials {
  provider: 'google';
  accessToken: string;
  profile: {
    id: string;
    email: string;
    name: string;
    picture?: string;
  };
}

export interface AuthContextType {
  // State
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (credentials: SignupCredentials) => Promise<void>;
  loginWithOAuth: (credentials: OAuthCredentials) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  
  // Utilities
  getAuthHeaders: () => Record<string, string>;
}

export interface AuthResponse {
  token: string;
  user: User;
}

export interface AuthError {
  message: string;
  field?: string;
}
