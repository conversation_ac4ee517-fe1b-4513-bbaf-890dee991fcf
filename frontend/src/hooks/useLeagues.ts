'use client';

import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { QUERY_KEYS, CACHE_TIMES } from '@/lib/constants';

// Hook for fetching all leagues
export function useLeagues() {
  return useQuery({
    queryKey: [QUERY_KEYS.leagues],
    queryFn: () => api.getLeagues(),
    staleTime: CACHE_TIMES.leagues,
  });
}

// Hook for fetching a specific league
export function useLeague(leagueId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.leagues, leagueId],
    queryFn: () => api.getLeagueById(leagueId),
    staleTime: CACHE_TIMES.leagues,
    enabled: !!leagueId,
  });
}

// Hook for fetching teams in a league
export function useTeamsByLeague(leagueId: number, season: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.teams, leagueId, season],
    queryFn: () => api.getTeamsByLeague(leagueId, season),
    staleTime: CACHE_TIMES.teams,
    enabled: !!leagueId && !!season,
  });
}

// Hook for fetching league standings
export function useStandings(leagueId: number, season: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.standings, leagueId, season],
    queryFn: () => api.getStandings(leagueId, season),
    staleTime: CACHE_TIMES.standings,
    enabled: !!leagueId && !!season,
  });
}
