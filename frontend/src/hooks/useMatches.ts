'use client';

import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo } from 'react';
import { api, formatDate } from '@/lib/api';
import { useSocket } from '@/providers/SocketProvider';
import {
  Fixture,
  MatchFilters,
  isLiveMatch
} from '@/lib/types';
import { QUERY_KEYS, CACHE_TIMES } from '@/lib/constants';

// Hook for fetching matches with filters
export function useMatches(filters?: MatchFilters) {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, subscribeToLiveMatches } = useSocket();

  const query = useQuery({
    queryKey: [QUERY_KEYS.fixtures, filters],
    queryFn: () => api.getFixtures(filters),
    staleTime: filters?.filter === 'live' ? CACHE_TIMES.fixtures : CACHE_TIMES.static,
    // ✅ Remove polling for live matches - WebSocket handles updates
    refetchInterval: false,
  });

  // Subscribe to live updates if filtering for live matches or if we have date-based queries
  useEffect(() => {
    if (filters?.filter === 'live' || filters?.date) {
      subscribeToLiveMatches();

      // Listen for fixture updates and invalidate cache
      const unsubscribe = onFixtureUpdate(() => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures] });
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
        // Also invalidate date-specific queries to catch cross-date live matches
        if (filters?.date) {
          queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, filters.date] });
        }
      });

      return unsubscribe;
    }
  }, [filters?.filter, filters?.date, subscribeToLiveMatches, onFixtureUpdate, queryClient]);

  return query;
}

// Hook for live matches specifically
export function useLiveMatches(enabled: boolean = true) {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, subscribeToLiveMatches } = useSocket();

  const query = useQuery({
    queryKey: [QUERY_KEYS.liveFixtures],
    queryFn: () => api.getLiveFixtures(),
    staleTime: CACHE_TIMES.fixtures,
    // ✅ Remove polling - WebSocket provides real-time updates
    refetchInterval: false,
    enabled,
  });

  useEffect(() => {
    if (!enabled) return;

    subscribeToLiveMatches();

    // Listen for bulk live fixtures updates (this is the main event for live matches)
    const unsubscribeLiveFixturesUpdate = onLiveFixturesUpdate((fixtures) => {
      console.log(`Live fixtures update: ${fixtures.length} matches`);

      // Replace all live fixtures data
      queryClient.setQueryData([QUERY_KEYS.liveFixtures], fixtures);
    });

    // Listen for individual fixture updates (backup/specific fixture updates)
    const unsubscribeFixtureUpdate = onFixtureUpdate((data) => {
      console.log(`Individual fixture update: ${data.fixtureId}`);

      queryClient.setQueryData([QUERY_KEYS.liveFixtures], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;
        return oldData.map(fixture =>
          fixture._id === data.fixtureId ? data.fixture : fixture
        );
      });
    });

    const unsubscribeGoalScored = onGoalScored((data) => {
      console.log('⚽ WebSocket goal event:', {
        fixtureId: data.fixtureId,
        timestamp: new Date().toLocaleTimeString(),
        team: data.team,
        player: data.player
      });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
      // Could add toast notification here
    });

    const unsubscribeRedCard = onRedCard((data) => {
      console.log('🟥 WebSocket red card event:', {
        fixtureId: data.fixtureId,
        timestamp: new Date().toLocaleTimeString(),
        team: data.team,
        player: data.player
      });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
      // Could add toast notification here
    });

    const unsubscribeStatusChange = onStatusChange((data) => {
      console.log('📊 WebSocket status change:', {
        fixtureId: data.fixtureId,
        timestamp: new Date().toLocaleTimeString(),
        status: data.status,
        elapsed: data.elapsed
      });

      // Update the specific fixture in live fixtures cache immediately
      queryClient.setQueryData([QUERY_KEYS.liveFixtures], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;

        // If the match finished, remove it from live fixtures
        if (['FT', 'AET', 'PEN'].includes(data.status)) {
          return oldData.filter(fixture => fixture._id !== data.fixtureId);
        }

        // Otherwise, update the fixture status
        return oldData.map(fixture => {
          if (fixture._id === data.fixtureId) {
            return {
              ...fixture,
              fixture: {
                ...fixture.fixture,
                status: {
                  ...fixture.fixture.status,
                  short: data.status,
                  elapsed: data.elapsed || fixture.fixture.status.elapsed
                }
              }
            };
          }
          return fixture;
        });
      });

      // Also invalidate as fallback
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    // Cleanup all subscriptions
    return () => {
      unsubscribeLiveFixturesUpdate();
      unsubscribeFixtureUpdate();
      unsubscribeGoalScored();
      unsubscribeRedCard();
      unsubscribeStatusChange();
    };
  }, [enabled, subscribeToLiveMatches, onLiveFixturesUpdate, onFixtureUpdate, onGoalScored, onRedCard, onStatusChange, queryClient]);

  return query;
}

// Hook for date-specific matches only (no live data to avoid duplicates)
export function useMatchesByDateOnly(date: string, enabled: boolean = true) {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, subscribeToLiveMatches } = useSocket();

  const query = useQuery({
    queryKey: [QUERY_KEYS.fixturesByDate, date],
    queryFn: () => api.getFixturesByDate(date),
    staleTime: CACHE_TIMES.static,
    enabled,
  });

  // Subscribe to live updates for this date
  useEffect(() => {
    if (!enabled) return;

    subscribeToLiveMatches();

    // Listen for individual fixture updates
    const unsubscribeFixtureUpdate = onFixtureUpdate(() => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
    });

    // Listen for bulk live fixtures updates to catch real-time score/time updates
    const unsubscribeLiveFixturesUpdate = onLiveFixturesUpdate((liveFixtures) => {
      console.log(`Live fixtures update for date ${date}: ${liveFixtures.length} live matches`);

      // Update the date-specific query data by merging live fixture updates
      queryClient.setQueryData([QUERY_KEYS.fixturesByDate, date], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;

        // Create a map of live fixtures for quick lookup
        const liveFixturesMap = new Map(liveFixtures.map(fixture => [fixture._id, fixture]));

        // Track fixtures that were live but are no longer in the live list
        const previouslyLiveFixtures: number[] = [];

        // Update existing fixtures with live data, keeping non-live fixtures unchanged
        const updatedData = oldData.map(fixture => {
          const liveUpdate = liveFixturesMap.get(fixture._id);

          if (liveUpdate) {
            return liveUpdate; // Update with live data
          }

          // Check if this fixture was previously live but is no longer in live updates
          if (isLiveMatch(fixture.fixture.status.short)) {
            previouslyLiveFixtures.push(fixture._id);
          }

          return fixture;
        });

        // If we found fixtures that were live but are no longer in live updates,
        // they might have finished - trigger a refetch to get their final status
        if (previouslyLiveFixtures.length > 0) {
          console.log(`🔄 Detected ${previouslyLiveFixtures.length} fixtures that may have finished:`, previouslyLiveFixtures);
          setTimeout(() => {
            queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
          }, 2000); // Small delay to allow backend to process
        }

        return updatedData;
      });
    });

    // Listen for goal events
    const unsubscribeGoalScored = onGoalScored((data) => {
      console.log('⚽ Goal event for date query:', {
        fixtureId: data.fixtureId,
        date,
        timestamp: new Date().toLocaleTimeString(),
        team: data.team,
        player: data.player
      });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
    });

    // Listen for red card events
    const unsubscribeRedCard = onRedCard((data) => {
      console.log('🟥 Red card event for date query:', {
        fixtureId: data.fixtureId,
        date,
        timestamp: new Date().toLocaleTimeString(),
        team: data.team,
        player: data.player
      });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
    });

    // Listen for status change events
    const unsubscribeStatusChange = onStatusChange((data) => {
      console.log('📊 Status change event for date query:', {
        fixtureId: data.fixtureId,
        date,
        timestamp: new Date().toLocaleTimeString(),
        status: data.status,
        elapsed: data.elapsed
      });

      // Update the specific fixture in cache immediately
      queryClient.setQueryData([QUERY_KEYS.fixturesByDate, date], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;

        return oldData.map(fixture => {
          if (fixture._id === data.fixtureId) {
            return {
              ...fixture,
              fixture: {
                ...fixture.fixture,
                status: {
                  ...fixture.fixture.status,
                  short: data.status,
                  elapsed: data.elapsed || fixture.fixture.status.elapsed
                }
              }
            };
          }
          return fixture;
        });
      });

      // Also invalidate as fallback to ensure consistency
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
    });

    return () => {
      unsubscribeFixtureUpdate();
      unsubscribeLiveFixturesUpdate();
      unsubscribeGoalScored();
      unsubscribeRedCard();
      unsubscribeStatusChange();
    };
  }, [enabled, date, subscribeToLiveMatches, onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, queryClient]);

  return query;
}

// Hook for matches by date (includes both date and live fixtures for compatibility)
export function useMatchesByDate(date: string) {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, subscribeToLiveMatches } = useSocket();

  // Get date-specific fixtures
  const dateQuery = useQuery({
    queryKey: [QUERY_KEYS.fixturesByDate, date],
    queryFn: () => api.getFixturesByDate(date),
    staleTime: CACHE_TIMES.static,
  });

  // Get live fixtures to merge with date fixtures
  const liveQuery = useQuery({
    queryKey: [QUERY_KEYS.liveFixtures],
    queryFn: () => api.getLiveFixtures(),
    staleTime: CACHE_TIMES.fixtures,
  });

  // Merge date fixtures with live fixtures, removing duplicates
  const mergedQuery = useMemo(() => {
    if (dateQuery.isLoading || liveQuery.isLoading) {
      return { ...dateQuery, isLoading: true };
    }

    if (dateQuery.isError) {
      return dateQuery;
    }

    const dateFixtures = dateQuery.data || [];
    const liveFixtures = liveQuery.data || [];

    // Create a map of existing fixture IDs from date query
    const dateFixtureIds = new Set(dateFixtures.map(f => f._id));

    // Add live fixtures that aren't already in the date fixtures
    // This catches live games that started on different dates
    const additionalLiveFixtures = liveFixtures.filter(liveFixture =>
      !dateFixtureIds.has(liveFixture._id)
    );

    const mergedData = [...dateFixtures, ...additionalLiveFixtures];

    return {
      ...dateQuery,
      data: mergedData,
      isLoading: false,
    };
  }, [dateQuery, liveQuery]);

  const query = mergedQuery;

  // Subscribe to live updates for cross-date live matches
  useEffect(() => {
    // Subscribe to live matches to catch games that started on different dates
    subscribeToLiveMatches();

    // Listen for any fixture updates and invalidate this date's cache
    const unsubscribeFixtureUpdate = onFixtureUpdate(() => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    const unsubscribeLiveFixturesUpdate = onLiveFixturesUpdate(() => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    const unsubscribeGoalScored = onGoalScored(() => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    const unsubscribeRedCard = onRedCard(() => {
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    const unsubscribeStatusChange = onStatusChange((data) => {
      console.log('📊 Status change event for merged date query:', {
        fixtureId: data.fixtureId,
        date,
        timestamp: new Date().toLocaleTimeString(),
        status: data.status,
        elapsed: data.elapsed
      });

      // Update both date-specific and live fixtures caches
      queryClient.setQueryData([QUERY_KEYS.fixturesByDate, date], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;

        return oldData.map(fixture => {
          if (fixture._id === data.fixtureId) {
            return {
              ...fixture,
              fixture: {
                ...fixture.fixture,
                status: {
                  ...fixture.fixture.status,
                  short: data.status,
                  elapsed: data.elapsed || fixture.fixture.status.elapsed
                }
              }
            };
          }
          return fixture;
        });
      });

      // Update live fixtures cache as well
      queryClient.setQueryData([QUERY_KEYS.liveFixtures], (oldData: Fixture[] | undefined) => {
        if (!oldData) return oldData;

        // If the match finished, remove it from live fixtures
        if (['FT', 'AET', 'PEN'].includes(data.status)) {
          return oldData.filter(fixture => fixture._id !== data.fixtureId);
        }

        // Otherwise, update the fixture status
        return oldData.map(fixture => {
          if (fixture._id === data.fixtureId) {
            return {
              ...fixture,
              fixture: {
                ...fixture.fixture,
                status: {
                  ...fixture.fixture.status,
                  short: data.status,
                  elapsed: data.elapsed || fixture.fixture.status.elapsed
                }
              }
            };
          }
          return fixture;
        });
      });

      // Invalidate as fallback
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, date] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });
    });

    // Cleanup all subscriptions
    return () => {
      unsubscribeFixtureUpdate();
      unsubscribeLiveFixturesUpdate();
      unsubscribeGoalScored();
      unsubscribeRedCard();
      unsubscribeStatusChange();
    };
  }, [date, subscribeToLiveMatches, onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, queryClient]);

  return query;
}

// Hook for today's matches
export function useTodayMatches() {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, onLiveFixturesUpdate, subscribeToLiveMatches } = useSocket();
  const today = formatDate(new Date());
  
  const query = useMatchesByDate(today);
  
  // Subscribe to live updates for today's matches
  useEffect(() => {
    subscribeToLiveMatches();

    // Listen for individual fixture updates and invalidate cache
    const unsubscribeFixtureUpdate = onFixtureUpdate(() => {
      // Invalidate and refetch immediately
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, today] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });

      // Force refetch to ensure immediate update
      queryClient.refetchQueries({ queryKey: [QUERY_KEYS.fixturesByDate, today] });
    });

    // Listen for bulk live fixtures updates and invalidate cache
    const unsubscribeLiveFixturesUpdate = onLiveFixturesUpdate(() => {
      // Invalidate and refetch immediately
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixturesByDate, today] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures] });
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.liveFixtures] });

      // Force refetch to ensure immediate update
      queryClient.refetchQueries({ queryKey: [QUERY_KEYS.fixturesByDate, today] });
    });

    // Cleanup subscriptions
    return () => {
      unsubscribeFixtureUpdate();
      unsubscribeLiveFixturesUpdate();
    };
  }, [subscribeToLiveMatches, onFixtureUpdate, onLiveFixturesUpdate, queryClient, today]);
  
  return query;
}

// Hook for matches by league
export function useMatchesByLeague(leagueId: number, season: number) {
  const queryClient = useQueryClient();
  const { onFixtureUpdate, subscribeToLeague } = useSocket();

  const query = useQuery({
    queryKey: [QUERY_KEYS.fixturesByLeague, leagueId, season],
    queryFn: () => api.getFixturesByLeague(leagueId, season),
    staleTime: CACHE_TIMES.static,
  });

  useEffect(() => {
    subscribeToLeague(leagueId);

    const unsubscribeFixtureUpdate = onFixtureUpdate(() => {
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.fixturesByLeague, leagueId]
      });
    });

    // Cleanup subscription
    return () => {
      unsubscribeFixtureUpdate();
    };
  }, [leagueId, subscribeToLeague, onFixtureUpdate, queryClient]);

  return query;
}

// Hook for a specific fixture with enhanced real-time updates
export function useFixture(fixtureId: number) {
  const queryClient = useQueryClient();
  const {
    onFixtureUpdate,
    onLiveFixturesUpdate,
    onGoalScored,
    onRedCard,
    onStatusChange,
    subscribeToFixture,
    subscribeToLiveMatches,
    unsubscribeFromFixture
  } = useSocket();

  const query = useQuery({
    queryKey: [QUERY_KEYS.fixtures, fixtureId],
    queryFn: () => api.getFixtureById(fixtureId),
    staleTime: CACHE_TIMES.fixtures,
  });

  useEffect(() => {
    // Subscribe to specific fixture updates
    subscribeToFixture(fixtureId);

    // Also subscribe to live matches to catch bulk updates
    subscribeToLiveMatches();

    // Listen for individual fixture updates
    const unsubscribeFixtureUpdate = onFixtureUpdate((data) => {
      if (data.fixtureId === fixtureId) {
        console.log(`🔄 Fixture ${fixtureId} update received:`, data);

        // Merge with existing data to preserve teamRatings and other fields
        queryClient.setQueryData([QUERY_KEYS.fixtures, fixtureId], (oldData: Fixture | undefined) => {
          if (!oldData) return data.fixture;

          return {
            ...data.fixture,
            // Preserve teamRatings from existing data if not in update
            teamRatings: data.fixture.teamRatings || oldData.teamRatings,
          };
        });
      }
    });

    // Listen for bulk live fixtures updates
    const unsubscribeLiveFixturesUpdate = onLiveFixturesUpdate((liveFixtures) => {
      const updatedFixture = liveFixtures.find(fixture => fixture._id === fixtureId);
      if (updatedFixture) {
        console.log(`🔄 Live fixture ${fixtureId} update from bulk:`, updatedFixture);

        // Merge with existing data to preserve teamRatings and other fields
        queryClient.setQueryData([QUERY_KEYS.fixtures, fixtureId], (oldData: Fixture | undefined) => {
          if (!oldData) return updatedFixture;

          return {
            ...updatedFixture,
            // Preserve teamRatings from existing data if not in update
            teamRatings: updatedFixture.teamRatings || oldData.teamRatings,
          };
        });
      }
    });

    // Listen for goal events
    const unsubscribeGoalScored = onGoalScored((data) => {
      if (data.fixtureId === fixtureId) {
        console.log(`⚽ Goal scored in fixture ${fixtureId}:`, data);
        // Invalidate to refetch latest data
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures, fixtureId] });
      }
    });

    // Listen for red card events
    const unsubscribeRedCard = onRedCard((data) => {
      if (data.fixtureId === fixtureId) {
        console.log(`🟥 Red card in fixture ${fixtureId}:`, data);
        // Invalidate to refetch latest data
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures, fixtureId] });
      }
    });

    // Listen for status change events
    const unsubscribeStatusChange = onStatusChange((data) => {
      if (data.fixtureId === fixtureId) {
        console.log(`📊 Status change in fixture ${fixtureId}:`, data);

        // Update the fixture in cache immediately
        queryClient.setQueryData([QUERY_KEYS.fixtures, fixtureId], (oldData: Fixture | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            fixture: {
              ...oldData.fixture,
              status: {
                ...oldData.fixture.status,
                short: data.status,
                elapsed: data.elapsed || oldData.fixture.status.elapsed
              }
            }
          };
        });

        // Also invalidate to refetch latest data as fallback
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.fixtures, fixtureId] });
      }
    });

    return () => {
      unsubscribeFromFixture(fixtureId);
      unsubscribeFixtureUpdate();
      unsubscribeLiveFixturesUpdate();
      unsubscribeGoalScored();
      unsubscribeRedCard();
      unsubscribeStatusChange();
    };
  }, [fixtureId, subscribeToFixture, subscribeToLiveMatches, unsubscribeFromFixture, onFixtureUpdate, onLiveFixturesUpdate, onGoalScored, onRedCard, onStatusChange, queryClient]);

  return query;
}

// Hook for fetching fixture odds
export function useFixtureOdds(fixtureId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.odds, fixtureId],
    queryFn: () => api.getOdds(fixtureId),
    staleTime: CACHE_TIMES.static,
    enabled: !!fixtureId,
  });
}

// Hook for fetching fixture predictions
export function useFixturePredictions(fixtureId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.predictions, fixtureId],
    queryFn: () => api.getPredictions(fixtureId),
    staleTime: CACHE_TIMES.static,
    enabled: !!fixtureId,
  });
}

// Hook for fetching head-to-head data
export function useHeadToHead(team1Id: number, team2Id: number, last: number = 10) {
  return useQuery({
    queryKey: [QUERY_KEYS.headToHead, team1Id, team2Id, last],
    queryFn: () => api.getHeadToHeadLimited(team1Id, team2Id, last),
    staleTime: CACHE_TIMES.static,
    enabled: !!(team1Id && team2Id),
  });
}

// Hook for fetching fixture statistics
export function useFixtureStatistics(fixtureId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.fixtureStats, fixtureId],
    queryFn: () => api.getFixtureStatistics(fixtureId),
    staleTime: CACHE_TIMES.static,
    enabled: !!fixtureId,
  });
}

// Hook for fetching league standings for fixture teams
export function useLeagueStandings(leagueId: number, season: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: [QUERY_KEYS.standings, leagueId, season],
    queryFn: () => api.getStandings(leagueId, season),
    staleTime: CACHE_TIMES.static,
    enabled: options?.enabled !== undefined ? options.enabled : !!(leagueId && season),
  });
}

// Hook for fetching team statistics
export function useTeamStatistics(teamId: number, leagueId: number, season: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: ['teamStatistics', teamId, leagueId, season],
    queryFn: () => api.getTeamStatistics(teamId, leagueId, season),
    staleTime: CACHE_TIMES.static,
    enabled: options?.enabled !== undefined ? options.enabled : !!(teamId && leagueId && season),
  });
}

// Hook for fetching team recent fixtures
export function useTeamRecentFixtures(teamId: number, last: number = 5, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: ['teamRecentFixtures', teamId, last],
    queryFn: () => api.getTeamRecentFixtures(teamId, last),
    staleTime: CACHE_TIMES.fixtures,
    enabled: options?.enabled !== undefined ? options.enabled : !!teamId,
  });
}

// Hook for fetching next fixture for a team
export function useNextFixtureForTeam(teamId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.fixtures, 'next', teamId],
    queryFn: () => api.getNextFixtureForTeam(teamId),
    staleTime: CACHE_TIMES.static,
    enabled: !!teamId,
  });
}

// Hook for fetching bulk next fixtures for a league (more efficient)
export function useNextFixturesForLeague(leagueId: number, season: number, limit: number = 20) {
  return useQuery({
    queryKey: [QUERY_KEYS.fixtures, 'next-league', leagueId, season, limit],
    queryFn: () => api.getNextFixturesForLeague(leagueId, season, limit),
    staleTime: CACHE_TIMES.static,
    enabled: !!(leagueId && season),
  });
}

// Hook for fetching venue details
export function useVenue(venueId: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: [QUERY_KEYS.venue, venueId],
    queryFn: () => api.getVenue(venueId),
    staleTime: CACHE_TIMES.static,
    enabled: options?.enabled !== false && !!venueId,
  });
}
