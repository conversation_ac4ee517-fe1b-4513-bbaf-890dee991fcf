'use client';

import { useState, useEffect, useCallback } from 'react';
import { TeamRatings, SocketTeamRatingUpdate } from '@/lib/types';
import { socketManager } from '@/lib/socket';

interface UseTeamRatingsOptions {
  fixtureId: number;
  initialTeamRatings?: TeamRatings;
  enableRealTimeUpdates?: boolean;
}

interface UseTeamRatingsReturn {
  teamRatings: TeamRatings | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  fetchTeamRatings: () => Promise<void>;
}

export const useTeamRatings = ({
  fixtureId,
  initialTeamRatings,
  enableRealTimeUpdates = true
}: UseTeamRatingsOptions): UseTeamRatingsReturn => {
  const [teamRatings, setTeamRatings] = useState<TeamRatings | null>(initialTeamRatings || null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch team ratings from API
  const fetchTeamRatings = useCallback(async () => {
    if (!fixtureId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/fixtures?id=${fixtureId}&includeTeamRatings=true`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch team ratings: ${response.statusText}`);
      }

      const fixtureData = await response.json();
      
      if (fixtureData.teamRatings) {
        setTeamRatings(fixtureData.teamRatings);
        setLastUpdated(new Date());
      } else {
        setTeamRatings(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch team ratings';
      setError(errorMessage);
      console.error('Error fetching team ratings:', err);
    } finally {
      setIsLoading(false);
    }
  }, [fixtureId]);

  // Handle WebSocket team rating updates
  const handleTeamRatingUpdate = useCallback((data: SocketTeamRatingUpdate) => {
    if (data.fixtureId === fixtureId && data.teamRatings) {
      setTeamRatings(data.teamRatings);
      setLastUpdated(new Date());
      console.log(`Team ratings updated for fixture ${fixtureId}:`, data.teamRatings);
    }
  }, [fixtureId]);

  // Setup WebSocket connection for real-time updates
  useEffect(() => {
    if (!enableRealTimeUpdates || !fixtureId) return;

    const fixtureSocket = socketManager.initializeFixtureSocket();

    // Subscribe to fixture updates
    fixtureSocket.emit('subscribe-fixture', fixtureId);

    // Listen for team rating updates
    fixtureSocket.on('team-rating-update', handleTeamRatingUpdate);

    // Cleanup function
    return () => {
      fixtureSocket.off('team-rating-update', handleTeamRatingUpdate);
      fixtureSocket.emit('unsubscribe-fixture', fixtureId);
    };
  }, [fixtureId, enableRealTimeUpdates, handleTeamRatingUpdate]);

  // Set initial team ratings if provided
  useEffect(() => {
    if (initialTeamRatings && !teamRatings) {
      setTeamRatings(initialTeamRatings);
      setLastUpdated(new Date());
    }
  }, [initialTeamRatings, teamRatings]);

  return {
    teamRatings,
    isLoading,
    error,
    lastUpdated,
    fetchTeamRatings
  };
};
