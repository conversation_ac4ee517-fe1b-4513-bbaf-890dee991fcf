'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  initializeGoogleOAuth,
  signInWithGoogle
} from '@/lib/oauth';
import { toast } from 'sonner';

export function useOAuth() {
  const [isGoogleReady, setIsGoogleReady] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const { loginWithOAuth } = useAuth();

  // Initialize OAuth providers on mount
  useEffect(() => {
    const initProviders = async () => {
      // Initialize Google OAuth
      try {
        await initializeGoogleOAuth();
        setIsGoogleReady(true);
      } catch (error) {
        console.error('Failed to initialize Google OAuth:', error);
      }
    };

    initProviders();
  }, []);

  const signInWithGoogleOAuth = async () => {
    if (!isGoogleReady) {
      toast.error('Google login is not available');
      return;
    }

    setIsGoogleLoading(true);
    try {
      const result = await signInWithGoogle();
      await loginWithOAuth({
        provider: 'google',
        accessToken: result.accessToken,
        profile: result.profile,
      });
      return result;
    } catch (error) {
      console.error('Google login error:', error);
      toast.error('Failed to login with Google');
      throw error;
    } finally {
      setIsGoogleLoading(false);
    }
  };



  return {
    // State
    isGoogleReady,
    isGoogleLoading,
    isLoading: isGoogleLoading,

    // Actions
    signInWithGoogle: signInWithGoogleOAuth,
  };
}
