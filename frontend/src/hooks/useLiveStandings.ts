'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Socket } from 'socket.io-client';
import { socketManager } from '@/lib/socket';
import { Standing } from '@/lib/types';

// Interface for live standing adjustments (matches backend)
export interface LiveStandingAdjustment {
  teamId: number;
  goalsFor: number;
  goalsAgainst: number;
  points: number;
  wins: number;
  draws: number;
  losses: number;
  matchesPlayed: number;
  liveMatches: LiveMatchInfo[];
  goalsDiff: number;
}

// Interface for live match info (matches backend)
export interface LiveMatchInfo {
  fixtureId: number;
  opponent: number;
  homeAway: 'home' | 'away';
  currentScore: { home: number; away: number };
  originalScore: { home: number; away: number };
  status: string;
  minute: number;
  isFinished: boolean;
}

// Interface for live standings update from WebSocket
export interface LiveStandingsUpdate {
  leagueId: number;
  season: number;
  adjustments: { [key: number]: LiveStandingAdjustment };
  timestamp: string;
}

// Interface for enhanced standing with live data
export interface LiveStanding extends Standing {
  isLive?: boolean;
  liveMatches?: LiveMatchInfo[];
  originalPosition?: number;
  positionChange?: number;
}

// Hook options
export interface UseLiveStandingsOptions {
  leagueId: number;
  season: number;
  baseStandings: Standing[][];
  enableLiveUpdates?: boolean;
}

// Hook return type
export interface UseLiveStandingsReturn {
  liveStandings: Standing[][];
  isConnected: boolean;
  lastUpdated: Date | null;
  liveTeams: Set<number>;
  error: string | null;
  reconnect: () => void;
}

/**
 * Hook to manage live standings with real-time updates
 */
export function useLiveStandings({
  leagueId,
  season,
  baseStandings,
  enableLiveUpdates = true
}: UseLiveStandingsOptions): UseLiveStandingsReturn {
  const [liveAdjustments, setLiveAdjustments] = useState<Map<number, LiveStandingAdjustment>>(new Map());
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [error, setError] = useState<string | null>(null);
  const socketRef = useRef<Socket | null>(null);

  /**
   * Apply live adjustments to base standings
   */
  const applyLiveAdjustments = useCallback((
    standings: Standing[][],
    adjustments: Map<number, LiveStandingAdjustment>
  ): Standing[][] => {
    if (adjustments.size === 0) {
      return standings;
    }

    return standings.map(group => {
      // Apply adjustments to each team in the group
      const adjustedStandings = group.map((standing, index) => {
        const adjustment = adjustments.get(standing.team.id);
        
        if (!adjustment) {
          return {
            ...standing,
            originalPosition: index + 1
          } as LiveStanding;
        }

        // Apply live adjustments
        const adjustedStanding: LiveStanding = {
          ...standing,
          points: standing.points + adjustment.points,
          goalsDiff: standing.goalsDiff + adjustment.goalsDiff,
          all: {
            ...standing.all,
            played: standing.all.played + adjustment.matchesPlayed,
            win: standing.all.win + adjustment.wins,
            draw: standing.all.draw + adjustment.draws,
            lose: standing.all.lose + adjustment.losses,
            goals: {
              for: standing.all.goals.for + adjustment.goalsFor,
              against: standing.all.goals.against + adjustment.goalsAgainst
            }
          },
          isLive: adjustment.liveMatches.length > 0,
          liveMatches: adjustment.liveMatches,
          originalPosition: index + 1
        };

        return adjustedStanding;
      });

      // Sort by points, then goal difference, then goals for
      adjustedStandings.sort((a, b) => {
        if (a.points !== b.points) {
          return b.points - a.points;
        }
        if (a.goalsDiff !== b.goalsDiff) {
          return b.goalsDiff - a.goalsDiff;
        }
        return b.all.goals.for - a.all.goals.for;
      });

      // Update ranks and calculate position changes
      return adjustedStandings.map((standing, newIndex) => {
        const liveStanding = standing as LiveStanding;
        const newPosition = newIndex + 1;
        const originalPosition = liveStanding.originalPosition || newPosition;
        
        return {
          ...liveStanding,
          rank: newPosition,
          positionChange: originalPosition - newPosition
        };
      });
    });
  }, []);

  /**
   * Handle live standings update from WebSocket
   */
  const handleLiveStandingsUpdate = useCallback((update: LiveStandingsUpdate) => {
    try {
      if (update.leagueId !== leagueId || update.season !== season) {
        return; // Not for this league/season
      }

      // Convert adjustments object to Map
      const adjustmentsMap = new Map<number, LiveStandingAdjustment>();
      Object.entries(update.adjustments).forEach(([teamId, adjustment]) => {
        adjustmentsMap.set(parseInt(teamId), adjustment);
      });

      setLiveAdjustments(adjustmentsMap);
      setLastUpdated(new Date(update.timestamp));
      setError(null);

      console.log(`Live standings updated for league ${leagueId}:`, adjustmentsMap.size, 'teams affected');
    } catch (err) {
      console.error('Error handling live standings update:', err);
      setError('Failed to process live standings update');
    }
  }, [leagueId, season]);

  /**
   * Initialize WebSocket connection
   */
  const initializeSocket = useCallback(() => {
    if (!enableLiveUpdates) {
      return;
    }

    try {
      const socket = socketManager.initializeFixtureSocket();
      socketRef.current = socket;

      // Subscribe to league updates
      socket.emit('subscribe-league', leagueId);

      // Listen for live standings updates
      socket.on('live-standings-update', handleLiveStandingsUpdate);

      // Handle connection events
      socket.on('connect', () => {
        setIsConnected(true);
        setError(null);
        console.log('Connected to live standings updates');
      });

      socket.on('disconnect', () => {
        setIsConnected(false);
        console.log('Disconnected from live standings updates');
      });

      socket.on('connect_error', (err: Error) => {
        setIsConnected(false);
        setError(`Connection error: ${err.message}`);
        console.error('Live standings connection error:', err);
      });

      socket.on('subscription-success', (data: { type: string; id: number }) => {
        if (data.type === 'league' && data.id === leagueId) {
          console.log(`Successfully subscribed to live standings for league ${leagueId}`);
        }
      });

      socket.on('subscription-error', (data: { message: string }) => {
        setError(`Subscription error: ${data.message}`);
        console.error('Live standings subscription error:', data.message);
      });

    } catch (err) {
      console.error('Error initializing live standings socket:', err);
      setError('Failed to initialize live standings connection');
    }
  }, [leagueId, enableLiveUpdates, handleLiveStandingsUpdate]);

  /**
   * Cleanup WebSocket connection
   */
  const cleanupSocket = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.off('live-standings-update', handleLiveStandingsUpdate);
      socketRef.current.emit('unsubscribe-league', leagueId);
      socketRef.current = null;
    }
  }, [leagueId, handleLiveStandingsUpdate]);

  /**
   * Reconnect to WebSocket
   */
  const reconnect = useCallback(() => {
    cleanupSocket();
    setTimeout(() => {
      initializeSocket();
    }, 1000);
  }, [cleanupSocket, initializeSocket]);

  // Initialize socket on mount and cleanup on unmount
  useEffect(() => {
    initializeSocket();
    return cleanupSocket;
  }, [initializeSocket, cleanupSocket]);

  // Calculate live standings by applying adjustments to base standings
  const liveStandings = applyLiveAdjustments(baseStandings, liveAdjustments);

  // Get set of teams currently playing live matches
  const liveTeams = new Set<number>();
  liveAdjustments.forEach((adjustment, teamId) => {
    if (adjustment.liveMatches.length > 0) {
      liveTeams.add(teamId);
    }
  });

  return {
    liveStandings,
    isConnected,
    lastUpdated,
    liveTeams,
    error,
    reconnect
  };
}
