'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useSocket } from './useSocket';
import { useAuth } from '@/contexts/AuthContext';
import { ChatMessage } from '@/lib/socket';
import { api } from '@/lib/api';

interface UseFixtureChatProps {
  fixtureId: number;
}

export function useFixtureChat({ fixtureId }: UseFixtureChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [isJoined, setIsJoined] = useState(false);

  const { 
    chatSocket, 
    initializeChatSocket, 
    joinFixtureChat, 
    sendMessage: socketSendMessage,
    onMessage 
  } = useSocket();
  const { isAuthenticated, token } = useAuth();
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = useCallback((force: boolean = false) => {
    if (messagesEndRef.current) {
      // Try to find the scroll area viewport (Radix UI ScrollArea)
      let scrollArea = messagesEndRef.current.closest('[data-radix-scroll-area-viewport]');

      // Fallback: look for our custom data attribute
      if (!scrollArea) {
        const chatScrollArea = messagesEndRef.current.closest('[data-chat-scroll-area]');
        if (chatScrollArea) {
          scrollArea = chatScrollArea.querySelector('[data-radix-scroll-area-viewport]');
        }
      }

      if (scrollArea) {
        // Only auto-scroll if user is near the bottom or if forced (like initial load)
        const isNearBottom = force || (scrollArea.scrollTop + scrollArea.clientHeight >= scrollArea.scrollHeight - 100);

        if (isNearBottom) {
          // Scroll within the ScrollArea component only, no page scroll
          scrollArea.scrollTop = scrollArea.scrollHeight;
        }
      }
    }
  }, []);

  // Load message history
  const loadMessageHistory = useCallback(async () => {
    if (!isAuthenticated || !token || !fixtureId) return;

    try {
      const response = await api.getFixtureMessages(fixtureId, 50);

      // Check if response has the expected structure
      if (!response || !Array.isArray(response.messages)) {
        console.error('Invalid response structure:', response);
        setMessages([]);
        setIsChatOpen(false);
        return;
      }

      // Convert string dates to Date objects and reverse to show oldest first
      const messagesWithDates: ChatMessage[] = response.messages
        .map(msg => ({
          ...msg,
          createdAt: new Date(msg.createdAt)
        }))
        .reverse(); // API returns newest first, we want oldest first for chat

      setMessages(messagesWithDates);
      setIsChatOpen(response.chatOpen || false);

      // Auto-scroll to bottom after loading history (force scroll for initial load)
      if (messagesWithDates.length > 0) {
        setTimeout(() => scrollToBottom(true), 50);
      }
    } catch (error) {
      console.error('Failed to load message history:', error);
      // Set empty messages array on error
      setMessages([]);
      setIsChatOpen(false);
    }
  }, [isAuthenticated, token, fixtureId, scrollToBottom]);

  // Initialize chat socket and join fixture room
  useEffect(() => {
    if (!isAuthenticated || !token || !fixtureId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    // Initialize chat socket
    initializeChatSocket();

    // Wait for socket to be available
    const checkSocket = () => {
      if (chatSocket) {
        // Set up socket event listeners
        chatSocket.on('connect', () => {
          setIsConnected(true);
          setError(null);
          // Join the fixture chat room
          joinFixtureChat(fixtureId);
        });

        chatSocket.on('disconnect', () => {
          setIsConnected(false);
          setIsJoined(false);
        });

        chatSocket.on('joined', (data: { fixtureId: number }) => {
          if (data.fixtureId === fixtureId) {
            setIsJoined(true);
            setIsChatOpen(true);
            setIsLoading(false);
            // Load message history after successfully joining
            loadMessageHistory();
          }
        });

        chatSocket.on('error', (data: { message: string }) => {
          setError(data.message);
          setIsLoading(false);
          if (data.message.includes('Chat is closed')) {
            setIsChatOpen(false);
          }
        });

        chatSocket.on('chat-closed', (data: { fixtureId: number }) => {
          if (data.fixtureId === fixtureId) {
            setIsChatOpen(false);
            setError('Chat has been closed for this fixture');
          }
        });

        chatSocket.on('chat-opened', (data: { fixtureId: number }) => {
          if (data.fixtureId === fixtureId) {
            setIsChatOpen(true);
            setError(null);
          }
        });

        // If already connected, join immediately
        if (chatSocket.connected) {
          setIsConnected(true);
          joinFixtureChat(fixtureId);
        }
      } else {
        // Retry after a short delay
        setTimeout(checkSocket, 100);
      }
    };

    checkSocket();

    return () => {
      if (chatSocket) {
        chatSocket.off('connect');
        chatSocket.off('disconnect');
        chatSocket.off('joined');
        chatSocket.off('error');
        chatSocket.off('chat-closed');
        chatSocket.off('chat-opened');
      }
    };
  }, [isAuthenticated, token, fixtureId, chatSocket, initializeChatSocket, joinFixtureChat, loadMessageHistory]);

  // Listen for new messages
  useEffect(() => {
    if (!chatSocket || !isJoined) return;

    const unsubscribe = onMessage((message: ChatMessage) => {
      if (message.fixtureId === fixtureId) {
        setMessages(prev => {
          // Avoid duplicates
          if (prev.some(m => m._id === message._id)) {
            return prev;
          }
          return [...prev, message];
        });
        // Auto-scroll to bottom after a short delay to ensure DOM is updated
        setTimeout(scrollToBottom, 50);
      }
    });

    return unsubscribe;
  }, [chatSocket, isJoined, fixtureId, onMessage, scrollToBottom]);

  // Send message function
  const sendMessage = useCallback(async () => {
    if (!messageInput.trim() || !isConnected || !isJoined) return;

    try {
      socketSendMessage(messageInput.trim());
      setMessageInput('');
    } catch {
      setError('Failed to send message');
    }
  }, [messageInput, isConnected, isJoined, socketSendMessage]);

  // Handle Enter key press
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  }, [sendMessage]);

  return {
    messages,
    isConnected,
    isChatOpen,
    isLoading,
    error,
    messageInput,
    setMessageInput,
    sendMessage,
    handleKeyPress,
    messagesEndRef,
    isAuthenticated,
    isJoined
  };
}
