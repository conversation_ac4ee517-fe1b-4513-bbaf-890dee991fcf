'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { TipWithDetails, TipType, TipDetails } from '@/lib/types';
import { api } from '@/lib/api';
import { toast } from 'sonner';

// Query keys for React Query
export const TIPS_QUERY_KEYS = {
  fixtureTips: (fixtureId: number) => ['tips', 'fixture', fixtureId],
  userTips: () => ['tips', 'user'],
  userStats: () => ['tips', 'user', 'stats'],
  tipsterRanking: (limit?: number) => ['tips', 'ranking', limit],
} as const;

// Cache times
const CACHE_TIMES = {
  tips: 1000 * 60 * 2, // 2 minutes for tips
  stats: 1000 * 60 * 5, // 5 minutes for stats
  ranking: 1000 * 60 * 10, // 10 minutes for ranking
} as const;

// Hook for fetching tips for a specific fixture
export function useFixtureTips(fixtureId: number) {
  return useQuery({
    queryKey: TIPS_QUERY_KEYS.fixtureTips(fixtureId),
    queryFn: () => api.getFixtureTips(fixtureId),
    staleTime: CACHE_TIMES.tips,
    enabled: !!fixtureId,
  });
}

// Hook for fetching user's tips
export function useUserTips() {
  return useQuery({
    queryKey: TIPS_QUERY_KEYS.userTips(),
    queryFn: () => api.getUserTips(),
    staleTime: CACHE_TIMES.tips,
  });
}

// Hook for fetching user's tipster stats
export function useUserTipsterStats() {
  return useQuery({
    queryKey: TIPS_QUERY_KEYS.userStats(),
    queryFn: () => api.getUserTipsterStats(),
    staleTime: CACHE_TIMES.stats,
  });
}

// Hook for fetching tipster ranking
export function useTipsterRanking(limit?: number) {
  return useQuery({
    queryKey: TIPS_QUERY_KEYS.tipsterRanking(limit),
    queryFn: () => api.getTipsterRanking(limit),
    staleTime: CACHE_TIMES.ranking,
  });
}

// Hook for creating tips
export function useCreateTip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tipData: {
      fixtureId: number;
      tipType: TipType;
      details: TipDetails;
      odds: number;
      stake?: number;
      description?: string;
      isPublic?: boolean;
    }) => api.createTip(tipData),
    onSuccess: (newTip, variables) => {
      // Invalidate and refetch fixture tips
      queryClient.invalidateQueries({
        queryKey: TIPS_QUERY_KEYS.fixtureTips(variables.fixtureId)
      });
      
      // Invalidate user tips
      queryClient.invalidateQueries({
        queryKey: TIPS_QUERY_KEYS.userTips()
      });
      
      // Invalidate user stats
      queryClient.invalidateQueries({
        queryKey: TIPS_QUERY_KEYS.userStats()
      });

      toast.success('Tip created successfully!');
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to create tip';
      toast.error(message);
    },
  });
}

// Hook for deleting tips
export function useDeleteTip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (tipId: string) => api.deleteTip(tipId),
    onSuccess: () => {
      // Invalidate all tips queries since we don't know which fixture this tip belongs to
      queryClient.invalidateQueries({
        queryKey: ['tips']
      });

      toast.success('Tip deleted successfully!');
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to delete tip';
      toast.error(message);
    },
  });
}

// Hook for liking/unliking tips
export function useLikeTip() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ tipId, action }: { tipId: string; action: 'like' | 'unlike' }) => 
      api.likeTip(tipId, action),
    onSuccess: (response, variables) => {
      // Update the tip in the cache
      queryClient.setQueriesData(
        { queryKey: ['tips'] },
        (oldData: TipWithDetails[] | undefined) => {
          if (!oldData) return oldData;
          
          return oldData.map(tip => 
            tip._id === variables.tipId 
              ? { ...tip, likes: response.likes }
              : tip
          );
        }
      );
    },
    onError: (error) => {
      const message = error instanceof Error ? error.message : 'Failed to update tip';
      toast.error(message);
    },
  });
}

// Custom hook for managing tips state with real-time updates
export function useTipsManager(fixtureId: number) {
  const [tips, setTips] = useState<TipWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { data: fetchedTips, isLoading, error: queryError } = useFixtureTips(fixtureId);
  const createTipMutation = useCreateTip();
  const deleteTipMutation = useDeleteTip();
  const likeTipMutation = useLikeTip();

  // Update local state when query data changes
  useEffect(() => {
    if (fetchedTips) {
      setTips(fetchedTips);
      setLoading(false);
      setError(null);
    } else if (queryError) {
      setError(queryError instanceof Error ? queryError.message : 'Failed to load tips');
      setLoading(false);
    } else {
      setLoading(isLoading);
    }
  }, [fetchedTips, isLoading, queryError]);

  // Handle new tip creation
  const handleTipCreated = (newTip: TipWithDetails) => {
    setTips(prev => [newTip, ...prev]);
  };

  // Handle tip deletion
  const handleTipDeleted = (tipId: string) => {
    setTips(prev => prev.filter(tip => tip._id !== tipId));
    deleteTipMutation.mutate(tipId);
  };

  // Handle tip like/unlike
  const handleTipLiked = (tipId: string, action: 'like' | 'unlike') => {
    likeTipMutation.mutate({ tipId, action });
  };

  // Calculate stats
  const stats = {
    totalTips: tips.length,
    activeTips: tips.filter(tip => tip.status === 'pending').length,
    avgOdds: tips.length > 0 
      ? (tips.reduce((sum, tip) => sum + tip.odds, 0) / tips.length).toFixed(2)
      : '0.00',
  };

  return {
    tips,
    loading,
    error,
    stats,
    handleTipCreated,
    handleTipDeleted,
    handleTipLiked,
    createTip: createTipMutation.mutate,
    isCreatingTip: createTipMutation.isPending,
    isDeletingTip: deleteTipMutation.isPending,
    isLikingTip: likeTipMutation.isPending,
  };
}

// Hook for socket-based real-time updates
export function useTipsSocket(fixtureId: number) {
  const queryClient = useQueryClient();

  useEffect(() => {
    // This would connect to socket.io for real-time updates
    // For now, we'll implement a simple polling mechanism
    const interval = setInterval(() => {
      queryClient.invalidateQueries({
        queryKey: TIPS_QUERY_KEYS.fixtureTips(fixtureId)
      });
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, [fixtureId, queryClient]);
}
