'use client';

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { api } from '@/lib/api';
import { VoteOption, VoteCategory, UserVotesByCategory } from '@/lib/types';
import { QUERY_KEYS, CACHE_TIMES } from '@/lib/constants';
import { useRef } from 'react';

// Hook for casting a vote with optimistic updates and duplicate prevention
export function useVoting(fixtureId: number) {
  const queryClient = useQueryClient();
  const pendingVotesRef = useRef<Set<string>>(new Set());

  return useMutation({
    mutationFn: async ({ vote, category }: { vote: VoteOption; category: VoteCategory }) => {
      // Create a unique key for this vote to prevent duplicates
      const voteKey = `${fixtureId}-${category}-${vote}`;
      
      // Check if this exact vote is already pending
      if (pendingVotesRef.current.has(voteKey)) {
        throw new Error('Vote already in progress');
      }
      
      // Add to pending votes
      pendingVotesRef.current.add(voteKey);
      
      try {
        const result = await api.castVote(fixtureId, vote, category);
        return { result, vote, category };
      } finally {
        // Remove from pending votes
        pendingVotesRef.current.delete(voteKey);
      }
    },
    onMutate: async ({ vote, category }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: [QUERY_KEYS.userVote, fixtureId] });
      
      // Snapshot the previous value
      const previousUserVote = queryClient.getQueryData<UserVotesByCategory>([QUERY_KEYS.userVote, fixtureId]);
      
      // Optimistically update the user vote
      queryClient.setQueryData<UserVotesByCategory>([QUERY_KEYS.userVote, fixtureId], (old) => {
        return {
          ...old,
          [category]: {
            vote,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        };
      });
      
      return { previousUserVote };
    },
    onSuccess: ({ vote, category }) => {
      // Update with actual server response
      queryClient.setQueryData<UserVotesByCategory>([QUERY_KEYS.userVote, fixtureId], (old) => {
        const now = new Date().toISOString();
        return {
          ...old,
          [category]: {
            vote,
            createdAt: now,
            updatedAt: now
          }
        };
      });
      
      // Invalidate and refetch vote stats
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.voteStats, fixtureId]
      });

      // Show success message
      console.log('Vote cast successfully!');
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update on error
      if (context?.previousUserVote) {
        queryClient.setQueryData([QUERY_KEYS.userVote, fixtureId], context.previousUserVote);
      }
      
      console.error('Error casting vote:', error);
      console.error('Failed to cast vote. Please try again.');
    },
  });
}

// Hook for fetching vote statistics
export function useVoteStats(fixtureId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.voteStats, fixtureId],
    queryFn: () => api.getVoteStats(fixtureId),
    staleTime: CACHE_TIMES.fixtures, // 15 seconds for live data
    enabled: !!fixtureId,
  });
}

// Hook for fetching user's vote for a specific fixture
export function useUserVote(fixtureId: number) {
  return useQuery({
    queryKey: [QUERY_KEYS.userVote, fixtureId],
    queryFn: () => api.getUserVote(fixtureId),
    staleTime: CACHE_TIMES.static, // 24 hours for user data
    enabled: !!fixtureId,
    retry: false, // Don't retry if user hasn't voted
  });
}

// Hook for fetching batch vote statistics
export function useBatchVoteStats(fixtureIds: number[]) {
  return useQuery({
    queryKey: [QUERY_KEYS.batchVoteStats, ...fixtureIds.sort()],
    queryFn: () => api.getBatchVoteStats(fixtureIds),
    staleTime: CACHE_TIMES.fixtures, // 15 seconds for live data
    enabled: fixtureIds.length > 0,
  });
}

// Hook for fetching user's total vote count
export function useUserVoteCount() {
  return useQuery({
    queryKey: [QUERY_KEYS.userVoteCount],
    queryFn: () => api.getUserVoteCount(),
    staleTime: CACHE_TIMES.static, // 24 hours
  });
}

// Hook for fetching user's votes by date
export function useUserVotesByDate(date: string) {
  return useQuery({
    queryKey: [QUERY_KEYS.userVotesByDate, date],
    queryFn: () => api.getUserVotesByDate(date),
    staleTime: CACHE_TIMES.static, // 24 hours
    enabled: !!date,
  });
}

// Hook for fetching all user votes
export function useAllUserVotes() {
  return useQuery({
    queryKey: [QUERY_KEYS.allUserVotes],
    queryFn: () => api.getAllUserVotes(),
    staleTime: CACHE_TIMES.static, // 24 hours
  });
}

// Utility hook for checking if user has voted in a specific category
export function useHasUserVoted(fixtureId: number, category: VoteCategory) {
  const { data: userVotes, isLoading } = useUserVote(fixtureId);

  const categoryVote = userVotes?.[category];

  return {
    hasVoted: !!categoryVote?.vote,
    userVote: categoryVote?.vote,
    isLoading,
  };
}

// Utility hook for checking if user has voted in any category
export function useHasUserVotedAny(fixtureId: number) {
  const { data: userVotes, isLoading } = useUserVote(fixtureId);

  const hasVotedAny = !!(userVotes?.match_outcome || userVotes?.over_under || userVotes?.btts);

  return {
    hasVoted: hasVotedAny,
    userVotes,
    isLoading,
  };
}

// Utility hook for vote percentages by category
export function useVotePercentages(fixtureId: number, category: VoteCategory) {
  const { data: voteStats, isLoading, error } = useVoteStats(fixtureId);

  let percentages: Record<string, number> = {};
  let totalVotes = 0;

  if (voteStats) {
    switch (category) {
      case VoteCategory.MATCH_OUTCOME:
        const matchOutcome = voteStats.match_outcome;
        totalVotes = matchOutcome.totalVotes;
        percentages = {
          home: totalVotes > 0 ? Math.round((matchOutcome.homeVotes / totalVotes) * 100) : 0,
          draw: totalVotes > 0 ? Math.round((matchOutcome.drawVotes / totalVotes) * 100) : 0,
          away: totalVotes > 0 ? Math.round((matchOutcome.awayVotes / totalVotes) * 100) : 0,
        };
        break;

      case VoteCategory.OVER_UNDER:
        const overUnder = voteStats.over_under;
        totalVotes = overUnder.totalVotes;
        percentages = {
          over: totalVotes > 0 ? Math.round((overUnder.over25Votes / totalVotes) * 100) : 0,
          under: totalVotes > 0 ? Math.round((overUnder.under25Votes / totalVotes) * 100) : 0,
        };
        break;

      case VoteCategory.BTTS:
        const btts = voteStats.btts;
        totalVotes = btts.totalVotes;
        percentages = {
          yes: totalVotes > 0 ? Math.round((btts.yesVotes / totalVotes) * 100) : 0,
          no: totalVotes > 0 ? Math.round((btts.noVotes / totalVotes) * 100) : 0,
        };
        break;
    }
  }

  return {
    percentages,
    totalVotes,
    voteStats,
    isLoading,
    error,
  };
}

// Legacy hook for backward compatibility (match outcome only)
export function useMatchOutcomePercentages(fixtureId: number) {
  return useVotePercentages(fixtureId, VoteCategory.MATCH_OUTCOME);
}