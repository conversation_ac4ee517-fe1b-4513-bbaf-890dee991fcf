import { Metadata } from 'next';
import { UserProfilePage } from '@/components/pages/UserProfilePage';

interface Props {
  params: Promise<{
    username: string;
  }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { username } = await params;
  return {
    title: `${username} - KickoffScore`,
    description: `View ${username}'s profile, favorites, and settings on KickoffScore.`,
  };
}

export default async function UserProfile({ params }: Props) {
  const { username } = await params;
  return <UserProfilePage username={username} />;
}
