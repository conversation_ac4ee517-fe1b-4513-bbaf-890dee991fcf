import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { FixtureDetailPage } from '@/components/pages/FixtureDetailPage';
import { api } from '@/lib/api';
import { generateMatchSlug } from '@/lib/utils';

// Force dynamic rendering to avoid build-time API calls
export const dynamic = 'force-dynamic';

// Shared function to validate slug and redirect if necessary
async function validateSlugAndRedirect(slug: string, id: string) {
  const fixtureId = parseInt(id);

  if (isNaN(fixtureId)) {
    notFound();
  }

  let fixture;
  try {
    fixture = await api.getFixtureById(fixtureId);
  } catch (error) {
    console.error('Error fetching fixture for slug validation:', error);
    notFound();
  }

  const correctSlug = generateMatchSlug(fixture.teams.home.name, fixture.teams.away.name);

  if (slug !== correctSlug) {
    redirect(`/matches/${correctSlug}/${id}`);
  }

  return fixture;
}

interface PageProps {
  params: Promise<{
    slug: string;
    id: string;
  }>;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug, id } = await params;

  try {
    // This will validate the slug and redirect if necessary
    const fixture = await validateSlugAndRedirect(slug, id);

    const homeTeam = fixture.teams.home.name;
    const awayTeam = fixture.teams.away.name;
    const homeScore = fixture.goals.home ?? 0;
    const awayScore = fixture.goals.away ?? 0;
    const status = fixture.fixture.status.long;
    const date = new Date(fixture.fixture.date).toLocaleDateString();

    const title = `${homeTeam} vs ${awayTeam} - ${homeScore}-${awayScore} | Live Score`;
    const description = `Follow the live score and updates for ${homeTeam} vs ${awayTeam}. Match status: ${status}. Date: ${date}`;

    return {
      title,
      description,
      openGraph: {
        title,
        description,
        type: 'article',
        images: [
          {
            url: fixture.teams.home.logo || '',
            width: 100,
            height: 100,
            alt: `${homeTeam} logo`,
          },
        ],
      },
      twitter: {
        card: 'summary',
        title,
        description,
      },
    };
  } catch (error) {
    // Check if this is a Next.js redirect error - if so, let it bubble up
    if (error && typeof error === 'object' && 'digest' in error &&
        typeof error.digest === 'string' && error.digest.includes('NEXT_REDIRECT')) {
      throw error; // Re-throw redirect errors
    }

    console.error('Error generating metadata:', error);
    return {
      title: 'Match Details',
      description: 'View live match details and statistics.',
    };
  }
}

export default async function MatchPage({ params }: PageProps) {
  const { slug, id } = await params;
  const fixtureId = parseInt(id);

  // Validate slug and redirect if necessary
  await validateSlugAndRedirect(slug, id);

  return <FixtureDetailPage fixtureId={fixtureId} />;
}
