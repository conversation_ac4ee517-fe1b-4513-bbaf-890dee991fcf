'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { MainLayout } from '@/components/layout/MainLayout';
import { MatchFilters } from '@/components/features/MatchFilters';
import { MatchList } from '@/components/features/MatchList';
import { MatchListSkeleton } from '@/components/features/MatchListSkeleton';
import { NewsSection } from '@/components/features/NewsSection';
import { Card } from '@/components/ui/card';
import { useLiveMatches, useMatchesByDateOnly } from '@/hooks/useMatches';
import { MatchFilter, Fixture, isFinishedMatch, isUpcomingMatch } from '@/lib/types';
import { formatDateSafe, generateMatchUrl } from '@/lib/utils';
import { sortMatchesByLeagueTier } from '@/lib/leagueTiers';

export default function HomePage() {
  const router = useRouter();
  const [activeFilter, setActiveFilter] = useState<MatchFilter>('all');
  const [selectedDate, setSelectedDate] = useState<string>(formatDateSafe(new Date()));
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [byTimeEnabled, setByTimeEnabled] = useState<boolean>(false);

  // Check if selected date is today
  const today = formatDateSafe(new Date());
  const isSelectedDateToday = selectedDate === today;

  // Auto-switch to "All" when date changes and user is on filtered tabs
  useEffect(() => {
    // If user changes date and is on live/finished/upcoming, switch to "All"
    if (!isSelectedDateToday && (activeFilter === 'live' || activeFilter === 'finished' || activeFilter === 'upcoming')) {
      setActiveFilter('all');
    }
  }, [selectedDate, isSelectedDateToday, activeFilter]);

  // Note: Removed auto-switch for "By Time" + filtered tabs
  // All combinations (Live + By Time, Finished + By Time, Upcoming + By Time) are valid
  // Users should be able to see filtered matches grouped by time

  // Debug logging for filter changes
  useEffect(() => {
    console.log('🔄 Filter changed:', {
      activeFilter,
      selectedDate,
      isSelectedDateToday,
      timestamp: new Date().toLocaleTimeString()
    });
  }, [activeFilter, selectedDate, isSelectedDateToday]);

  // Optimized data fetching to prevent duplicate socket subscriptions
  // Only enable the query that's actually needed based on the active filter
  const isLiveFilter = activeFilter === 'live';
  const liveMatchesQuery = useLiveMatches(isLiveFilter);
  const dateOnlyQuery = useMatchesByDateOnly(selectedDate, !isLiveFilter);

  // Determine which data to show based on active filter
  const matchesQuery = isLiveFilter ? liveMatchesQuery : dateOnlyQuery;
  const fixtures = matchesQuery.data || [];

  // Filter fixtures based on search query and active filter
  const filteredFixtures = fixtures.filter((fixture) => {
    // Apply status-based filtering first (skip for 'all' filter)
    if (activeFilter === 'finished' && !isFinishedMatch(fixture.fixture.status.short)) {
      return false;
    }

    if (activeFilter === 'upcoming' && !isUpcomingMatch(fixture.fixture.status.short)) {
      return false;
    }

    // Then apply search query filtering
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      fixture.teams.home.name.toLowerCase().includes(query) ||
      fixture.teams.away.name.toLowerCase().includes(query) ||
      fixture.league.name.toLowerCase().includes(query)
    );
  });

  // Sort fixtures by league tier (Tier 1 = highest priority, Tier 6 = lowest priority)
  const sortedFixtures = sortMatchesByLeagueTier(filteredFixtures);

  // Group fixtures by date and sort by time when "By Time" is enabled
  const processedFixtures = byTimeEnabled
    ? groupFixturesByDateAndTime(sortedFixtures)
    : sortedFixtures;

  function groupFixturesByDateAndTime(fixtures: Fixture[]) {
    // Group fixtures by date
    const groupedByDate = fixtures.reduce((groups, fixture) => {
      const fixtureDate = new Date(fixture.fixture.date);
      const dateKey = fixtureDate.toDateString();

      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(fixture);
      return groups;
    }, {} as Record<string, Fixture[]>);

    // Sort fixtures within each date group by time
    Object.keys(groupedByDate).forEach(dateKey => {
      groupedByDate[dateKey].sort((a, b) => {
        return new Date(a.fixture.date).getTime() - new Date(b.fixture.date).getTime();
      });
    });

    // Convert back to flat array with date headers
    const result: Fixture[] = [];
    const sortedDates = Object.keys(groupedByDate).sort((a, b) => {
      return new Date(a).getTime() - new Date(b).getTime();
    });

    sortedDates.forEach((dateKey, index) => {
      // Add a special "date header" fixture
      const dateHeaderFixture = {
        ...groupedByDate[dateKey][0],
        _id: -1000 - index, // Use negative numbers to avoid conflicts with real fixture IDs
        isDateHeader: true,
        dateKey
      } as Fixture & { isDateHeader: boolean; dateKey: string };

      result.push(dateHeaderFixture);
      result.push(...groupedByDate[dateKey]);
    });

    return result;
  }



  const handleMatchClick = (fixture: Fixture) => {
    const matchUrl = generateMatchUrl(
      fixture.teams.home.name,
      fixture.teams.away.name,
      fixture.fixture.id
    );
    router.push(matchUrl);
  };

  const getEmptyMessage = () => {
    switch (activeFilter) {
      case 'all':
        return 'No matches found for today';
      case 'live':
        return 'No live matches at the moment';
      case 'finished':
        return 'No finished matches found';
      case 'upcoming':
        return 'No upcoming matches found';
      default:
        return 'No matches found';
    }
  };

  return (
    <MainLayout>
      <div className="grid grid-cols-1 lg:grid-cols-10 gap-2 sm:gap-4">
        {/* Main content */}
        <div className="lg:col-span-7 space-y-6">
            {/* Match filters */}
            <Card className="p-3.5">
              <MatchFilters
                activeFilter={activeFilter}
                onFilterChange={setActiveFilter}
                byTimeEnabled={byTimeEnabled}
                onByTimeToggle={setByTimeEnabled}
                selectedDate={selectedDate}
                onDateChange={setSelectedDate}
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
              />
            </Card>

            {/* Loading state */}
            {matchesQuery.isLoading && (
              <MatchListSkeleton groupByLeague={!byTimeEnabled} />
            )}

            {/* Error state */}
            {matchesQuery.isError && (
              <Card className="p-3.5">
                <div className="text-center py-12">
                  <div className="text-red-500 text-4xl mb-4">⚠️</div>
                  <p className="text-lg text-red-600">Failed to load matches</p>
                  <p className="text-sm text-muted-foreground mt-2">
                    Please check your connection and try again
                  </p>
                </div>
              </Card>
            )}

            {/* Match list */}
            {!matchesQuery.isLoading && !matchesQuery.isError && (
              <MatchList
                fixtures={processedFixtures}
                onMatchClick={handleMatchClick}
                groupByLeague={!byTimeEnabled}
                emptyMessage={getEmptyMessage()}
              />
            )}
          </div>

        {/* News sidebar */}
        <div className="lg:col-span-3">
          <NewsSection />
        </div>
      </div>
    </MainLayout>
  );
}
