import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/providers/ThemeProvider";
import { QueryProvider } from "@/providers/QueryProvider";
import { SocketProvider } from "@/providers/SocketProvider";
import { AuthProvider } from "@/contexts/AuthContext";
import { Toaster } from "sonner";

const roboto = Roboto({
  subsets: ["latin"],
  weight: ["300", "400", "500", "700"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "KickoffScore - Live Football Scores & Predictions",
  description: "Get real-time football scores, match updates, and predictions for all major leagues. Follow your favorite teams and never miss a goal.",
  keywords: ["football", "soccer", "live scores", "predictions", "matches", "leagues", "teams"],
  authors: [{ name: "KickoffScore Team" }],
  creator: "KickoffScore",
  publisher: "KickoffScore",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://kickoffpredictions.com"),
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://kickoffpredictions.com",
    title: "KickoffScore - Live Football Scores & Predictions",
    description: "Get real-time football scores, match updates, and predictions for all major leagues.",
    siteName: "KickoffScore",
  },
  twitter: {
    card: "summary_large_image",
    title: "KickoffScore - Live Football Scores & Predictions",
    description: "Get real-time football scores, match updates, and predictions for all major leagues.",
    creator: "@kickoffscore",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${roboto.variable} font-sans antialiased`} suppressHydrationWarning>
        <ThemeProvider>
          <QueryProvider>
            <AuthProvider>
              <SocketProvider>
                {children}
                <Toaster position="top-right" />
              </SocketProvider>
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
