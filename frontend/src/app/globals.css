
@import "tailwindcss";

:root {
  --background: rgba(250, 250, 250, 1);
  --foreground: rgb(10, 10, 10);
  --card: rgba(255, 255, 255, 1);
  --card-foreground: rgb(10, 10, 10);
  --popover: rgb(255, 255, 255);
  --popover-foreground: rgb(10, 10, 10);
  --primary: rgb(23, 23, 23);
  --primary-foreground: rgb(250, 250, 250);
  --secondary: rgb(245, 245, 245);
  --secondary-foreground: rgb(23, 23, 23);
  --muted: rgb(245, 245, 245);
  --muted-foreground: rgb(115, 115, 115);
  --accent: rgb(245, 245, 245);
  --accent-foreground: rgb(23, 23, 23);
  --destructive: rgb(231, 0, 11);
  --destructive-foreground: rgb(255, 255, 255);
  --border: 240 240 240;
  --input: rgb(229, 229, 229);
  --ring: rgb(161, 161, 161);
  --chart-1: rgb(145, 197, 255);
  --chart-2: rgb(58, 129, 246);
  --chart-3: rgb(37, 99, 239);
  --chart-4: rgb(26, 78, 218);
  --chart-5: rgb(31, 63, 173);
  --sidebar: rgb(250, 250, 250);
  --sidebar-foreground: rgb(10, 10, 10);
  --sidebar-primary: rgb(23, 23, 23);
  --sidebar-primary-foreground: rgb(250, 250, 250);
  --sidebar-accent: rgb(245, 245, 245);
  --sidebar-accent-foreground: rgb(23, 23, 23);
  --sidebar-border: rgb(229, 229, 229);
  --sidebar-ring: rgb(161, 161, 161);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
  --match-card-border: #F5F5F5;
  --form-draw: #e88c30;
  --form-win: #22c55e;
  --form-loss: #ef4444;
  --form-default: #6b7280;

  /* Status Colors */
  --status-live: #ef4444;           /* Red for live matches */
  --status-error: #ef4444;          /* Red for error messages */
  --status-success: #22c55e;        /* Green for success states */
  --status-warning: #eab308;        /* Yellow for warnings */
  --status-info: #3b82f6;           /* Blue for info states */

  /* Team Colors */
  --team-home-primary: #3399db;     /* Primary blue for home team */
  --team-home-secondary: #3b82f6;   /* Secondary blue for home team */
  --team-home-goalkeeper: #059669;  /* Green for home goalkeeper */
  --team-away-primary: #e84c3d;     /* Primary red for away team */
  --team-away-secondary: #ef4444;   /* Secondary red for away team */
  --team-away-goalkeeper: #dc2626;  /* Dark red for away goalkeeper */
  --team-neutral: #e88c30;          /* Gray for neutral/draw states */

  /* Competition Colors */
  --competition-champions-league: #00b04f;    /* UEFA Champions League blue */
  --competition-europa-league: #f47e01;       /* UEFA Europa League orange */
  --competition-conference-league: #003399;   /* UEFA Conference League green */
  --competition-relegation: #ef4444;          /* Relegation red */
  --competition-playoff: #eab308;             /* Playoff yellow */
  --competition-relegation-playoff: #f16a73;  /* Relegation Playoff pink */

  /* Performance Colors */
  --performance-excellent: #7bbe45;  /* Green for high performance (70%+) */
  --performance-good: #22c55e;       /* Light green for good performance */
  --performance-average: #eca803;    /* Orange for average performance (30-70%) */
  --performance-poor: #f44c36;       /* Red for poor performance (<30%) */

  /* UI Element Colors */
  --ui-icon-muted: #838893;         /* Muted gray for icons */
  --ui-text-white: #ffffff;         /* Pure white text */
  --ui-text-black: #000000;         /* Pure black text */

  /* Football Pitch Colors - Light Theme */
  --pitch-bg: rgba(12, 159, 103, 1);    /* Green pitch background for light theme */
  --pitch-lines: rgba(255, 255, 255); /* White lines for light theme */

  /* Active Team Row Colors - Light Theme */
  --active-team-row-bg: #f0f0f0;        /* Light gray for active team rows in light theme */
}

.dark {
  --background: rgba(18, 18, 18, 1);
  --foreground: rgb(250, 250, 250);
  --card: rgba(26, 26, 26, 1);
  --card-foreground: rgb(250, 250, 250);
  --popover: rgb(38, 38, 38);
  --popover-foreground: rgb(250, 250, 250);
  --primary: rgb(229, 229, 229);
  --primary-foreground: rgb(23, 23, 23);
  --secondary: rgb(38, 38, 38);
  --secondary-foreground: rgb(250, 250, 250);
  --muted: rgb(38, 38, 38);
  --muted-foreground: rgb(161, 161, 161);
  --accent: rgb(64, 64, 64);
  --accent-foreground: rgb(250, 250, 250);
  --destructive: rgb(255, 100, 103);
  --destructive-foreground: rgb(250, 250, 250);
  --border: 0 0 0 / 0;
  --input: rgb(52, 52, 52);
  --ring: rgb(115, 115, 115);
  --chart-1: rgb(145, 197, 255);
  --chart-2: rgb(58, 129, 246);
  --chart-3: rgb(37, 99, 239);
  --chart-4: rgb(26, 78, 218);
  --chart-5: rgb(31, 63, 173);
  --sidebar: rgb(23, 23, 23);
  --sidebar-foreground: rgb(250, 250, 250);
  --sidebar-primary: rgb(20, 71, 230);
  --sidebar-primary-foreground: rgb(250, 250, 250);
  --sidebar-accent: rgb(38, 38, 38);
  --sidebar-accent-foreground: rgb(250, 250, 250);
  --sidebar-border: rgb(40, 40, 40);
  --sidebar-ring: rgb(82, 82, 82);
  --match-card-border: #2A2A2A;
  --fixtures-dark-bg: #1a1a1a;
  --fixtures-card-bg: #262626;
  --fixtures-header-bg: #0f0f0f;
  --fixtures-text-primary: #ffffff;
  --fixtures-text-secondary: #a1a1a1;
  --fixtures-accent-green: #22c55e;
  --fixtures-accent-red: #ef4444;
  --fixtures-accent-yellow: #eab308;
  --fixtures-border: #404040;
  --fixtures-hover: #333333;
  --form-draw: #e88c30;
  --form-win: #22c55e;
  --form-loss: #ef4444;
  --form-default: #6b7280;

  /* Status Colors - Same as light theme for consistency */
  --status-live: #ef4444;           /* Red for live matches */
  --status-error: #ef4444;          /* Red for error messages */
  --status-success: #22c55e;        /* Green for success states */
  --status-warning: #eab308;        /* Yellow for warnings */
  --status-info: #3b82f6;           /* Blue for info states */

  /* Team Colors - Same as light theme for consistency */
  --team-home-primary: #3399db;     /* Primary blue for home team */
  --team-home-secondary: #3b82f6;   /* Secondary blue for home team */
  --team-home-goalkeeper: #059669;  /* Green for home goalkeeper */
  --team-away-primary: #e84c3d;     /* Primary red for away team */
  --team-away-secondary: #ef4444;   /* Secondary red for away team */
  --team-away-goalkeeper: #dc2626;  /* Dark red for away goalkeeper */
  --team-neutral: #e88c30;          /* Gray for neutral/draw states */

  /* Competition Colors - Same as light theme for consistency */
  --competition-champions-league: #00b04f;     /* UEFA Champions League green*/
  --competition-europa-league: #f47e01;       /* UEFA Europa League orange */
  --competition-conference-league: #003399;   /* UEFA Conference League blue */
  --competition-relegation: #ef4444;          /* Relegation red */
  --competition-playoff: #eab308;             /* Playoff yellow */
  --competition-relegation-playoff: #f16a73;  /* Relegation Playoff pink */

  /* Performance Colors - Same as light theme for consistency */
  --performance-excellent: #7bbe45;  /* Green for high performance (70%+) */
  --performance-good: #22c55e;       /* Light green for good performance */
  --performance-average: #eca803;    /* Orange for average performance (30-70%) */
  --performance-poor: #f44c36;       /* Red for poor performance (<30%) */

  /* UI Element Colors */
  --ui-icon-muted: #838893;         /* Muted gray for icons */
  --ui-text-white: #ffffff;         /* Pure white text */
  --ui-text-black: #000000;         /* Pure black text */

  /* Football Pitch Colors - Dark Theme */
  --pitch-bg: rgba(44, 44, 44, 1);      /* Dark gray pitch background for dark theme */
  --pitch-lines: rgba(52, 52, 52, 1);   /* Darker gray lines for dark theme */

  /* Active Team Row Colors - Dark Theme */
  --active-team-row-bg: #0a0a0a;        /* Very dark gray for active team rows in dark theme */

  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.625rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-fixtures-dark-bg: var(--fixtures-dark-bg);
  --color-fixtures-card-bg: var(--fixtures-card-bg);
  --color-fixtures-header-bg: var(--fixtures-header-bg);
  --color-fixtures-text-primary: var(--fixtures-text-primary);
  --color-fixtures-text-secondary: var(--fixtures-text-secondary);
  --color-fixtures-accent-green: var(--fixtures-accent-green);
  --color-fixtures-accent-red: var(--fixtures-accent-red);
  --color-fixtures-accent-yellow: var(--fixtures-accent-yellow);
  --color-fixtures-border: var(--fixtures-border);
  --color-fixtures-hover: var(--fixtures-hover);
  --color-active-team-row-bg: var(--active-team-row-bg);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer utilities {
  .border-custom-gray {
    border-color: #f5f5f5;
  }
  .dark .border-custom-gray {
    border-color: #2a2a2a;
  }
  .divide-custom-gray > :not([hidden]) ~ :not([hidden]) {
    border-color: #f5f5f5;
  }
  .dark .divide-custom-gray > :not([hidden]) ~ :not([hidden]) {
    border-color: #2a2a2a;
  }

  /* Status Color Utilities */
  .text-status-live { color: var(--status-live); }
  .text-status-error { color: var(--status-error); }
  .text-status-success { color: var(--status-success); }
  .text-status-warning { color: var(--status-warning); }
  .text-status-info { color: var(--status-info); }

  /* Team Color Utilities */
  .bg-team-home { background-color: var(--team-home-primary); }
  .bg-team-away { background-color: var(--team-away-primary); }
  .bg-team-neutral { background-color: var(--team-neutral); }
  .text-team-home { color: var(--team-home-primary); }
  .text-team-away { color: var(--team-away-primary); }
  .text-team-neutral { color: var(--team-neutral); }

  /* Competition Color Utilities */
  .border-competition-cl { border-color: var(--competition-champions-league); }
  .border-competition-el { border-color: var(--competition-europa-league); }
  .border-competition-ecl { border-color: var(--competition-conference-league); }
  .border-competition-relegation { border-color: var(--competition-relegation); }
  .border-competition-playoff { border-color: var(--competition-playoff); }
  .border-competition-relegation-playoff { border-color: var(--competition-relegation-playoff); }

  /* Performance Color Utilities */
  .bg-performance-excellent { background-color: var(--performance-excellent); }
  .bg-performance-good { background-color: var(--performance-good); }
  .bg-performance-average { background-color: var(--performance-average); }
  .bg-performance-poor { background-color: var(--performance-poor); }

  /* Active Team Row Utilities */
  .bg-active-team-row { background-color: var(--active-team-row-bg); }
}
