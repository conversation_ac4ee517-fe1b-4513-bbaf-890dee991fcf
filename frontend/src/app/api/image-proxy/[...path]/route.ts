import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';
import crypto from 'crypto';

// Cache directory for images
const CACHE_DIR = path.join(process.cwd(), '.next/cache/images');
const CACHE_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

interface CacheMetadata {
  originalUrl: string;
  cachedAt: number;
  contentType: string;
  size: number;
}

// Ensure cache directory exists
async function ensureCacheDir() {
  try {
    await fs.access(CACHE_DIR);
  } catch {
    await fs.mkdir(CACHE_DIR, { recursive: true });
  }
}

// Generate cache key from URL
function getCacheKey(url: string): string {
  return crypto.createHash('md5').update(url).digest('hex');
}

// Check if cached image exists and is valid
async function getCachedImage(cacheKey: string): Promise<{ buffer: Buffer; metadata: CacheMetadata } | null> {
  try {
    const metaPath = path.join(CACHE_DIR, `${cacheKey}.meta`);
    const imagePath = path.join(CACHE_DIR, `${cacheKey}.img`);

    // Check if both files exist
    await Promise.all([
      fs.access(metaPath),
      fs.access(imagePath)
    ]);

    // Read metadata
    const metaContent = await fs.readFile(metaPath, 'utf-8');
    const metadata: CacheMetadata = JSON.parse(metaContent);

    // Check if cache is still valid
    const now = Date.now();
    if ((now - metadata.cachedAt) > CACHE_DURATION) {
      // Cache expired, delete files
      await Promise.all([
        fs.unlink(metaPath).catch(() => {}),
        fs.unlink(imagePath).catch(() => {})
      ]);
      return null;
    }

    // Read image buffer
    const buffer = await fs.readFile(imagePath);
    return { buffer, metadata };
  } catch {
    return null;
  }
}

// Cache image to disk
async function cacheImage(cacheKey: string, buffer: Buffer, metadata: CacheMetadata): Promise<void> {
  try {
    const metaPath = path.join(CACHE_DIR, `${cacheKey}.meta`);
    const imagePath = path.join(CACHE_DIR, `${cacheKey}.img`);

    await Promise.all([
      fs.writeFile(metaPath, JSON.stringify(metadata)),
      fs.writeFile(imagePath, buffer)
    ]);

    console.log(`💾 Cached image: ${metadata.originalUrl} (${metadata.size} bytes)`);
  } catch (error) {
    console.error('Failed to cache image:', error);
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // Ensure cache directory exists
    await ensureCacheDir();

    // Await params in Next.js 15
    const resolvedParams = await params;

    // Reconstruct the original API-Sports URL
    const imagePath = resolvedParams.path.join('/');
    const originalUrl = `https://media.api-sports.io/${imagePath}`;
    const cacheKey = getCacheKey(originalUrl);

    console.log(`🖼️ Image proxy request: ${originalUrl}`);

    // Check cache first
    const cached = await getCachedImage(cacheKey);
    if (cached) {
      console.log(`✅ Serving cached image: ${originalUrl}`);
      return new NextResponse(cached.buffer, {
        headers: {
          'Content-Type': cached.metadata.contentType,
          'Cache-Control': 'public, max-age=604800', // 7 days
          'X-Cache-Status': 'HIT'
        }
      });
    }

    // Fetch from API-Sports
    console.log(`📡 Fetching from API-Sports: ${originalUrl}`);
    const response = await fetch(originalUrl, {
      headers: {
        'User-Agent': 'KickoffScore/1.0'
      }
    });

    if (!response.ok) {
      console.error(`❌ API-Sports request failed: ${response.status} ${response.statusText}`);
      
      // Return a fallback image or error response
      return new NextResponse('Image not found', { 
        status: 404,
        headers: {
          'X-Cache-Status': 'MISS'
        }
      });
    }

    const buffer = Buffer.from(await response.arrayBuffer());
    const contentType = response.headers.get('content-type') || 'image/png';

    // Cache the image
    const metadata: CacheMetadata = {
      originalUrl,
      cachedAt: Date.now(),
      contentType,
      size: buffer.length
    };

    // Cache asynchronously (don't wait)
    cacheImage(cacheKey, buffer, metadata).catch(console.error);

    console.log(`✅ Served fresh image: ${originalUrl} (${buffer.length} bytes)`);

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=604800', // 7 days
        'X-Cache-Status': 'MISS'
      }
    });

  } catch (error) {
    console.error('Image proxy error:', error);
    return new NextResponse('Internal Server Error', { 
      status: 500,
      headers: {
        'X-Cache-Status': 'ERROR'
      }
    });
  }
}
