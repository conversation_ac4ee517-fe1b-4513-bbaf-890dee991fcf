'use client';

import { useState } from 'react';
import { Header } from '@/components/fixtures/Header';
import { FeaturedMatch } from '@/components/fixtures/FeaturedMatch';
import { TeamForm } from '@/components/fixtures/TeamForm';
import { BettingOdds } from '@/components/fixtures/BettingOdds';
import { LeagueStandings } from '@/components/fixtures/LeagueStandings';
import { UpcomingMatches } from '@/components/fixtures/UpcomingMatches';
import { mockQuery } from '@/lib/fixturesMockData';

export default function FixturesPage() {
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="min-h-screen bg-fixtures-dark-bg">
      <div className="container mx-auto px-4 py-6 space-y-6">
        {/* Header */}
        <Header 
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
        />

        {/* Main content grid */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left column - Main content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Featured Match */}
            <FeaturedMatch match={mockQuery.featuredMatch} />
            
            {/* Team Form */}
            <TeamForm formData={mockQuery.teamForm} />
          </div>

          {/* Right column - Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Betting Odds */}
            <BettingOdds odds={mockQuery.bettingOdds} />
            
            {/* League Standings */}
            <LeagueStandings 
              standings={mockQuery.leagueStandings}
              leagueName="Premier League"
              round="Round 1"
            />
            
            {/* Upcoming Matches */}
            <UpcomingMatches matches={mockQuery.upcomingMatches} />
          </div>
        </div>
      </div>
    </div>
  );
}