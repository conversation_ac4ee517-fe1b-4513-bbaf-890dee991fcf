import { Metadata } from 'next';
import { TipsterRankingPage } from '@/components/pages/TipsterRankingPage';

export const metadata: Metadata = {
  title: 'Tipster Rankings - KickoffScore',
  description: 'View the top tipsters on KickoffScore. See rankings based on profit, yield, hit rate, and more.',
  keywords: 'tipster rankings, football tips, betting tips, profit, yield, hit rate',
  openGraph: {
    title: 'Tipster Rankings - KickoffScore',
    description: 'View the top tipsters on KickoffScore. See rankings based on profit, yield, hit rate, and more.',
    type: 'website',
  },
};

export default function TipsterRanking() {
  return <TipsterRankingPage />;
}
