import { Metadata } from 'next';
import { TipsterProfilePage } from '@/components/pages/TipsterProfilePage';

interface Props {
  params: Promise<{
    username: string;
  }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { username } = await params;

  return {
    title: `${username} - Tipster Profile - KickoffScore`,
    description: `View ${username}'s tipster profile, statistics, and recent tips on KickoffScore.`,
    keywords: `${username}, tipster profile, football tips, betting tips, statistics`,
    openGraph: {
      title: `${username} - Tipster Profile - KickoffScore`,
      description: `View ${username}'s tipster profile, statistics, and recent tips on KickoffScore.`,
      type: 'profile',
    },
  };
}

export default async function TipsterProfile({ params }: Props) {
  const { username } = await params;
  return <TipsterProfilePage username={username} />;
}
