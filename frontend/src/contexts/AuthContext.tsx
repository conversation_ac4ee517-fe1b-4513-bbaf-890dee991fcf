'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, AuthContextType, LoginCredentials, SignupCredentials, OAuthCredentials } from '@/types/auth';
import { authAPI, tokenStorage, userStorage } from '@/lib/auth';
import { toast } from 'sonner';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user && !!token;

  // Initialize auth state from storage
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const storedToken = tokenStorage.get();
        const storedUser = userStorage.get();

        if (storedToken && storedUser) {
          // Verify token is still valid by fetching profile
          try {
            const profile = await authAPI.getProfile(storedToken);
            setToken(storedToken);
            setUser(profile);
          } catch {
            // Token is invalid, clear storage
            tokenStorage.remove();
            console.log('Stored token is invalid, cleared from storage');
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await authAPI.login(credentials);
      
      setToken(response.token);
      setUser(response.user);
      
      tokenStorage.set(response.token);
      userStorage.set(response.user);
      
      toast.success('Successfully logged in!');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (credentials: SignupCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await authAPI.signup(credentials);
      
      setToken(response.token);
      setUser(response.user);
      
      tokenStorage.set(response.token);
      userStorage.set(response.user);
      
      toast.success('Account created successfully!');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Signup failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithOAuth = async (credentials: OAuthCredentials): Promise<void> => {
    try {
      setIsLoading(true);
      const response = await authAPI.loginWithOAuth(credentials);
      
      setToken(response.token);
      setUser(response.user);
      
      tokenStorage.set(response.token);
      userStorage.set(response.user);
      
      toast.success('Successfully logged in with Google!');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'OAuth login failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = (): void => {
    setUser(null);
    setToken(null);
    tokenStorage.remove();
    toast.success('Successfully logged out');
  };

  const updateProfile = async (updates: Partial<User>): Promise<void> => {
    if (!token) throw new Error('Not authenticated');

    try {
      const response = await authAPI.updateProfile(token, updates);
      // Merge updates with current user data
      const updatedUser = { ...user, ...updates, ...response };
      setUser(updatedUser);
      userStorage.set(updatedUser);
      toast.success('Profile updated successfully!');
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update profile';
      toast.error(message);
      throw error;
    }
  };

  const getAuthHeaders = (): Record<string, string> => {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-API-Key': process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756',
    };

    if (token) {
      headers['x-auth-token'] = token;
    }

    return headers;
  };

  const value: AuthContextType = {
    user,
    token,
    isLoading,
    isAuthenticated,
    login,
    signup,
    loginWithOAuth,
    logout,
    updateProfile,
    getAuthHeaders,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
