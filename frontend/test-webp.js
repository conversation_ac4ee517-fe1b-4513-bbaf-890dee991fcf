#!/usr/bin/env node

const fetch = require('node-fetch').default || require('node-fetch');

async function testWebPConversion() {
  console.log('🧪 Testing WebP conversion...\n');
  
  try {
    // Test a team logo conversion
    const testUrl = 'http://localhost:3000/api/image-proxy/football/teams/40.png';
    
    console.log(`📡 Requesting: ${testUrl}`);
    const response = await fetch(testUrl);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    // Check headers
    const contentType = response.headers.get('content-type');
    const cacheStatus = response.headers.get('x-cache');
    const imageFormat = response.headers.get('x-image-format');
    const originalFormat = response.headers.get('x-original-format');
    const compressed = response.headers.get('x-compressed');
    const contentLength = response.headers.get('content-length');
    
    console.log('\n📊 Response Headers:');
    console.log(`Content-Type: ${contentType}`);
    console.log(`Content-Length: ${contentLength} bytes`);
    console.log(`X-Cache: ${cacheStatus}`);
    console.log(`X-Image-Format: ${imageFormat}`);
    console.log(`X-Original-Format: ${originalFormat}`);
    console.log(`X-Compressed: ${compressed}`);
    
    // Verify it's WebP
    if (contentType === 'image/webp') {
      console.log('\n✅ SUCCESS: Image is being served as WebP!');
      if (compressed === 'true') {
        console.log('✅ SUCCESS: Image was successfully compressed!');
      }
    } else {
      console.log(`\n❌ ISSUE: Expected image/webp but got ${contentType}`);
    }
    
  } catch (error) {
    console.error(`\n❌ ERROR: ${error.message}`);
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 TIP: Make sure the Next.js dev server is running on port 3000');
    }
  }
}

// Run the test
testWebPConversion();
