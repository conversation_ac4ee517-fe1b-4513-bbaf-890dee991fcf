# KickoffScore Frontend

A modern, responsive football live score website built with Next.js 14, TypeScript, and Tailwind CSS. Features real-time match updates, dark/light mode, and a clean FOTMOB-inspired design.

## 🚀 Features

- **Real-time Live Scores**: WebSocket integration for instant match updates
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Modern UI**: Clean, professional design using Shadcn/UI components
- **SEO Optimized**: Server-side rendering with proper metadata
- **Type Safety**: Full TypeScript implementation with strict mode
- **Performance**: Optimized with React Query caching and Next.js features

## 🏗️ Architecture

### Tech Stack
- **Framework**: Next.js 14 (App Router)
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS + Shadcn/UI
- **State Management**: TanStack React Query v5
- **Real-time**: Socket.io-client
- **Theme**: next-themes
- **Icons**: Lucide React

### Project Structure
```
src/
├── app/                     # Next.js 14 App Router
│   ├── layout.tsx          # Root layout with providers
│   ├── page.tsx            # Homepage
│   └── globals.css         # Global styles
├── components/
│   ├── ui/                 # Shadcn/UI components
│   ├── layout/             # Layout components
│   │   ├── Header.tsx      # Main header with search & theme toggle
│   │   ├── Sidebar.tsx     # League navigation sidebar
│   │   ├── Footer.tsx      # Footer with links & partners
│   │   └── MainLayout.tsx  # Main layout wrapper
│   └── features/           # Feature-specific components
│       ├── MatchCard.tsx   # Individual match display
│       ├── MatchList.tsx   # Match listing with grouping
│       ├── MatchFilters.tsx # Filter tabs & date selector
│       └── NewsSection.tsx # News sidebar
├── lib/
│   ├── types.ts           # TypeScript definitions
│   ├── api.ts             # API client & functions
│   ├── socket.ts          # Socket.io client setup
│   ├── constants.ts       # App constants & configuration
│   └── utils.ts           # Utility functions
├── hooks/                 # Custom React hooks
│   ├── useMatches.ts      # Match data hooks
│   └── useLeagues.ts      # League data hooks
└── providers/             # Context providers
    ├── QueryProvider.tsx  # React Query setup
    ├── ThemeProvider.tsx  # Theme management
    └── SocketProvider.tsx # Socket connection
```

## 🔧 Setup & Installation

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Environment Variables
Create a `.env.local` file in the frontend directory:
```env
NEXT_PUBLIC_API_URL=http://api.kickoffpredictions.com
NEXT_PUBLIC_API_KEY=1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756
```

## 🎨 Design System

### Color Scheme
- **Primary**: Football-themed blue/green
- **Background**: Dynamic (light/dark mode)
- **Accent**: Red for live matches, green for goals
- **Text**: High contrast for accessibility

### Components
- **MatchCard**: Displays team names, scores, status, and events
- **MatchFilters**: Live/Finished/Upcoming tabs with date picker
- **Sidebar**: Top leagues navigation with icons
- **Header**: Search, settings, and theme toggle
- **NewsSection**: Latest football news and transfer rumors

## 🔄 Real-time Features

### Socket.io Integration
- **Live Matches**: Automatic updates every 15 seconds
- **Goal Notifications**: Instant goal alerts
- **Status Changes**: Match start/end notifications
- **Red Cards**: Important event updates

### Data Caching
- **React Query**: Smart caching with background updates
- **Cache Times**: 15s for live data, 1h for static data
- **Optimistic Updates**: Instant UI updates from socket events

## 📱 Responsive Design

### Breakpoints
- **Mobile**: < 768px (collapsible sidebar)
- **Tablet**: 768px - 1024px (adjusted layout)
- **Desktop**: > 1024px (full sidebar + news)

### Mobile Features
- Hamburger menu for sidebar
- Touch-friendly match cards
- Optimized search interface
- Swipe gestures (future enhancement)

## 🚀 Performance

### Optimization Features
- **Server-side Rendering**: Fast initial page loads
- **Image Optimization**: Next.js Image component
- **Font Optimization**: Self-hosted fonts
- **Code Splitting**: Automatic route-based splitting
- **Caching**: Aggressive caching for static content

## 🧪 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

### API Integration
All API calls go through the centralized `api.ts` client:
```typescript
import { api } from '@/lib/api';

// Get live matches
const liveMatches = await api.getLiveFixtures();

// Get matches by date
const todayMatches = await api.getFixturesByDate('2024-01-15');
```

## 🔮 Future Enhancements

### Planned Features
- [ ] User authentication & favorites
- [ ] Match predictions & betting odds
- [ ] Player statistics & profiles
- [ ] League tables & standings
- [ ] Push notifications
- [ ] PWA support
- [ ] Multi-language support
- [ ] Advanced search & filters

### Technical Improvements
- [ ] Unit & integration tests
- [ ] E2E testing with Playwright
- [ ] Performance monitoring
- [ ] Error boundary implementation
- [ ] Accessibility improvements
- [ ] SEO enhancements

---

Built with ❤️ for football fans worldwide ⚽
